package area

import (
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	GetAll() ([]entities.Area, error)
	GetByID(id uuid.UUID) (*entities.Area, error)
	Create(area *entities.Area) error
	Update(area *entities.Area) error
	Delete(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetAll() ([]entities.Area, error) {
	var areas []entities.Area
    err := r.db.Order("name ASC, created_at DESC").Find(&areas).Error
	return areas, err
}

func (r *repository) GetByID(id uuid.UUID) (*entities.Area, error) {
	var area entities.Area
	err := r.db.First(&area, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &area, nil
}

func (r *repository) Create(area *entities.Area) error {
	return r.db.Create(area).Error
}

func (r *repository) Update(area *entities.Area) error {
	return r.db.Save(area).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Area{}, "id = ?", id).Error
}
