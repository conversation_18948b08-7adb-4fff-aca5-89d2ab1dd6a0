package auth

import (
	"errors"

	"github.com/nocytech/kpss-plus-admin/pkg/config"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"github.com/nocytech/kpss-plus-admin/pkg/utils"
)

type LoginResponse struct {
	Token string   `json:"token"`
	User  UserInfo `json:"user"`
}

type UserInfo struct {
	Username string `json:"username"`
	Role     string `json:"role"`
}

type Service interface {
	Login(username, password string) (*LoginResponse, error)
	ListAdmins() ([]entities.AdminUser, error)
	CreateAdmin(username, password, role string) (*entities.AdminUser, error)
	UpdateAdmin(id string, username, password, role *string) error
	DeleteAdmin(id string) error
}

type service struct {
	repo Repository
	app  config.App
}

func NewService(r Repository, appc config.App) Service {
	return &service{repo: r, app: appc}
}

func (s *service) Login(username, password string) (*LoginResponse, error) {
    // Bootstrap fallback with config admin credentials
    if username == s.app.AdminUser && password == s.app.AdminPass {
        token, err := utils.GenerateToken(s.app.JwtSecret, s.app.JwtIssuer, username, "admin", "", 24*7)
        if err != nil {
            return nil, err
        }
        return &LoginResponse{
            Token: token,
            User: UserInfo{
                Username: username,
                Role:     "admin",
            },
        }, nil
    }
	// DB based admin user
	u, err := s.repo.GetByUsername(username)
	if err != nil {
		return nil, errors.New("invalid credentials")
	}
	if !utils.CheckPassword(u.PasswordHash, password) {
		return nil, errors.New("invalid credentials")
	}
	token, err := utils.GenerateToken(s.app.JwtSecret, s.app.JwtIssuer, u.Username, u.Role, u.ID.String(), 24*7)
	if err != nil {
		return nil, err
	}
	return &LoginResponse{
		Token: token,
		User: UserInfo{
			Username: u.Username,
			Role:     u.Role,
		},
	}, nil
}

func (s *service) ListAdmins() ([]entities.AdminUser, error) {
	return s.repo.ListAdmins()
}

func (s *service) CreateAdmin(username, password, role string) (*entities.AdminUser, error) {
	hash, err := utils.HashPassword(password)
	if err != nil {
		return nil, err
	}
	u := &entities.AdminUser{Username: username, PasswordHash: hash, Role: role}
	if err := s.repo.CreateAdmin(u); err != nil {
		return nil, err
	}
	return u, nil
}

func (s *service) UpdateAdmin(id string, username, password, role *string) error {
	updates := map[string]any{}
	if username != nil {
		updates["username"] = *username
	}
	if role != nil {
		updates["role"] = *role
	}
	if password != nil && *password != "" {
		if h, err := utils.HashPassword(*password); err == nil {
			updates["password_hash"] = h
		} else {
			return err
		}
	}
	if len(updates) == 0 {
		return nil
	}
	return s.repo.UpdateAdmin(id, updates)
}

func (s *service) DeleteAdmin(id string) error {
	return s.repo.DeleteAdmin(id)
}
