package dtos

import (
	"time"

	"github.com/google/uuid"
)

// Progress Statistics DTOs
type ProgressStatsResponse struct {
	UserID uuid.UUID `json:"user_id"`

	// Overall progress statistics
	TotalContent      int     `json:"total_content"`
	CompletedContent  int     `json:"completed_content"`
	InProgressContent int     `json:"in_progress_content"`
	CompletionRate    float64 `json:"completion_rate"`

	// Study time statistics
	TotalStudyTime     int `json:"total_study_time"`     // in seconds
	WeeklyStudyTime    int `json:"weekly_study_time"`    // in seconds
	MonthlyStudyTime   int `json:"monthly_study_time"`   // in seconds
	AverageSessionTime int `json:"average_session_time"` // in seconds

	// Streak statistics
	CurrentStreak int        `json:"current_streak"` // days
	LongestStreak int        `json:"longest_streak"` // days
	LastStudyDate *time.Time `json:"last_study_date"`

	// Content type breakdown
	BookProgress     ProgressBreakdown `json:"book_progress"`
	VideoProgress    ProgressBreakdown `json:"video_progress"`
	QuestionProgress ProgressBreakdown `json:"question_progress"`

	// Subject breakdown
	SubjectProgress []SubjectProgressInfo `json:"subject_progress"`

	// Recent activity
	RecentSessions []RecentSessionInfo  `json:"recent_sessions"`
	RecentProgress []RecentProgressInfo `json:"recent_progress"`
}

type ProgressBreakdown struct {
	Total     int     `json:"total"`
	Completed int     `json:"completed"`
	Rate      float64 `json:"rate"`
}

type SubjectProgressInfo struct {
	Subject   string  `json:"subject"`
	Total     int     `json:"total"`
	Completed int     `json:"completed"`
	Rate      float64 `json:"rate"`
}

type RecentSessionInfo struct {
	ID        uuid.UUID `json:"id"`
	ContentID uuid.UUID `json:"content_id"`
	Title     string    `json:"title"`
	Type      string    `json:"type"`
	Duration  int       `json:"duration"`
	Date      time.Time `json:"date"`
}

type RecentProgressInfo struct {
	ID          uuid.UUID `json:"id"`
	ContentID   uuid.UUID `json:"content_id"`
	Title       string    `json:"title"`
	Type        string    `json:"type"`
	Percentage  float64   `json:"percentage"`
	IsCompleted bool      `json:"is_completed"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Progress History DTOs
type GetProgressHistoryRequest struct {
	Page        int        `json:"page" validate:"omitempty,min=1"`
	Limit       int        `json:"limit" validate:"omitempty,min=1,max=100"`
	StartDate   *time.Time `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
	ContentType *string    `json:"content_type" validate:"omitempty,oneof=book video playlist question pdf"`
	Subject     *string    `json:"subject"`
}

type ProgressHistoryResponse struct {
	History    []ProgressHistoryEntry `json:"history"`
	Total      int                    `json:"total"`
	Page       int                    `json:"page"`
	Limit      int                    `json:"limit"`
	TotalPages int                    `json:"total_pages"`
}

type ProgressHistoryEntry struct {
	ID            uuid.UUID `json:"id"`
	ContentID     uuid.UUID `json:"content_id"`
	ContentTitle  string    `json:"content_title"`
	ContentType   string    `json:"content_type"`
	Subject       *string   `json:"subject"`
	OldPercentage float64   `json:"old_percentage"`
	NewPercentage float64   `json:"new_percentage"`
	ProgressDelta float64   `json:"progress_delta"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// Study Streak DTOs
type StudyStreakResponse struct {
	UserID        uuid.UUID            `json:"user_id"`
	CurrentStreak int                  `json:"current_streak"`
	LongestStreak int                  `json:"longest_streak"`
	LastStudyDate *time.Time           `json:"last_study_date"`
	StreakHistory []StreakHistoryEntry `json:"streak_history"`
}

type StreakHistoryEntry struct {
	Date         time.Time `json:"date"`
	StudyTime    int       `json:"study_time"` // in seconds
	SessionCount int       `json:"session_count"`
	HasStudied   bool      `json:"has_studied"`
}

// Study Session DTOs
type StartStudySessionRequest struct {
	ContentID   *uuid.UUID `json:"content_id" validate:"omitempty"`
	QuizID      *uuid.UUID `json:"quiz_id" validate:"omitempty"`
	SessionType string     `json:"session_type" validate:"required,oneof=reading video quiz practice"`
	DeviceType  *string    `json:"device_type" validate:"omitempty,oneof=mobile tablet desktop"`
	Platform    *string    `json:"platform" validate:"omitempty,oneof=ios android web"`
}

type StudySessionResponse struct {
	ID          uuid.UUID  `json:"id"`
	UserID      *uuid.UUID `json:"user_id"`
	ContentID   *uuid.UUID `json:"content_id"`
	QuizID      *uuid.UUID `json:"quiz_id"`
	SessionType string     `json:"session_type"`
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`
	Duration    *int       `json:"duration"` // in seconds

	// Progress tracking
	ProgressBefore float64 `json:"progress_before"`
	ProgressAfter  float64 `json:"progress_after"`

	// Device info
	DeviceType string `json:"device_type"`
	Platform   string `json:"platform"`

	// Content info (if applicable)
	Content *ContentResponse `json:"content"`
	Quiz    *QuizInfo        `json:"quiz"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type GetStudySessionsRequest struct {
	Page        int        `json:"page" validate:"omitempty,min=1"`
	Limit       int        `json:"limit" validate:"omitempty,min=1,max=100"`
	StartDate   *time.Time `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
	ContentType *string    `json:"content_type" validate:"omitempty,oneof=book video playlist question pdf"`
	SessionType *string    `json:"session_type" validate:"omitempty,oneof=reading video quiz practice"`
	IsCompleted *bool      `json:"is_completed"`
}

type StudySessionsResponse struct {
	Sessions   []StudySessionResponse `json:"sessions"`
	Total      int                    `json:"total"`
	Page       int                    `json:"page"`
	Limit      int                    `json:"limit"`
	TotalPages int                    `json:"total_pages"`

	// Summary statistics
	TotalStudyTime     int `json:"total_study_time"`
	AverageSessionTime int `json:"average_session_time"`
	CompletedSessions  int `json:"completed_sessions"`
	OngoingSessions    int `json:"ongoing_sessions"`
}

// Progress Goals DTOs
type SetProgressGoalRequest struct {
	Type        string     `json:"type" validate:"required,oneof=daily weekly monthly content_completion study_time"`
	Title       string     `json:"title" validate:"required,min=1,max=200"`
	Description *string    `json:"description" validate:"omitempty,max=500"`
	TargetValue int        `json:"target_value" validate:"required,min=1"`
	Unit        string     `json:"unit" validate:"required,oneof=minutes hours pages questions content"`
	StartDate   time.Time  `json:"start_date" validate:"required"`
	EndDate     time.Time  `json:"end_date" validate:"required"`
	ContentID   *uuid.UUID `json:"content_id" validate:"omitempty"`
	Subject     *string    `json:"subject" validate:"omitempty"`
}

type UpdateProgressGoalRequest struct {
	Title       *string    `json:"title" validate:"omitempty,min=1,max=200"`
	Description *string    `json:"description" validate:"omitempty,max=500"`
	TargetValue *int       `json:"target_value" validate:"omitempty,min=1"`
	EndDate     *time.Time `json:"end_date" validate:"omitempty"`
	IsActive    *bool      `json:"is_active"`
}

type ProgressGoalResponse struct {
	ID           uuid.UUID  `json:"id"`
	UserID       uuid.UUID  `json:"user_id"`
	Type         string     `json:"type"`
	Title        string     `json:"title"`
	Description  *string    `json:"description"`
	TargetValue  int        `json:"target_value"`
	CurrentValue int        `json:"current_value"`
	Unit         string     `json:"unit"`
	Progress     float64    `json:"progress"`
	IsCompleted  bool       `json:"is_completed"`
	IsActive     bool       `json:"is_active"`
	StartDate    time.Time  `json:"start_date"`
	EndDate      time.Time  `json:"end_date"`
	ContentID    *uuid.UUID `json:"content_id"`
	Subject      *string    `json:"subject"`

	// Content info (if applicable)
	Content *ContentResponse `json:"content"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type ProgressGoalsResponse struct {
	Goals     []ProgressGoalResponse `json:"goals"`
	Total     int                    `json:"total"`
	Active    int                    `json:"active"`
	Completed int                    `json:"completed"`
	Overdue   int                    `json:"overdue"`
}

// Achievement DTOs
type AchievementResponse struct {
	ID          uuid.UUID `json:"id"`
	BadgeID     uuid.UUID `json:"badge_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Type        string    `json:"type"`
	IconURL     string    `json:"icon_url"`
	Color       string    `json:"color"`
	Rarity      string    `json:"rarity"`
	Points      int       `json:"points"`
	EarnedAt    time.Time `json:"earned_at"`
	IsNew       bool      `json:"is_new"`
}

type AchievementsResponse struct {
	Achievements []AchievementResponse `json:"achievements"`
	Total        int                   `json:"total"`
	TotalPoints  int                   `json:"total_points"`

	// Achievement breakdown by type
	StudyAchievements    int `json:"study_achievements"`
	ProgressAchievements int `json:"progress_achievements"`
	SocialAchievements   int `json:"social_achievements"`
	QuizAchievements     int `json:"quiz_achievements"`
}

// Placeholder for QuizInfo (will be defined in quiz DTOs)
type QuizInfo struct {
	ID    uuid.UUID `json:"id"`
	Title string    `json:"title"`
	// Add other quiz fields as needed
}
