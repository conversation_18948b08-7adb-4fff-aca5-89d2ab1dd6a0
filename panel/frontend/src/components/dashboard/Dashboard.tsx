import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BookOpen,
  TrendingUp,
  Trophy,
  Target,
  Clock,
  Zap,
  ArrowUpRight,
  Calendar,
  BarChart3,
  Pie<PERSON><PERSON>
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { analyticsService } from '../../services/analyticsService';
import { useToast } from '../common/Toast';

interface DashboardStats {
  totalQuizzes: number;
  averageScore: number;
  studyStreak: number;
  studyTime: number;
  weeklyQuizzes: number;
  monthlyScoreChange: number;
  weeklyStudyTime: number;
}

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { showError } = useToast();

  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load user analytics data
      const response = await analyticsService.getUserAnalytics({
        days: 30,
        metrics: ['quizzes', 'scores', 'study_time', 'streak'],
        granularity: 'day'
      });

      if (response.success && response.data) {
        // Transform API response to dashboard stats
        const data = response.data;
        setStats({
          totalQuizzes: data.total_quizzes || 0,
          averageScore: data.average_score || 0,
          studyStreak: data.current_streak || 0,
          studyTime: data.total_study_time || 0,
          weeklyQuizzes: data.weekly_quizzes || 0,
          monthlyScoreChange: data.score_change || 0,
          weeklyStudyTime: data.weekly_study_time || 0
        });
      }
    } catch (err) {
      setError('Failed to load dashboard data');
      showError('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [showError]);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const getStatsCards = () => {
    if (!stats) return [];

    return [
      {
        title: t('dashboard.totalQuizzes'),
        value: stats.totalQuizzes.toString(),
        change: `+${stats.weeklyQuizzes} this week`,
        changeType: 'positive' as const,
        icon: BookOpen,
        gradient: 'from-primary-500 to-primary-600',
      },
      {
        title: t('dashboard.averageScore'),
        value: `${Math.round(stats.averageScore)}%`,
        change: `${stats.monthlyScoreChange >= 0 ? '+' : ''}${stats.monthlyScoreChange}% from last month`,
        changeType: stats.monthlyScoreChange >= 0 ? 'positive' as const : 'negative' as const,
        icon: TrendingUp,
        gradient: 'from-success-500 to-success-600',
      },
      {
        title: t('dashboard.studyStreak'),
        value: `${stats.studyStreak} days`,
        change: 'Keep it up!',
        changeType: 'positive' as const,
        icon: Zap,
        gradient: 'from-warning-500 to-warning-600',
      },
      {
        title: 'Study Time',
        value: `${Math.round(stats.studyTime / 60)}h`,
        change: `+${Math.round(stats.weeklyStudyTime / 60)}h this week`,
        changeType: 'positive' as const,
        icon: Clock,
        gradient: 'from-secondary-500 to-secondary-600',
      },
    ];
  };

  const recentActivities = [
    {
      id: 1,
      type: 'quiz',
      title: 'KPSS Genel Kültür Testi',
      score: 92,
      time: '2 hours ago',
      icon: BookOpen,
    },
    {
      id: 2,
      type: 'achievement',
      title: 'First Perfect Score!',
      description: 'Scored 100% on a quiz',
      time: '1 day ago',
      icon: Trophy,
    },
    {
      id: 3,
      type: 'study',
      title: 'Completed Tarih Chapter 5',
      progress: 100,
      time: '2 days ago',
      icon: Target,
    },
  ];

  const quickActions = [
    {
      title: t('dashboard.takeQuiz'),
      description: 'Start a new practice test',
      icon: BookOpen,
      color: 'from-primary-600 to-primary-700',
      href: '/quizzes',
    },
    {
      title: t('dashboard.viewProgress'),
      description: 'Check your learning progress',
      icon: BarChart3,
      color: 'from-success-600 to-success-700',
      href: '/progress',
    },
    {
      title: t('dashboard.browseContent'),
      description: 'Explore study materials',
      icon: PieChart,
      color: 'from-secondary-600 to-secondary-700',
      href: '/content',
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">
          {t('dashboard.title')}
        </h1>
        <p className="text-dark-300 text-lg">
          {t('dashboard.welcomeMessage', { name: user ? `, ${user.name}` : '' })}
        </p>
      </div>

      {/* Stats Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="card-hover animate-pulse">
              <div className="h-20 bg-dark-700 rounded"></div>
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-400">{error}</p>
          <button
            onClick={loadDashboardData}
            className="mt-4 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
          >
            Retry
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {getStatsCards().map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div 
              key={index} 
              className="card-hover group"
            >
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-medium text-dark-400 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-white">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-3 bg-gradient-to-br ${stat.gradient} rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="flex items-center">
                <ArrowUpRight className="h-4 w-4 text-success-400 mr-1" />
                <span className="text-sm font-medium text-success-400">
                  {stat.change}
                </span>
              </div>
            </div>
          );
        })}
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">
                {t('dashboard.recentActivity')}
              </h3>
              <button className="text-primary-400 hover:text-primary-300 text-sm font-medium transition-colors">
                View All
              </button>
            </div>
            
            <div className="space-y-4">
              {recentActivities.length > 0 ? (
                recentActivities.map((activity) => {
                  const Icon = activity.icon;
                  return (
                    <div 
                      key={activity.id}
                      className="flex items-center space-x-4 p-4 bg-dark-700/30 rounded-lg hover:bg-dark-700/50 transition-colors"
                    >
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        activity.type === 'quiz' 
                          ? 'bg-primary-500/20 text-primary-400'
                          : activity.type === 'achievement'
                          ? 'bg-warning-500/20 text-warning-400'
                          : 'bg-success-500/20 text-success-400'
                      }`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <p className="text-white font-medium truncate">
                          {activity.title}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-dark-400">
                          <span>{activity.time}</span>
                          {activity.score && (
                            <span className="text-success-400">
                              Score: {activity.score}%
                            </span>
                          )}
                          {activity.progress && (
                            <span className="text-primary-400">
                              Progress: {activity.progress}%
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-12">
                  <Calendar className="h-16 w-16 text-dark-500 mx-auto mb-4" />
                  <p className="text-dark-400 text-lg">{t('dashboard.noRecentActivity')}</p>
                  <p className="text-dark-500 text-sm mt-2">{t('dashboard.startLearning')}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-xl font-semibold text-white mb-6">
              {t('dashboard.quickActions')}
            </h3>
            
            <div className="space-y-4">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <button
                    key={index}
                    className={`w-full p-4 bg-gradient-to-r ${action.color} rounded-lg hover:shadow-lg transition-all duration-300 group text-left`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="h-5 w-5 text-white group-hover:scale-110 transition-transform" />
                      <div>
                        <p className="text-white font-medium">{action.title}</p>
                        <p className="text-white/80 text-sm">{action.description}</p>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Study Goals */}
          <div className="card">
            <h3 className="text-xl font-semibold text-white mb-6">
              Today's Goals
            </h3>
            
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-dark-300 text-sm">Complete 2 Quizzes</span>
                  <span className="text-primary-400 text-sm">1/2</span>
                </div>
                <div className="w-full bg-dark-700 rounded-full h-2">
                  <div className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full" style={{ width: '50%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-dark-300 text-sm">Study 30 minutes</span>
                  <span className="text-success-400 text-sm">45/30 min</span>
                </div>
                <div className="w-full bg-dark-700 rounded-full h-2">
                  <div className="bg-gradient-to-r from-success-500 to-success-600 h-2 rounded-full" style={{ width: '100%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
