import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';

export interface Content {
  id: string;
  title: string;
  description: string;
  content_type: 'article' | 'video' | 'document' | 'interactive';
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  duration: number; // minutes
  content_url?: string;
  content_text?: string;
  thumbnail_url?: string;
  tags: string[];
  author: string;
  author_avatar?: string;
  created_at: string;
  updated_at: string;
  is_featured: boolean;
  is_premium: boolean;
  view_count: number;
  like_count: number;
  is_liked?: boolean;
  is_favorite?: boolean;
  is_bookmarked?: boolean;
  progress_percentage?: number;
  estimated_read_time?: number;
}

export interface ContentStats {
  total_views: number;
  total_likes: number;
  total_bookmarks: number;
  average_rating: number;
  completion_rate: number;
}

export interface ContentFilters {
  content_type?: string;
  subject?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  tags?: string[];
  author?: string;
  is_featured?: boolean;
  is_premium?: boolean;
  min_duration?: number;
  max_duration?: number;
  search?: string;
}

export interface ContentListResponse {
  contents: Content[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

export interface CreateContentRequest {
  title: string;
  description: string;
  content_type: 'article' | 'video' | 'document' | 'interactive';
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  duration: number;
  content_url?: string;
  content_text?: string;
  thumbnail_url?: string;
  tags: string[];
  is_featured?: boolean;
  is_premium?: boolean;
}

export interface ContentProgress {
  content_id: string;
  progress_percentage: number;
  time_spent: number;
  last_accessed: string;
  is_completed: boolean;
  notes?: string;
}

export const contentService = {
  // Get content list with filters
  async getContents(params?: ContentFilters & { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get(endpoints.content.list, { params });
  },

  // Get content by ID
  async getContent(id: string): Promise<ApiResponse<Content>> {
    return http.get(endpoints.content.detail(id));
  },

  // Search contents
  async searchContents(query: string, filters?: ContentFilters & { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get(endpoints.content.search, {
      params: { search: query, ...filters }
    });
  },

  // Get contents by subject
  async getContentsBySubject(subject: string, params?: { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get(endpoints.content.bySubject(subject), { params });
  },

  // Get featured contents
  async getFeaturedContents(params?: { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get(endpoints.content.featured, { params });
  },

  // Get popular contents
  async getPopularContents(params?: { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get('/content/popular', { params });
  },

  // Get recommended contents
  async getRecommendedContents(params?: { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get(endpoints.content.recommended, { params });
  },

  // Get content stats
  async getContentStats(id: string): Promise<ApiResponse<ContentStats>> {
    return http.get(endpoints.content.detail(id) + '/stats');
  },

  // Create content
  async createContent(data: CreateContentRequest): Promise<ApiResponse<Content>> {
    return http.post(endpoints.content.create, data);
  },

  // Update content
  async updateContent(id: string, data: Partial<CreateContentRequest>): Promise<ApiResponse<Content>> {
    return http.put(endpoints.content.update(id), data);
  },

  // Delete content
  async deleteContent(id: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.content.delete(id));
  },

  // Add to library
  async addToLibrary(id: string): Promise<ApiResponse<void>> {
    return http.post(endpoints.content.addToLibrary(id));
  },

  // Remove from library
  async removeFromLibrary(id: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.content.removeFromLibrary(id));
  },

  // Get user's library contents
  async getLibraryContents(params?: { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get(endpoints.content.library, { params });
  },

  // Get content progress
  async getContentProgress(id: string): Promise<ApiResponse<ContentProgress>> {
    return http.get(endpoints.content.progress(id));
  },

  // Update content progress
  async updateContentProgress(data: {
    content_id: string;
    progress_percentage: number;
    time_spent: number;
    is_completed?: boolean;
    notes?: string;
  }): Promise<ApiResponse<ContentProgress>> {
    return http.put(endpoints.content.updateProgress, data);
  },

  // Like content
  async likeContent(id: string): Promise<ApiResponse<void>> {
    return http.post(`/content/${id}/like`);
  },

  // Unlike content
  async unlikeContent(id: string): Promise<ApiResponse<void>> {
    return http.delete(`/content/${id}/like`);
  },

  // Bookmark content
  async bookmarkContent(id: string): Promise<ApiResponse<void>> {
    return http.post(`/content/${id}/bookmark`);
  },

  // Remove bookmark
  async removeBookmark(id: string): Promise<ApiResponse<void>> {
    return http.delete(`/content/${id}/bookmark`);
  },

  // Get content by type
  async getContentsByType(type: string, params?: { page?: number; limit?: number }): Promise<ApiResponse<ContentListResponse>> {
    return http.get(`/content/type/${type}`, { params });
  }
};

export default contentService;
