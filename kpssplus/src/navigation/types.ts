import {NavigatorScreenParams} from '@react-navigation/native';

// Root Stack Navigator
export type RootStackParamList = {
  Main: NavigatorScreenParams<MainTabParamList>;
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Modal: NavigatorScreenParams<ModalStackParamList>;
};

// Main Tab Navigator (Bottom Tabs)
export type MainTabParamList = {
  Content: NavigatorScreenParams<ContentStackParamList>;
  Quiz: NavigatorScreenParams<QuizStackParamList>;
  Battle: NavigatorScreenParams<BattleStackParamList>;
  Social: NavigatorScreenParams<SocialStackParamList>;
  Menu: NavigatorScreenParams<MenuStackParamList>;
};

// Content Stack Navigator
export type ContentStackParamList = {
  ContentHome: undefined;
  StudyTopics: undefined;
  ContentDetail: {contentId: string};
  ContentPlayer: {contentId: string};
  ContentSearch: {query?: string};
  ContentLibrary: undefined;
  TopicDetail: {topicId: string};
  TopicList: {parentId?: string; subjectId?: string};
};

// Quiz Stack Navigator
export type QuizStackParamList = {
  QuizHome: undefined;
  QuizDetail: {quizId: string};
  QuizPlay: {quizId: string; sessionId?: string};
  QuizResult: {resultId: string};
  QuizSearch: {query?: string};
  QuizLeaderboard: {quizId: string};
};

// Social Stack Navigator
export type SocialStackParamList = {
  SocialHome: undefined;
  Timeline: undefined;
  Profile: {userId?: string};
  Friends: undefined;
  FriendRequests: undefined;
  UserSearch: {query?: string};
  PostDetail: {postId: string};
  CreatePost: undefined;
  Leaderboard: undefined;
};

export type BattleStackParamList = {
  BattleHome: undefined;
  GroupBattle: undefined;
  GroupBattleDetail: {battleId: string};
  CreateGroupBattle: undefined;
  JoinBattleByCode: undefined;
  FriendChallenge: undefined;
  ChallengeDetail: {challengeId: string};
  SendChallenge: undefined;
  BattleStats: undefined;
};

// Menu Stack Navigator
export type MenuStackParamList = {
  MenuHome: undefined;
  Profile: undefined;
  Settings: undefined;
  Badges: undefined;
  Analytics: undefined;
  Progress: undefined;
  Goals: undefined;
  Notifications: undefined;
  Help: undefined;
  About: undefined;
};

// Auth Stack Navigator
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: {token: string};
  VerifyOTP: {phone: string};
};

// Modal Stack Navigator
export type ModalStackParamList = {
  LoginModal: undefined;
  GuestLimitModal: undefined;
  ShareModal: {type: 'content' | 'quiz'; id: string};
  FilterModal: {filters: any};
  SearchModal: undefined;
};
