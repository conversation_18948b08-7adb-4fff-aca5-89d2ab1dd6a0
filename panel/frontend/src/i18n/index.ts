import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Translation resources
const resources = {
  en: {
    translation: {
      // Common
      common: {
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        cancel: 'Cancel',
        save: 'Save',
        delete: 'Delete',
        edit: 'Edit',
        view: 'View',
        search: 'Search',
        filter: 'Filter',
        sort: 'Sort',
        next: 'Next',
        previous: 'Previous',
        submit: 'Submit',
        close: 'Close',
        confirm: 'Confirm',
        yes: 'Yes',
        no: 'No',
      },

      // Navigation
      navigation: {
        dashboard: 'Dashboard',
        quizzes: 'Quizzes',
        progress: 'Progress',
        social: 'Social',
        content: 'Content',
        settings: 'Settings',
        profile: 'Profile',
        logout: 'Sign Out',
      },

      // Auth
      auth: {
        login: 'Sign In',
        register: 'Sign Up',
        logout: 'Sign Out',
        forgotPassword: 'Forgot Password?',
        resetPassword: 'Reset Password',
        email: 'Email',
        username: 'Use<PERSON><PERSON>',
        password: 'Password',
        confirmPassword: 'Confirm Password',
        name: 'Full Name',
        phone: 'Phone Number',
        welcomeBack: 'Welcome Back',
        createAccount: 'Create Account',
        alreadyHaveAccount: 'Already have an account?',
        dontHaveAccount: "Don't have an account?",
        signInHere: 'Sign in here',
        signUpHere: 'Sign up here',
        termsAndConditions: 'Terms and Conditions',
        privacyPolicy: 'Privacy Policy',
        agreeToTerms: 'I agree to the Terms and Conditions and Privacy Policy',
        loginSuccess: 'Login successful!',
        registerSuccess: 'Registration successful!',
        loginFailed: 'Login failed. Please try again.',
        registerFailed: 'Registration failed. Please try again.',
      },

      // Dashboard
      dashboard: {
        title: 'Dashboard',
        welcomeMessage: 'Welcome back{{name}}!',
        overview: 'Overview',
        recentActivity: 'Recent Activity',
        quickActions: 'Quick Actions',
        statistics: 'Statistics',
        totalQuizzes: 'Total Quizzes',
        completedQuizzes: 'Completed',
        averageScore: 'Average Score',
        studyStreak: 'Study Streak',
        weeklyProgress: 'Weekly Progress',
        monthlyProgress: 'Monthly Progress',
        takeQuiz: 'Take Quiz',
        viewProgress: 'View Progress',
        browseContent: 'Browse Content',
        noRecentActivity: 'No recent activity',
        startLearning: 'Start learning to see your activity here',
      },

      // Quiz
      quiz: {
        title: 'Quiz',
        quizzes: 'Quizzes',
        takeQuiz: 'Take Quiz',
        createQuiz: 'Create Quiz',
        myQuizzes: 'My Quizzes',
        popularQuizzes: 'Popular Quizzes',
        featuredQuizzes: 'Featured Quizzes',
        difficulty: 'Difficulty',
        easy: 'Easy',
        medium: 'Medium',
        hard: 'Hard',
        questions: 'Questions',
        timeLimit: 'Time Limit',
        minutes: 'minutes',
        startQuiz: 'Start Quiz',
        submitAnswer: 'Submit Answer',
        nextQuestion: 'Next Question',
        previousQuestion: 'Previous Question',
        finishQuiz: 'Finish Quiz',
        quizCompleted: 'Quiz Completed!',
        yourScore: 'Your Score',
        correctAnswers: 'Correct Answers',
        timeTaken: 'Time Taken',
        viewResults: 'View Results',
        retakeQuiz: 'Retake Quiz',
        shareQuiz: 'Share Quiz',
        noQuizzes: 'No quizzes available',
        searchQuizzes: 'Search quizzes...',
      },

      // Progress
      progress: {
        title: 'Progress',
        overview: 'Progress Overview',
        studyStats: 'Study Statistics',
        achievements: 'Achievements',
        goals: 'Goals',
        streak: 'Study Streak',
        totalStudyTime: 'Total Study Time',
        quizzesCompleted: 'Quizzes Completed',
        averageScore: 'Average Score',
        weakAreas: 'Areas to Improve',
        strongAreas: 'Strong Areas',
        recentProgress: 'Recent Progress',
        setGoal: 'Set Goal',
        dailyGoal: 'Daily Goal',
        weeklyGoal: 'Weekly Goal',
        monthlyGoal: 'Monthly Goal',
        goalAchieved: 'Goal Achieved!',
        keepItUp: 'Keep up the great work!',
      },

      // Social
      social: {
        title: 'Social',
        friends: 'Friends',
        leaderboard: 'Leaderboard',
        findFriends: 'Find Friends',
        friendRequests: 'Friend Requests',
        addFriend: 'Add Friend',
        removeFriend: 'Remove Friend',
        acceptRequest: 'Accept',
        declineRequest: 'Decline',
        sendRequest: 'Send Request',
        rank: 'Rank',
        score: 'Score',
        globalRanking: 'Global Ranking',
        friendsRanking: 'Friends Ranking',
        weeklyLeaderboard: 'Weekly Leaderboard',
        monthlyLeaderboard: 'Monthly Leaderboard',
        allTimeLeaderboard: 'All Time Leaderboard',
        noFriends: 'No friends yet',
        startConnecting: 'Start connecting with other learners',
      },

      // Content
      content: {
        title: 'Content',
        studyMaterials: 'Study Materials',
        myLibrary: 'My Library',
        recommended: 'Recommended',
        subjects: 'Subjects',
        addToLibrary: 'Add to Library',
        removeFromLibrary: 'Remove from Library',
        downloadContent: 'Download',
        shareContent: 'Share',
        contentType: 'Type',
        document: 'Document',
        video: 'Video',
        audio: 'Audio',
        interactive: 'Interactive',
        noContent: 'No content available',
        browseContent: 'Browse available content',
      },

      // Settings
      settings: {
        title: 'Settings',
        profile: 'Profile Settings',
        account: 'Account Settings',
        notifications: 'Notification Settings',
        privacy: 'Privacy Settings',
        language: 'Language',
        theme: 'Theme',
        changePassword: 'Change Password',
        deleteAccount: 'Delete Account',
        saveChanges: 'Save Changes',
        profileUpdated: 'Profile updated successfully',
        passwordChanged: 'Password changed successfully',
        settingsSaved: 'Settings saved successfully',
      },

      // Errors
      errors: {
        generic: 'Something went wrong. Please try again.',
        network: 'Network error. Please check your connection.',
        unauthorized: 'You are not authorized to perform this action.',
        notFound: 'The requested resource was not found.',
        validation: 'Please check your input and try again.',
        serverError: 'Server error. Please try again later.',
      },
    },
  },
  tr: {
    translation: {
      // Common
      common: {
        loading: 'Yükleniyor...',
        error: 'Hata',
        success: 'Başarılı',
        cancel: 'İptal',
        save: 'Kaydet',
        delete: 'Sil',
        edit: 'Düzenle',
        view: 'Görüntüle',
        search: 'Ara',
        filter: 'Filtrele',
        sort: 'Sırala',
        next: 'Sonraki',
        previous: 'Önceki',
        submit: 'Gönder',
        close: 'Kapat',
        confirm: 'Onayla',
        yes: 'Evet',
        no: 'Hayır',
      },

      // Navigation
      navigation: {
        dashboard: 'Ana Sayfa',
        quizzes: 'Testler',
        progress: 'İlerleme',
        social: 'Sosyal',
        content: 'İçerik',
        settings: 'Ayarlar',
        profile: 'Profil',
        logout: 'Çıkış Yap',
      },

      // Auth
      auth: {
        login: 'Giriş Yap',
        register: 'Kayıt Ol',
        logout: 'Çıkış Yap',
        forgotPassword: 'Şifremi Unuttum?',
        resetPassword: 'Şifre Sıfırla',
        email: 'E-posta',
        username: 'Kullanıcı Adı',
        password: 'Şifre',
        confirmPassword: 'Şifre Tekrar',
        name: 'Ad Soyad',
        phone: 'Telefon Numarası',
        welcomeBack: 'Tekrar Hoş Geldiniz',
        createAccount: 'Hesap Oluştur',
        alreadyHaveAccount: 'Zaten hesabınız var mı?',
        dontHaveAccount: 'Hesabınız yok mu?',
        signInHere: 'Buradan giriş yapın',
        signUpHere: 'Buradan kayıt olun',
        termsAndConditions: 'Kullanım Şartları',
        privacyPolicy: 'Gizlilik Politikası',
        agreeToTerms: 'Kullanım Şartları ve Gizlilik Politikasını kabul ediyorum',
        loginSuccess: 'Giriş başarılı!',
        registerSuccess: 'Kayıt başarılı!',
        loginFailed: 'Giriş başarısız. Lütfen tekrar deneyin.',
        registerFailed: 'Kayıt başarısız. Lütfen tekrar deneyin.',
      },

      // Dashboard
      dashboard: {
        title: 'Ana Sayfa',
        welcomeMessage: 'Tekrar hoş geldiniz{{name}}!',
        overview: 'Genel Bakış',
        recentActivity: 'Son Aktiviteler',
        quickActions: 'Hızlı İşlemler',
        statistics: 'İstatistikler',
        totalQuizzes: 'Toplam Test',
        completedQuizzes: 'Tamamlanan',
        averageScore: 'Ortalama Puan',
        studyStreak: 'Çalışma Serisi',
        weeklyProgress: 'Haftalık İlerleme',
        monthlyProgress: 'Aylık İlerleme',
        takeQuiz: 'Test Çöz',
        viewProgress: 'İlerlemeyi Gör',
        browseContent: 'İçerikleri Gözat',
        noRecentActivity: 'Son aktivite yok',
        startLearning: 'Aktivitelerinizi görmek için öğrenmeye başlayın',
      },

      // Quiz
      quiz: {
        title: 'Test',
        quizzes: 'Testler',
        takeQuiz: 'Test Çöz',
        createQuiz: 'Test Oluştur',
        myQuizzes: 'Testlerim',
        popularQuizzes: 'Popüler Testler',
        featuredQuizzes: 'Öne Çıkan Testler',
        difficulty: 'Zorluk',
        easy: 'Kolay',
        medium: 'Orta',
        hard: 'Zor',
        questions: 'Sorular',
        timeLimit: 'Süre Sınırı',
        minutes: 'dakika',
        startQuiz: 'Teste Başla',
        submitAnswer: 'Cevabı Gönder',
        nextQuestion: 'Sonraki Soru',
        previousQuestion: 'Önceki Soru',
        finishQuiz: 'Testi Bitir',
        quizCompleted: 'Test Tamamlandı!',
        yourScore: 'Puanınız',
        correctAnswers: 'Doğru Cevaplar',
        timeTaken: 'Geçen Süre',
        viewResults: 'Sonuçları Gör',
        retakeQuiz: 'Tekrar Çöz',
        shareQuiz: 'Testi Paylaş',
        noQuizzes: 'Mevcut test yok',
        searchQuizzes: 'Test ara...',
      },

      // Progress
      progress: {
        title: 'İlerleme',
        overview: 'İlerleme Özeti',
        studyStats: 'Çalışma İstatistikleri',
        achievements: 'Başarılar',
        goals: 'Hedefler',
        streak: 'Çalışma Serisi',
        totalStudyTime: 'Toplam Çalışma Süresi',
        quizzesCompleted: 'Tamamlanan Testler',
        averageScore: 'Ortalama Puan',
        weakAreas: 'Geliştirilmesi Gereken Alanlar',
        strongAreas: 'Güçlü Alanlar',
        recentProgress: 'Son İlerleme',
        setGoal: 'Hedef Belirle',
        dailyGoal: 'Günlük Hedef',
        weeklyGoal: 'Haftalık Hedef',
        monthlyGoal: 'Aylık Hedef',
        goalAchieved: 'Hedef Başarıldı!',
        keepItUp: 'Böyle devam edin!',
      },

      // Social
      social: {
        title: 'Sosyal',
        friends: 'Arkadaşlar',
        leaderboard: 'Lider Tablosu',
        findFriends: 'Arkadaş Bul',
        friendRequests: 'Arkadaşlık İstekleri',
        addFriend: 'Arkadaş Ekle',
        removeFriend: 'Arkadaşı Kaldır',
        acceptRequest: 'Kabul Et',
        declineRequest: 'Reddet',
        sendRequest: 'İstek Gönder',
        rank: 'Sıralama',
        score: 'Puan',
        globalRanking: 'Genel Sıralama',
        friendsRanking: 'Arkadaş Sıralaması',
        weeklyLeaderboard: 'Haftalık Lider Tablosu',
        monthlyLeaderboard: 'Aylık Lider Tablosu',
        allTimeLeaderboard: 'Tüm Zamanlar Lider Tablosu',
        noFriends: 'Henüz arkadaşınız yok',
        startConnecting: 'Diğer öğrencilerle bağlantı kurmaya başlayın',
      },

      // Content
      content: {
        title: 'İçerik',
        studyMaterials: 'Çalışma Materyalleri',
        myLibrary: 'Kütüphanem',
        recommended: 'Önerilen',
        subjects: 'Konular',
        addToLibrary: 'Kütüphaneye Ekle',
        removeFromLibrary: 'Kütüphaneden Kaldır',
        downloadContent: 'İndir',
        shareContent: 'Paylaş',
        contentType: 'Tür',
        document: 'Doküman',
        video: 'Video',
        audio: 'Ses',
        interactive: 'Etkileşimli',
        noContent: 'Mevcut içerik yok',
        browseContent: 'Mevcut içerikleri gözat',
      },

      // Settings
      settings: {
        title: 'Ayarlar',
        profile: 'Profil Ayarları',
        account: 'Hesap Ayarları',
        notifications: 'Bildirim Ayarları',
        privacy: 'Gizlilik Ayarları',
        language: 'Dil',
        theme: 'Tema',
        changePassword: 'Şifre Değiştir',
        deleteAccount: 'Hesabı Sil',
        saveChanges: 'Değişiklikleri Kaydet',
        profileUpdated: 'Profil başarıyla güncellendi',
        passwordChanged: 'Şifre başarıyla değiştirildi',
        settingsSaved: 'Ayarlar başarıyla kaydedildi',
      },

      // Errors
      errors: {
        generic: 'Bir şeyler ters gitti. Lütfen tekrar deneyin.',
        network: 'Ağ hatası. Lütfen bağlantınızı kontrol edin.',
        unauthorized: 'Bu işlemi gerçekleştirme yetkiniz yok.',
        notFound: 'İstenen kaynak bulunamadı.',
        validation: 'Lütfen girişinizi kontrol edin ve tekrar deneyin.',
        serverError: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.',
      },
    },
  },
};

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false,
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },

    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
  });

export default i18n;
