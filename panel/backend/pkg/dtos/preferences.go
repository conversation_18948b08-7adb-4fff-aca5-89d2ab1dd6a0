package dtos

type PreferencesResponse struct {
    Interests      []string          `json:"interests"`
    SubjectWeights map[string]float64 `json:"subject_weights"`
    DifficultyPref *string           `json:"difficulty_pref"`
}

type UpdatePreferencesRequest struct {
    Interests      []string          `json:"interests"`
    SubjectWeights map[string]float64 `json:"subject_weights"`
    DifficultyPref *string           `json:"difficulty_pref"`
}

