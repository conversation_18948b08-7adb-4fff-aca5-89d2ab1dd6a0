import {useState, useEffect, useCallback} from 'react';
import {Alert} from 'react-native';
import {battleService} from '../services/battleService';
import {GroupBattle, FriendChallenge} from '../types';
import {useAuth} from './useAuth';

interface BattleState {
  groupBattles: GroupBattle[];
  friendChallenges: FriendChallenge[];
  activeBattle: GroupBattle | null;
  activeChallenge: FriendChallenge | null;
  isLoading: boolean;
  error: string | null;
  battleStats: {
    totalGroupBattles: number;
    totalFriendChallenges: number;
    winRate: number;
    averageScore: number;
    bestRank: number;
  } | null;
}

interface BattleActions {
  // Group Battle Actions
  createGroupBattle: (data: {
    name: string;
    description?: string;
    quizId: string;
    maxParticipants: number;
    duration: number;
    isPrivate: boolean;
  }) => Promise<GroupBattle | null>;
  joinGroupBattle: (battleId: string, inviteCode?: string) => Promise<boolean>;
  leaveGroupBattle: (battleId: string) => Promise<boolean>;
  startGroupBattle: (battleId: string) => Promise<boolean>;
  submitGroupBattleResult: (
    battleId: string,
    result: {
      score: number;
      correctAnswers: number;
      totalQuestions: number;
      timeSpent: number;
    },
  ) => Promise<boolean>;

  // Friend Challenge Actions
  sendFriendChallenge: (data: {
    challengedId: string;
    quizId: string;
    message?: string;
  }) => Promise<FriendChallenge | null>;
  respondToFriendChallenge: (
    challengeId: string,
    action: 'accept' | 'decline',
  ) => Promise<boolean>;
  submitChallengeResult: (
    challengeId: string,
    result: {
      score: number;
      correctAnswers: number;
      totalQuestions: number;
      timeSpent: number;
    },
  ) => Promise<boolean>;

  // Data Loading
  loadGroupBattles: () => Promise<void>;
  loadFriendChallenges: () => Promise<void>;
  loadBattleStats: () => Promise<void>;
  refreshData: () => Promise<void>;

  // Utility
  clearError: () => void;
  setActiveBattle: (battle: GroupBattle | null) => void;
  setActiveChallenge: (challenge: FriendChallenge | null) => void;
}

const INITIAL_STATE: BattleState = {
  groupBattles: [],
  friendChallenges: [],
  activeBattle: null,
  activeChallenge: null,
  isLoading: false,
  error: null,
  battleStats: null,
};

export const useBattle = (): BattleState & BattleActions => {
  const [state, setState] = useState<BattleState>(INITIAL_STATE);
  const {isAuthenticated} = useAuth();

  // Create Group Battle
  const createGroupBattle = useCallback(
    async (data: {
      name: string;
      description?: string;
      quizId: string;
      maxParticipants: number;
      duration: number;
      isPrivate: boolean;
    }): Promise<GroupBattle | null> => {
      if (!isAuthenticated) {
        Alert.alert(
          'Hata',
          'Grup kapışması oluşturmak için giriş yapmalısınız',
        );
        return null;
      }

      try {
        setState(prev => ({...prev, isLoading: true, error: null}));
        const battle = await battleService.createGroupBattle(data);

        setState(prev => ({
          ...prev,
          groupBattles: [battle, ...prev.groupBattles],
          activeBattle: battle,
          isLoading: false,
        }));

        Alert.alert('Başarılı', 'Grup kapışması oluşturuldu!');
        return battle;
      } catch (error) {
        console.error('Error creating group battle:', error);
        setState(prev => ({
          ...prev,
          error: 'Grup kapışması oluşturulamadı',
          isLoading: false,
        }));
        Alert.alert('Hata', 'Grup kapışması oluşturulamadı');
        return null;
      }
    },
    [isAuthenticated],
  );

  // Join Group Battle
  const joinGroupBattle = useCallback(
    async (battleId: string, inviteCode?: string): Promise<boolean> => {
      if (!isAuthenticated) {
        Alert.alert(
          'Hata',
          'Grup kapışmasına katılmak için giriş yapmalısınız',
        );
        return false;
      }

      try {
        setState(prev => ({...prev, isLoading: true, error: null}));
        await battleService.joinGroupBattle(battleId, inviteCode);

        // Refresh battle data
        await loadGroupBattles();

        setState(prev => ({...prev, isLoading: false}));
        Alert.alert('Başarılı', 'Grup kapışmasına katıldınız!');
        return true;
      } catch (error) {
        console.error('Error joining group battle:', error);
        setState(prev => ({
          ...prev,
          error: 'Grup kapışmasına katılamadı',
          isLoading: false,
        }));
        Alert.alert('Hata', 'Grup kapışmasına katılamadı');
        return false;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isAuthenticated],
  );

  // Send Friend Challenge
  const sendFriendChallenge = useCallback(
    async (data: {
      challengedId: string;
      quizId: string;
      message?: string;
    }): Promise<FriendChallenge | null> => {
      if (!isAuthenticated) {
        Alert.alert('Hata', 'Meydan okuma göndermek için giriş yapmalısınız');
        return null;
      }

      try {
        setState(prev => ({...prev, isLoading: true, error: null}));
        const challenge = await battleService.sendFriendChallenge(data);

        setState(prev => ({
          ...prev,
          friendChallenges: [challenge, ...prev.friendChallenges],
          isLoading: false,
        }));

        Alert.alert('Başarılı', 'Meydan okuma gönderildi!');
        return challenge;
      } catch (error) {
        console.error('Error sending friend challenge:', error);
        setState(prev => ({
          ...prev,
          error: 'Meydan okuma gönderilemedi',
          isLoading: false,
        }));
        Alert.alert('Hata', 'Meydan okuma gönderilemedi');
        return null;
      }
    },
    [isAuthenticated],
  );

  // Respond to Friend Challenge
  const respondToFriendChallenge = useCallback(
    async (
      challengeId: string,
      action: 'accept' | 'decline',
    ): Promise<boolean> => {
      try {
        setState(prev => ({...prev, isLoading: true, error: null}));
        const challenge = await battleService.respondToFriendChallenge(
          challengeId,
          action,
        );

        setState(prev => ({
          ...prev,
          friendChallenges: prev.friendChallenges.map(c =>
            c.id === challengeId ? challenge : c,
          ),
          isLoading: false,
        }));

        const message =
          action === 'accept'
            ? 'Meydan okuma kabul edildi!'
            : 'Meydan okuma reddedildi';
        Alert.alert('Başarılı', message);
        return true;
      } catch (error) {
        console.error('Error responding to challenge:', error);
        setState(prev => ({
          ...prev,
          error: 'Meydan okumaya yanıt verilemedi',
          isLoading: false,
        }));
        Alert.alert('Hata', 'Meydan okumaya yanıt verilemedi');
        return false;
      }
    },
    [],
  );

  // Load Group Battles
  const loadGroupBattles = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      // For now, use mock data
      const battles = await battleService.getMockGroupBattles();

      setState(prev => ({
        ...prev,
        groupBattles: battles,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading group battles:', error);
      setState(prev => ({
        ...prev,
        error: 'Grup kapışmaları yüklenemedi',
        isLoading: false,
      }));
    }
  }, [isAuthenticated]);

  // Load Friend Challenges
  const loadFriendChallenges = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      // For now, use mock data
      const challenges = await battleService.getMockFriendChallenges();

      setState(prev => ({
        ...prev,
        friendChallenges: challenges,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading friend challenges:', error);
      setState(prev => ({
        ...prev,
        error: 'Arkadaş meydan okumaları yüklenemedi',
        isLoading: false,
      }));
    }
  }, [isAuthenticated]);

  // Load Battle Stats
  const loadBattleStats = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      // Mock stats for now
      const stats = {
        totalGroupBattles: 15,
        totalFriendChallenges: 8,
        winRate: 65,
        averageScore: 78,
        bestRank: 2,
      };

      setState(prev => ({
        ...prev,
        battleStats: stats,
      }));
    } catch (error) {
      console.error('Error loading battle stats:', error);
    }
  }, [isAuthenticated]);

  // Refresh All Data
  const refreshData = useCallback(async () => {
    await Promise.all([
      loadGroupBattles(),
      loadFriendChallenges(),
      loadBattleStats(),
    ]);
  }, [loadGroupBattles, loadFriendChallenges, loadBattleStats]);

  // Utility functions
  const clearError = useCallback(() => {
    setState(prev => ({...prev, error: null}));
  }, []);

  const setActiveBattle = useCallback((battle: GroupBattle | null) => {
    setState(prev => ({...prev, activeBattle: battle}));
  }, []);

  const setActiveChallenge = useCallback(
    (challenge: FriendChallenge | null) => {
      setState(prev => ({...prev, activeChallenge: challenge}));
    },
    [],
  );

  // Stub implementations for remaining actions
  const leaveGroupBattle = useCallback(
    async (_battleId: string): Promise<boolean> => {
      // Implementation would go here
      return true;
    },
    [],
  );

  const startGroupBattle = useCallback(
    async (_battleId: string): Promise<boolean> => {
      // Implementation would go here
      return true;
    },
    [],
  );

  const submitGroupBattleResult = useCallback(
    async (_battleId: string, _result: any): Promise<boolean> => {
      // Implementation would go here
      return true;
    },
    [],
  );

  const submitChallengeResult = useCallback(
    async (_challengeId: string, _result: any): Promise<boolean> => {
      // Implementation would go here
      return true;
    },
    [],
  );

  // Load initial data
  useEffect(() => {
    if (isAuthenticated) {
      refreshData();
    } else {
      setState(INITIAL_STATE);
    }
  }, [isAuthenticated, refreshData]);

  return {
    ...state,
    createGroupBattle,
    joinGroupBattle,
    leaveGroupBattle,
    startGroupBattle,
    submitGroupBattleResult,
    sendFriendChallenge,
    respondToFriendChallenge,
    submitChallengeResult,
    loadGroupBattles,
    loadFriendChallenges,
    loadBattleStats,
    refreshData,
    clearError,
    setActiveBattle,
    setActiveChallenge,
  };
};
