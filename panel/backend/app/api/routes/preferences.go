package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/kpss-plus-backend/pkg/domains/preferences"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/middleware"
    "github.com/kpss-plus-backend/pkg/state"
)

func PreferencesRoutes(r *gin.RouterGroup, s preferences.Service) {
    g := r.Group("/preferences")
    // GET is public (guest gets defaults), personalize if token present
    g.GET("", middleware.OptionalAuthorized(), getPreferences(s))
    // Update requires auth
    g.PUT("", middleware.Authorized(), updatePreferences(s))
}

func getPreferences(s preferences.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        resp, err := s.Get(userID)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return
        }
        c.<PERSON>(200, gin.H{"data": resp, "status": 200})
    }
}

func updatePreferences(s preferences.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.UpdatePreferencesRequest
        if err := c.ShouldBindJSON(&req); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        resp, err := s.Update(userID, &req)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

