package middleware

import (
    "net/http"
    "strings"

    "github.com/gin-gonic/gin"
    "github.com/nocytech/kpss-plus-admin/pkg/config"
    "github.com/nocytech/kpss-plus-admin/pkg/utils"
)

// AdminAuthorized validates admin JWT and exposes role and identity in context
func AdminAuthorized(appc config.App) gin.HandlerFunc {
    return func(c *gin.Context) {
        bearer := c.GetHeader("Authorization")
        if len(bearer) < 8 || !strings.HasPrefix(bearer, "Bearer ") {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "missing bearer"})
            return
        }
        token := strings.TrimPrefix(bearer, "Bearer ")
        if claims, ok := utils.ParseAndValidate(appc.JwtSecret, token); !ok {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
            return
        } else {
            c.Set("admin_role", claims.Role)
            c.Set("admin_username", claims.Username)
            c.Set("admin_id", claims.AdminID)
        }
        c.Next()
    }
}

// RequireRole checks role hierarchy: admin > editor > viewer
func RequireRole(required string) gin.HandlerFunc {
    rank := map[string]int{"viewer": 1, "editor": 2, "admin": 3}
    return func(c *gin.Context) {
        role := c.GetString("admin_role")
        if rank[role] < rank[required] {
            c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "forbidden"})
            return
        }
        c.Next()
    }
}

