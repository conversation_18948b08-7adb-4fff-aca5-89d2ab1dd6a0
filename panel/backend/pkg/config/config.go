package config

import (
	"log"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

type Config struct {
	App        App        `yaml:"app"`
	Database   Database   `yaml:"database"`
	Cloudinary Cloudinary `yaml:"cloudinary"`
	Allows     Allows     `yaml:"allows"`
	Whatsapp   Whatsapp   `yaml:"whatsapp"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	BaseUrl         string `yaml:"base_url"`
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtSecret       string `yaml:"jwt_secret"`
	JwtExpire       int    `yaml:"jwt_expire"`
	ClientID        string `yaml:"client_id"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
	ForceUpdateKey  string `yaml:"force_update_key"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Cloudinary struct {
	Name      string `mapstructure:"name"`
	APIKey    string `mapstructure:"api_key"`
	APISecret string `mapstructure:"api_secret"`
	APIFolder string `mapstructure:"api_folder"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type Whatsapp struct {
	ApiKey string `yaml:"api_key"`
}

func InitConfig() *Config {
	var cfg Config

	// Try to load from config file first
	filename, _ := filepath.Abs("./config.yaml")
	data, err := os.ReadFile(filename)
	if err == nil {
		if err := yaml.Unmarshal(data, &cfg); err != nil {
			log.Fatalf("config: unmarshal error: %v", err)
		}
	}

	// Override with environment variables if they exist
	if val := os.Getenv("APP_NAME"); val != "" {
		cfg.App.Name = val
	}
	if val := os.Getenv("APP_PORT"); val != "" {
		cfg.App.Port = val
	}
	if val := os.Getenv("APP_HOST"); val != "" {
		cfg.App.Host = val
	}
	if val := os.Getenv("APP_BASE_URL"); val != "" {
		cfg.App.BaseUrl = val
	}
	if val := os.Getenv("APP_JWT_ISSUER"); val != "" {
		cfg.App.JwtIssuer = val
	}
	if val := os.Getenv("APP_JWT_SECRET"); val != "" {
		cfg.App.JwtSecret = val
	}

	if val := os.Getenv("DB_HOST"); val != "" {
		cfg.Database.Host = val
	}
	if val := os.Getenv("DB_PORT"); val != "" {
		cfg.Database.Port = val
	}
	if val := os.Getenv("DB_USER"); val != "" {
		cfg.Database.User = val
	}
	if val := os.Getenv("DB_PASS"); val != "" {
		cfg.Database.Pass = val
	}
	if val := os.Getenv("DB_NAME"); val != "" {
		cfg.Database.Name = val
	}

	if val := os.Getenv("CORS_ORIGINS"); val != "" {
		cfg.Allows.Origins = strings.Split(val, ",")
	}

	return &cfg
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}
	filename, _ := filepath.Abs("./config.yaml")
	// Sanitize the destination path using filepath.Clean
	cleanedDst := filepath.Clean(filename)
	yamlFile, _ := os.ReadFile(cleanedDst)
	err := yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error loading config.yaml ", err)
	}
	return configs
}
