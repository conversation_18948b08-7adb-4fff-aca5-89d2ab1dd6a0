export type LibraryItemType = 'content' | 'topic' | 'quiz';

export interface FolderResponse {
  id: string;
  name: string;
}

export interface FoldersResponse {
  folders: FolderResponse[];
}

export interface LibraryItemResponse {
  id: string;
  item_type: LibraryItemType;
  item_id: string;
  folder_id?: string | null;
}

export interface LibraryListResponse {
  items: LibraryItemResponse[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface LibraryItemDetailContent { id: string; title: string; type: string; subject?: string | null }
export interface LibraryItemDetailTopic { id: string; title: string; subject?: string | null }
export interface LibraryItemDetailQuiz { id: string; title: string; difficulty?: string | null; time_limit?: number | null }

export interface LibraryItemDetail {
  content?: LibraryItemDetailContent;
  topic?: LibraryItemDetailTopic;
  quiz?: LibraryItemDetailQuiz;
}

export interface LibraryListWithDetailsResponse {
  items: LibraryItemResponse[];
  details: Record<string, LibraryItemDetail>; // key: item_id
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

