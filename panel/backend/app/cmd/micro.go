package cmd

import (
    "github.com/kpss-plus-backend/pkg/config"
    "github.com/kpss-plus-backend/pkg/database"
    "github.com/kpss-plus-backend/pkg/server"
    "github.com/kpss-plus-backend/pkg/dummy"
)

func StartApp() {
    config := config.InitConfig()
    database.InitDB(config.Database)
    //cache.InitRedis(config.Redis)
    //cron.MyCron()
    // Seed demo data in empty DB (idempotent)
    dummy.CreateDummy()
    dummy.SeedTopicsIfEmpty()
    server.LaunchHttpServer(config.App, config.Allows)

}
