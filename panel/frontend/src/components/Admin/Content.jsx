import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

const Content = () => {
  const [content, setContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedContent, setSelectedContent] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const API_URL = process.env.REACT_APP_ADMIN_API_URL || 'http://localhost:8100/api';

  const fetchContent = useCallback(async () => {
    const token = localStorage.getItem('adminToken');
    
    try {
      const response = await axios.get(`${API_URL}/contents?limit=10`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setContent(response.data.data);
    } catch (err) {
      console.error('Failed to fetch content:', err);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  useEffect(() => {
    fetchContent();
  }, [fetchContent]);

  const handleDelete = async (contentId) => {
    if (!window.confirm('Are you sure you want to delete this content?')) {
      return;
    }

    const token = localStorage.getItem('adminToken');
    
    try {
      await axios.delete(`${API_URL}/admin/content/${contentId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      fetchContent();
    } catch (err) {
      console.error('Failed to delete content:', err);
    }
  };

  const handleEdit = (contentItem) => {
    setSelectedContent(contentItem);
    setShowModal(true);
  };

  const handleCreate = () => {
    setSelectedContent({
      title: '',
      description: '',
      content_type: '',
      difficulty_level: '',
      status: 'draft'
    });
    setShowModal(true);
  };

  const handleSave = async (e) => {
    e.preventDefault();
    const token = localStorage.getItem('adminToken');
    
    try {
      if (selectedContent.id) {
        await axios.put(`${API_URL}/admin/content/${selectedContent.id}`, selectedContent, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } else {
        await axios.post(`${API_URL}/admin/content`, selectedContent, {
          headers: { Authorization: `Bearer ${token}` }
        });
      }
      
      setShowModal(false);
      fetchContent();
    } catch (err) {
      console.error('Failed to save content:', err);
    }
  };

  if (loading) {
    return <div className="loading">Loading content...</div>;
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>Content</h2>
        <button className="btn btn-success" onClick={handleCreate}>
          Create New Content
        </button>
      </div>
      
      <div className="table-container">
        <h3>Manage Content</h3>
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Title</th>
              <th>Type</th>
              <th>Difficulty</th>
              <th>Status</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {content.map((item) => (
              <tr key={item.id}>
                <td>{item.id}</td>
                <td>{item.title}</td>
                <td>{item.content_type}</td>
                <td>{item.difficulty_level}</td>
                <td>{item.status}</td>
                <td>{new Date(item.created_at).toLocaleDateString()}</td>
                <td className="actions">
                  <button className="btn btn-primary" onClick={() => handleEdit(item)}>
                    Edit
                  </button>
                  <button className="btn btn-danger" onClick={() => handleDelete(item.id)}>
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {showModal && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{selectedContent.id ? 'Edit Content' : 'Create Content'}</h3>
              <button className="close-btn" onClick={() => setShowModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={handleSave}>
              <div className="form-group">
                <label>Title</label>
                <input
                  type="text"
                  value={selectedContent.title}
                  onChange={(e) => setSelectedContent({...selectedContent, title: e.target.value})}
                  required
                />
              </div>
              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={selectedContent.description}
                  onChange={(e) => setSelectedContent({...selectedContent, description: e.target.value})}
                  required
                />
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>Content Type</label>
                  <select
                    value={selectedContent.content_type}
                    onChange={(e) => setSelectedContent({...selectedContent, content_type: e.target.value})}
                    required
                  >
                    <option value="">Select type</option>
                    <option value="article">Article</option>
                    <option value="video">Video</option>
                    <option value="quiz">Quiz</option>
                    <option value="exercise">Exercise</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>Difficulty Level</label>
                  <select
                    value={selectedContent.difficulty_level}
                    onChange={(e) => setSelectedContent({...selectedContent, difficulty_level: e.target.value})}
                    required
                  >
                    <option value="">Select difficulty</option>
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
              </div>
              <div className="form-group">
                <label>Status</label>
                <select
                  value={selectedContent.status}
                  onChange={(e) => setSelectedContent({...selectedContent, status: e.target.value})}
                  required
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
              </div>
              <div className="actions">
                <button type="submit" className="btn btn-success">
                  Save
                </button>
                <button type="button" className="btn btn-danger" onClick={() => setShowModal(false)}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export { Content };