package content

import (
	"errors"
	"fmt"
	"math"
	"strings"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	// Content management
	CreateContent(userID string, payload *dtos.CreateContentRequest) (*dtos.ContentResponse, error)
	GetContent(contentID string) (*dtos.ContentResponse, error)
	GetContentList(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	UpdateContent(contentID string, payload *dtos.UpdateContentRequest) (*dtos.ContentResponse, error)
	DeleteContent(contentID string) error

	// Content search and filtering
	SearchContent(payload *dtos.SearchContentRequest) (*dtos.ContentListResponse, error)
	GetContentByType(contentType string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	GetContentBySubject(subject string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)

	// User's content library
	GetUserContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	AddContentToLibrary(userID string, contentID string) error
	RemoveContentFromLibrary(userID string, contentID string) error

	// Progress tracking
	UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error)
	GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error)
	GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error)

	// Content statistics
	GetContentStats(contentID string) (*dtos.ContentStatsResponse, error)
	GetPopularContent(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	GetRecommendedContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Content management
func (r *repository) CreateContent(userID string, payload *dtos.CreateContentRequest) (*dtos.ContentResponse, error) {
	var creatorID *uuid.UUID
	if userID != "" {
		id, err := uuid.Parse(userID)
		if err != nil {
			return nil, errors.New("Invalid user ID")
		}
		creatorID = &id
	}

	content := entities.Content{
		Title:       payload.Title,
		Description: payload.Description,
		Type:        entities.ContentType(payload.Type),
		URL:         payload.URL,
		TotalPages:  payload.TotalPages,
		TotalTime:   payload.TotalTime,
		Subject:     payload.Subject,
		Difficulty:  payload.Difficulty,
		IsOfficial:  payload.IsOfficial,
		CreatorID:   creatorID,
		Author:      payload.Author,
		Publisher:   payload.Publisher,
		Year:        payload.Year,
		ISBN:        payload.ISBN,
		ChannelName: payload.ChannelName,
		Duration:    payload.Duration,
	}

	err := r.db.Create(&content).Error
	if err != nil {
		return nil, errors.New("Failed to create content")
	}

	return r.GetContent(content.ID.String())
}

func (r *repository) GetContent(contentID string) (*dtos.ContentResponse, error) {
	var content entities.Content
	err := r.db.Where("id = ?", contentID).First(&content).Error
	if err != nil {
		return nil, errors.New("Content not found")
	}

	// Get content statistics
	var usersCount int64
	r.db.Model(&entities.Progress{}).Where("content_id = ?", contentID).Count(&usersCount)

	var completedCount int64
	r.db.Model(&entities.Progress{}).Where("content_id = ? AND is_completed = ?", contentID, true).Count(&completedCount)

	completionRate := float64(0)
	if usersCount > 0 {
		completionRate = float64(completedCount) / float64(usersCount) * 100
	}

	var creatorIDStr *string
	if content.CreatorID != nil {
		str := content.CreatorID.String()
		creatorIDStr = &str
	}

	return &dtos.ContentResponse{
		ID:             content.ID,
		Title:          content.Title,
		Description:    content.Description,
		Type:           string(content.Type),
		URL:            content.URL,
		TotalPages:     content.TotalPages,
		TotalTime:      content.TotalTime,
		Subject:        content.Subject,
		Difficulty:     content.Difficulty,
		IsOfficial:     content.IsOfficial,
		CreatorID:      creatorIDStr,
		Author:         content.Author,
		Publisher:      content.Publisher,
		Year:           content.Year,
		ISBN:           content.ISBN,
		ChannelName:    content.ChannelName,
		Duration:       content.Duration,
		UsersCount:     int(usersCount),
		CompletionRate: completionRate,
		CreatedAt:      content.CreatedAt,
		UpdatedAt:      content.UpdatedAt,
	}, nil
}

func (r *repository) GetContentList(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	page := payload.Page
	if page < 1 {
		page = 1
	}

	limit := payload.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	query := r.db.Model(&entities.Content{})

	// Apply filters
	if payload.Type != nil {
		query = query.Where("type = ?", *payload.Type)
	}
	if payload.Subject != nil {
		query = query.Where("subject = ?", *payload.Subject)
	}
	if payload.Difficulty != nil {
		query = query.Where("difficulty = ?", *payload.Difficulty)
	}
	if payload.IsOfficial != nil {
		query = query.Where("is_official = ?", *payload.IsOfficial)
	}

	// Apply sorting
	sortBy := payload.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := "ASC"
	if payload.SortDesc {
		sortOrder = "DESC"
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Get total count
	var total int64
	query.Count(&total)

	// Get contents
	var contents []entities.Content
	err := query.Offset(offset).Limit(limit).Find(&contents).Error
	if err != nil {
		return nil, errors.New("Failed to get content list")
	}

	// Convert to response format
	contentResponses := make([]dtos.ContentResponse, len(contents))
	for i, content := range contents {
		var creatorIDStr *string
		if content.CreatorID != nil {
			str := content.CreatorID.String()
			creatorIDStr = &str
		}

		contentResponses[i] = dtos.ContentResponse{
			ID:          content.ID,
			Title:       content.Title,
			Description: content.Description,
			Type:        string(content.Type),
			URL:         content.URL,
			TotalPages:  content.TotalPages,
			TotalTime:   content.TotalTime,
			Subject:     content.Subject,
			Difficulty:  content.Difficulty,
			IsOfficial:  content.IsOfficial,
			CreatorID:   creatorIDStr,
			Author:      content.Author,
			Publisher:   content.Publisher,
			Year:        content.Year,
			ISBN:        content.ISBN,
			ChannelName: content.ChannelName,
			Duration:    content.Duration,
			CreatedAt:   content.CreatedAt,
			UpdatedAt:   content.UpdatedAt,
		}
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &dtos.ContentListResponse{
		Contents:   contentResponses,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (r *repository) UpdateContent(contentID string, payload *dtos.UpdateContentRequest) (*dtos.ContentResponse, error) {
	var content entities.Content
	err := r.db.Where("id = ?", contentID).First(&content).Error
	if err != nil {
		return nil, errors.New("Content not found")
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if payload.Title != nil {
		updates["title"] = *payload.Title
	}
	if payload.Description != nil {
		updates["description"] = *payload.Description
	}
	if payload.URL != nil {
		updates["url"] = *payload.URL
	}
	if payload.TotalPages != nil {
		updates["total_pages"] = *payload.TotalPages
	}
	if payload.TotalTime != nil {
		updates["total_time"] = *payload.TotalTime
	}
	if payload.Subject != nil {
		updates["subject"] = *payload.Subject
	}
	if payload.Difficulty != nil {
		updates["difficulty"] = *payload.Difficulty
	}
	if payload.Author != nil {
		updates["author"] = *payload.Author
	}
	if payload.Publisher != nil {
		updates["publisher"] = *payload.Publisher
	}
	if payload.Year != nil {
		updates["year"] = *payload.Year
	}
	if payload.ISBN != nil {
		updates["isbn"] = *payload.ISBN
	}
	if payload.ChannelName != nil {
		updates["channel_name"] = *payload.ChannelName
	}
	if payload.Duration != nil {
		updates["duration"] = *payload.Duration
	}

	if len(updates) > 0 {
		err = r.db.Model(&content).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update content")
		}
	}

	return r.GetContent(contentID)
}

func (r *repository) DeleteContent(contentID string) error {
	err := r.db.Where("id = ?", contentID).Delete(&entities.Content{}).Error
	if err != nil {
		return errors.New("Failed to delete content")
	}
	return nil
}

// Content search and filtering
func (r *repository) SearchContent(payload *dtos.SearchContentRequest) (*dtos.ContentListResponse, error) {
	page := payload.Page
	if page < 1 {
		page = 1
	}

	limit := payload.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	query := r.db.Model(&entities.Content{})

	// Search in title, description, author, and subject
	searchTerm := "%" + strings.ToLower(payload.Query) + "%"
	query = query.Where(
		"LOWER(title) LIKE ? OR LOWER(description) LIKE ? OR LOWER(author) LIKE ? OR LOWER(subject) LIKE ?",
		searchTerm, searchTerm, searchTerm, searchTerm,
	)

	// Apply filters
	if payload.Type != nil {
		query = query.Where("type = ?", *payload.Type)
	}
	if payload.Subject != nil {
		query = query.Where("subject = ?", *payload.Subject)
	}
	if payload.Difficulty != nil {
		query = query.Where("difficulty = ?", *payload.Difficulty)
	}
	if payload.IsOfficial != nil {
		query = query.Where("is_official = ?", *payload.IsOfficial)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get contents
	var contents []entities.Content
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&contents).Error
	if err != nil {
		return nil, errors.New("Failed to search content")
	}

	// Convert to response format
	contentResponses := make([]dtos.ContentResponse, len(contents))
	for i, content := range contents {
		var creatorIDStr *string
		if content.CreatorID != nil {
			str := content.CreatorID.String()
			creatorIDStr = &str
		}

		contentResponses[i] = dtos.ContentResponse{
			ID:          content.ID,
			Title:       content.Title,
			Description: content.Description,
			Type:        string(content.Type),
			URL:         content.URL,
			TotalPages:  content.TotalPages,
			TotalTime:   content.TotalTime,
			Subject:     content.Subject,
			Difficulty:  content.Difficulty,
			IsOfficial:  content.IsOfficial,
			CreatorID:   creatorIDStr,
			Author:      content.Author,
			Publisher:   content.Publisher,
			Year:        content.Year,
			ISBN:        content.ISBN,
			ChannelName: content.ChannelName,
			Duration:    content.Duration,
			CreatedAt:   content.CreatedAt,
			UpdatedAt:   content.UpdatedAt,
		}
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &dtos.ContentListResponse{
		Contents:   contentResponses,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (r *repository) GetContentByType(contentType string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	// Create a new request with type filter
	newPayload := *payload
	newPayload.Type = &contentType
	return r.GetContentList(&newPayload)
}

func (r *repository) GetContentBySubject(subject string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	// Create a new request with subject filter
	newPayload := *payload
	newPayload.Subject = &subject
	return r.GetContentList(&newPayload)
}

// User's content library (placeholder implementations)
func (r *repository) GetUserContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	// TODO: Implement user content library
	// This would involve a many-to-many relationship between users and content
	return r.GetContentList(payload)
}

func (r *repository) AddContentToLibrary(userID string, contentID string) error {
	// TODO: Implement adding content to user library
	// This would involve creating a UserContent relationship
	return errors.New("User content library not implemented yet")
}

func (r *repository) RemoveContentFromLibrary(userID string, contentID string) error {
	// TODO: Implement removing content from user library
	return errors.New("User content library not implemented yet")
}

// Progress tracking
func (r *repository) UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error) {
	var progress entities.Progress

	// Try to find existing progress
	err := r.db.Where("user_id = ? AND content_id = ?", userID, payload.ContentID).First(&progress).Error
	if err != nil {
		// Create new progress record
		userUUID := uuid.MustParse(userID)
		progress = entities.Progress{
			UserID:    &userUUID,
			ContentID: &payload.ContentID,
		}
	}

	// Update fields if provided
	if payload.CurrentPage != nil {
		progress.CurrentPage = payload.CurrentPage
	}
	if payload.CurrentTime != nil {
		progress.CurrentTime = payload.CurrentTime
	}
	if payload.Notes != nil {
		progress.Notes = payload.Notes
	}
	if payload.IsCompleted != nil {
		progress.IsCompleted = *payload.IsCompleted
	}

	// Calculate percentage based on content type and progress
	var content entities.Content
	r.db.Where("id = ?", payload.ContentID).First(&content)

	percentage := float64(0)
	if content.TotalPages != nil && progress.CurrentPage != nil {
		percentage = float64(*progress.CurrentPage) / float64(*content.TotalPages) * 100
	} else if content.TotalTime != nil && progress.CurrentTime != nil {
		percentage = float64(*progress.CurrentTime) / float64(*content.TotalTime) * 100
	}

	if percentage > 100 {
		percentage = 100
	}
	progress.Percentage = percentage

	// Save progress
	if progress.ID == uuid.Nil {
		err = r.db.Create(&progress).Error
	} else {
		err = r.db.Save(&progress).Error
	}

	if err != nil {
		return nil, errors.New("Failed to update progress")
	}

	return r.GetProgress(userID, payload.ContentID.String())
}

func (r *repository) GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error) {
	var progress entities.Progress
	err := r.db.Where("user_id = ? AND content_id = ?", userID, contentID).First(&progress).Error
	if err != nil {
		return nil, errors.New("Progress not found")
	}

	// Get content info
	contentResponse, err := r.GetContent(contentID)
	if err != nil {
		return nil, err
	}

	return &dtos.ProgressResponse{
		ID:          progress.ID,
		UserID:      progress.UserID,
		ContentID:   progress.ContentID,
		CurrentPage: progress.CurrentPage,
		CurrentTime: progress.CurrentTime,
		Percentage:  progress.Percentage,
		IsCompleted: progress.IsCompleted,
		Notes:       progress.Notes,
		Content:     *contentResponse,
		CreatedAt:   progress.CreatedAt,
		UpdatedAt:   progress.UpdatedAt,
	}, nil
}

func (r *repository) GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error) {
	page := payload.Page
	if page < 1 {
		page = 1
	}

	limit := payload.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	query := r.db.Model(&entities.Progress{}).Where("user_id = ?", userID)

	// Apply filters
	if payload.ContentType != nil {
		query = query.Joins("JOIN contents ON progress.content_id = contents.id").
			Where("contents.type = ?", *payload.ContentType)
	}
	if payload.Subject != nil {
		query = query.Joins("JOIN contents ON progress.content_id = contents.id").
			Where("contents.subject = ?", *payload.Subject)
	}
	if payload.IsCompleted != nil {
		query = query.Where("is_completed = ?", *payload.IsCompleted)
	}

	// Apply sorting
	sortBy := payload.SortBy
	if sortBy == "" {
		sortBy = "updated_at"
	}

	sortOrder := "ASC"
	if payload.SortDesc {
		sortOrder = "DESC"
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Get total count
	var total int64
	query.Count(&total)

	// Get progress records
	var progressRecords []entities.Progress
	err := query.Offset(offset).Limit(limit).Find(&progressRecords).Error
	if err != nil {
		return nil, errors.New("Failed to get user progress")
	}

	// Convert to response format
	progressResponses := make([]dtos.ProgressResponse, len(progressRecords))
	for i, progress := range progressRecords {
		contentResponse, _ := r.GetContent(progress.ContentID.String())

		progressResponses[i] = dtos.ProgressResponse{
			ID:          progress.ID,
			UserID:      progress.UserID,
			ContentID:   progress.ContentID,
			CurrentPage: progress.CurrentPage,
			CurrentTime: progress.CurrentTime,
			Percentage:  progress.Percentage,
			IsCompleted: progress.IsCompleted,
			Notes:       progress.Notes,
			CreatedAt:   progress.CreatedAt,
			UpdatedAt:   progress.UpdatedAt,
		}

		if contentResponse != nil {
			progressResponses[i].Content = *contentResponse
		}
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &dtos.ProgressListResponse{
		Progress:   progressResponses,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

// Content statistics (placeholder implementations)
func (r *repository) GetContentStats(contentID string) (*dtos.ContentStatsResponse, error) {
	var usersCount int64
	r.db.Model(&entities.Progress{}).Where("content_id = ?", contentID).Count(&usersCount)

	var completedCount int64
	r.db.Model(&entities.Progress{}).Where("content_id = ? AND is_completed = ?", contentID, true).Count(&completedCount)

	completionRate := float64(0)
	if usersCount > 0 {
		completionRate = float64(completedCount) / float64(usersCount) * 100
	}

	contentUUID, err := uuid.Parse(contentID)
	if err != nil {
		return nil, errors.New("Invalid content ID")
	}

	return &dtos.ContentStatsResponse{
		ContentID:      contentUUID,
		UsersCount:     int(usersCount),
		CompletedCount: int(completedCount),
		AverageRating:  0, // TODO: Implement rating system
		CompletionRate: completionRate,
		DailyUsers:     []dtos.DailyUserStat{},   // TODO: Implement time-based stats
		WeeklyUsers:    []dtos.WeeklyUserStat{},  // TODO: Implement time-based stats
		MonthlyUsers:   []dtos.MonthlyUserStat{}, // TODO: Implement time-based stats
	}, nil
}

func (r *repository) GetPopularContent(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	// TODO: Implement popularity-based sorting
	// For now, just return content sorted by creation date
	return r.GetContentList(payload)
}

func (r *repository) GetRecommendedContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	// TODO: Implement recommendation algorithm based on:
	// - User's study area
	// - User's progress history
	// - Similar users' preferences
	// - Content popularity

	// For now, just return content sorted by creation date
	return r.GetContentList(payload)
}
