import {TextStyle} from 'react-native';
import {colors} from './colors';

// Font weights
export const fontWeights = {
  light: '300' as const,
  normal: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
  extrabold: '800' as const,
};

// Font sizes
export const fontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
  '6xl': 60,
};

// Line heights
export const lineHeights = {
  none: 1,
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2,
};

// Typography styles
export const typography = {
  // Headings
  h1: {
    fontSize: fontSizes['4xl'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.tight * fontSizes['4xl'],
    color: colors.text.primary,
  } as TextStyle,

  h2: {
    fontSize: fontSizes['3xl'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.tight * fontSizes['3xl'],
    color: colors.text.primary,
  } as TextStyle,

  h3: {
    fontSize: fontSizes['2xl'],
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.tight * fontSizes['2xl'],
    color: colors.text.primary,
  } as TextStyle,

  h4: {
    fontSize: fontSizes.xl,
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.snug * fontSizes.xl,
    color: colors.text.primary,
  } as TextStyle,

  h5: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.snug * fontSizes.lg,
    color: colors.text.primary,
  } as TextStyle,

  h6: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal * fontSizes.base,
    color: colors.text.primary,
  } as TextStyle,

  // Body text
  body1: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.base,
    color: colors.text.primary,
  } as TextStyle,

  body2: {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.sm,
    color: colors.text.secondary,
  } as TextStyle,

  // Captions and small text
  caption: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.xs,
    color: colors.text.tertiary,
  } as TextStyle,

  overline: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal * fontSizes.xs,
    color: colors.text.secondary,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.5,
  } as TextStyle,

  // Button text
  button: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.none * fontSizes.base,
    textAlign: 'center' as const,
  } as TextStyle,

  buttonSmall: {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.none * fontSizes.sm,
    textAlign: 'center' as const,
  } as TextStyle,

  buttonLarge: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.none * fontSizes.lg,
    textAlign: 'center' as const,
  } as TextStyle,

  // Link text
  link: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.base,
    color: colors.text.link,
    textDecorationLine: 'underline' as const,
  } as TextStyle,

  // Input text
  input: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.base,
    color: colors.input.text,
  } as TextStyle,

  inputLabel: {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal * fontSizes.sm,
    color: colors.text.primary,
    marginBottom: 4,
  } as TextStyle,

  inputError: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.xs,
    color: colors.text.error,
    marginTop: 4,
  } as TextStyle,

  // Tab bar text
  tabLabel: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.none * fontSizes.xs,
  } as TextStyle,

  // Navigation text
  navTitle: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.none * fontSizes.lg,
    color: colors.white,
  } as TextStyle,

  // Card text
  cardTitle: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.semibold,
    lineHeight: lineHeights.snug * fontSizes.lg,
    color: colors.text.primary,
  } as TextStyle,

  cardSubtitle: {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.sm,
    color: colors.text.secondary,
  } as TextStyle,

  // Quiz text
  quizQuestion: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.normal * fontSizes.lg,
    color: colors.text.primary,
  } as TextStyle,

  quizOption: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal * fontSizes.base,
    color: colors.text.primary,
  } as TextStyle,

  // Score text
  scoreText: {
    fontSize: fontSizes['2xl'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.none * fontSizes['2xl'],
    color: colors.primary,
  } as TextStyle,

  // Badge text
  badgeText: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.medium,
    lineHeight: lineHeights.none * fontSizes.xs,
    color: colors.white,
    textAlign: 'center' as const,
  } as TextStyle,
} as const;

// Utility function to create custom text styles
export const createTextStyle = (
  fontSize: number,
  fontWeight: keyof typeof fontWeights,
  color: string,
  lineHeight?: number,
): TextStyle => ({
  fontSize,
  fontWeight: fontWeights[fontWeight],
  color,
  lineHeight: lineHeight || lineHeights.normal * fontSize,
});
