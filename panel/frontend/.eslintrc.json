{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["react-app", "react-app/jest"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-unused-vars": "warn", "no-console": "off", "no-use-before-define": "error", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off"}, "settings": {"react": {"version": "detect"}}}