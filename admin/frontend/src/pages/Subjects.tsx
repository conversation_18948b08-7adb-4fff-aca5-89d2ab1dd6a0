import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout';
import { apiDel, apiGet, apiPost, apiPut } from '../lib/api';
import { 
  MagnifyingGlassIcon, 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface Area {
  id: string;
  name: string;
  code: string;
}

interface Subject {
  id: string;
  area_id: string;
  name: string;
  code: string;
  description?: string;
  order: number;
  is_active: boolean;
  area?: Area;
}

const Subjects: React.FC = () => {
  const [items, setItems] = useState<Subject[]>([]);
  const [areas, setAreas] = useState<Area[]>([]);
  const [loading, setLoading] = useState(false);
  const [q, setQ] = useState('');

  // Form states
  const [areaId, setAreaId] = useState('');
  const [name, setName] = useState('');
  const [code, setCode] = useState('');
  const [description, setDescription] = useState('');
  const [order, setOrder] = useState<number | ''>('');
  const [isActive, setIsActive] = useState(true);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);

  const loadAreas = async () => {
    try {
      const data = await apiGet<{ data: Area[] }>('/areas');
      setAreas(data.data || []);
    } catch (error) {
      console.error('Error loading areas:', error);
    }
  };

  const load = async () => {
    setLoading(true);
    try {
      const data = await apiGet<{ data: Subject[] }>('/subjects');
      setItems(data.data || []);
    } catch (error) {
      console.error('Error loading subjects:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAreas();
    load();
  }, []);

  const create = async () => {
    if (!name || !code || !areaId) return;
    
    try {
      await apiPost('/subjects', {
        area_id: areaId,
        name,
        code,
        description: description || undefined,
        order: order === '' ? 0 : Number(order),
        is_active: isActive
      });
      
      // Reset form
      setAreaId('');
      setName('');
      setCode('');
      setDescription('');
      setOrder('');
      setIsActive(true);
      setShowCreateModal(false);
      
      load();
    } catch (error) {
      console.error('Error creating subject:', error);
    }
  };

  const openEditModal = (subject: Subject) => {
    setEditingSubject(subject);
    setShowEditModal(true);
  };

  const saveEdit = async () => {
    if (!editingSubject) return;
    
    try {
      await apiPut(`/subjects/${editingSubject.id}`, editingSubject);
      setShowEditModal(false);
      setEditingSubject(null);
      load();
    } catch (error) {
      console.error('Error updating subject:', error);
    }
  };

  const remove = async (id: string) => {
    if (window.confirm('Bu dersi silmek istediğinizden emin misiniz?')) {
      try {
        await apiDel(`/subjects/${id}`);
        load();
      } catch (error) {
        console.error('Error deleting subject:', error);
      }
    }
  };

  const filteredItems = items.filter(item => 
    item.name.toLowerCase().includes(q.toLowerCase()) ||
    item.code.toLowerCase().includes(q.toLowerCase()) ||
    item.area?.name.toLowerCase().includes(q.toLowerCase())
  );

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dersler</h1>
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              KPSS derslerini yönetin (Türkçe, Matematik, Tarih, Coğrafya, Vatandaşlık...)
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              onClick={() => setShowCreateModal(true)}
              className="btn-primary flex items-center"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Ders Ekle
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="card">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="input-field pl-10"
              placeholder="Ders ara..."
              value={q}
              onChange={(e) => setQ(e.target.value)}
            />
          </div>
        </div>

        {/* Subjects Table */}
        <div className="card p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <span className="ml-2 text-gray-600 dark:text-gray-300">Dersler yükleniyor...</span>
            </div>
          ) : (
            <div className="table-container">
              <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" className="table-header">Ders</th>
                    <th scope="col" className="table-header">Alan</th>
                    <th scope="col" className="table-header">Kod</th>
                    <th scope="col" className="table-header">Sıra</th>
                    <th scope="col" className="table-header">Durum</th>
                    <th scope="col" className="table-header">İşlemler</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                  {filteredItems.map((subject) => (
                    <tr key={subject.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="table-cell">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                              <BookOpenIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{subject.name}</div>
                            {subject.description && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">{subject.description}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                          {subject.area?.name || 'Bilinmiyor'}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                          {subject.code}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className="text-sm text-gray-900 dark:text-gray-100">{subject.order}</span>
                      </td>
                      <td className="table-cell">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          subject.is_active 
                            ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
                            : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                        }`}>
                          {subject.is_active ? 'Aktif' : 'Pasif'}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => openEditModal(subject)}
                            className="p-1 rounded-md text-primary-600 hover:text-primary-900 dark:hover:text-primary-400"
                            title="Düzenle"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => remove(subject.id)}
                            className="p-1 rounded-md text-red-600 hover:text-red-900 dark:hover:text-red-400"
                            title="Sil"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Modal */}
      <Transition.Root show={showCreateModal} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={setShowCreateModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                  <div>
                    <div className="mt-3 text-center sm:mt-5">
                      <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900 dark:text-white">
                        Yeni Ders Ekle
                      </Dialog.Title>
                      <div className="mt-4 space-y-4">
                        <div>
                          <select
                            className="input-field"
                            value={areaId}
                            onChange={(e) => setAreaId(e.target.value)}
                          >
                            <option value="">Alan Seçin</option>
                            {areas.map((area) => (
                              <option key={area.id} value={area.id}>
                                {area.name} ({area.code})
                              </option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <input
                            type="text"
                            className="input-field"
                            placeholder="Ders Adı (örn: Türkçe)"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                          />
                        </div>
                        <div>
                          <input
                            type="text"
                            className="input-field"
                            placeholder="Ders Kodu (örn: TR)"
                            value={code}
                            onChange={(e) => setCode(e.target.value)}
                          />
                        </div>
                        <div>
                          <textarea
                            className="input-field"
                            placeholder="Açıklama (opsiyonel)"
                            rows={3}
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                          />
                        </div>
                        <div>
                          <input
                            type="number"
                            className="input-field"
                            placeholder="Sıra"
                            value={order}
                            onChange={(e) => setOrder(e.target.value === '' ? '' : Number(e.target.value))}
                          />
                        </div>
                        <div className="flex items-center">
                          <input
                            id="active"
                            type="checkbox"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={isActive}
                            onChange={(e) => setIsActive(e.target.checked)}
                          />
                          <label htmlFor="active" className="ml-2 block text-sm text-gray-900 dark:text-white">
                            Aktif
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                    <button
                      type="button"
                      className="btn-primary w-full sm:col-start-2"
                      onClick={create}
                      disabled={loading || !name || !code || !areaId}
                    >
                      Oluştur
                    </button>
                    <button
                      type="button"
                      className="btn-secondary w-full mt-3 sm:col-start-1 sm:mt-0"
                      onClick={() => setShowCreateModal(false)}
                    >
                      İptal
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Edit Modal */}
      <Transition.Root show={showEditModal} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={setShowEditModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                  <div>
                    <div className="mt-3 text-center sm:mt-5">
                      <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900">
                        Ders Düzenle
                      </Dialog.Title>
                      {editingSubject && (
                        <div className="mt-4 space-y-4">
                          <div>
                            <select
                              className="input-field"
                              value={editingSubject.area_id}
                              onChange={(e) => setEditingSubject({ ...editingSubject, area_id: e.target.value })}
                            >
                              <option value="">Alan Seçin</option>
                              {areas.map((area) => (
                                <option key={area.id} value={area.id}>
                                  {area.name} ({area.code})
                                </option>
                              ))}
                            </select>
                          </div>
                          <div>
                            <input
                              type="text"
                              className="input-field"
                              placeholder="Ders Adı"
                              value={editingSubject.name}
                              onChange={(e) => setEditingSubject({ ...editingSubject, name: e.target.value })}
                            />
                          </div>
                          <div>
                            <input
                              type="text"
                              className="input-field"
                              placeholder="Ders Kodu"
                              value={editingSubject.code}
                              onChange={(e) => setEditingSubject({ ...editingSubject, code: e.target.value })}
                            />
                          </div>
                          <div>
                            <textarea
                              className="input-field"
                              placeholder="Açıklama"
                              rows={3}
                              value={editingSubject.description || ''}
                              onChange={(e) => setEditingSubject({ ...editingSubject, description: e.target.value })}
                            />
                          </div>
                          <div>
                            <input
                              type="number"
                              className="input-field"
                              placeholder="Sıra"
                              value={editingSubject.order}
                              onChange={(e) => setEditingSubject({ ...editingSubject, order: Number(e.target.value) })}
                            />
                          </div>
                          <div className="flex items-center">
                            <input
                              id="edit-active"
                              type="checkbox"
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                              checked={editingSubject.is_active}
                              onChange={(e) => setEditingSubject({ ...editingSubject, is_active: e.target.checked })}
                            />
                            <label htmlFor="edit-active" className="ml-2 block text-sm text-gray-900">
                              Aktif
                            </label>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                    <button
                      type="button"
                      className="btn-primary w-full sm:col-start-2"
                      onClick={saveEdit}
                      disabled={loading}
                    >
                      Kaydet
                    </button>
                    <button
                      type="button"
                      className="btn-secondary w-full mt-3 sm:col-start-1 sm:mt-0"
                      onClick={() => setShowEditModal(false)}
                    >
                      İptal
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </Layout>
  );
};

export default Subjects;
