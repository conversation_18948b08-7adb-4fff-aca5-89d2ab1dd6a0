import httpClient from './httpClient';
import endpoints from './endpoints';

export interface CreateTimelineEntry {
  type: 'study' | 'quiz' | 'progress' | 'achievement' | 'social' | 'custom';
  title: string;
  description?: string;
  visibility: 'public' | 'friends' | 'private';
  metadata?: Record<string, any>;
  content_id?: string;
  quiz_id?: string;
}

export interface UpdateTimelineEntry {
  title?: string;
  description?: string;
  visibility?: 'public' | 'friends' | 'private';
  metadata?: Record<string, any>;
}

export interface TimelineListParams {
  page?: number;
  limit?: number;
  type?: CreateTimelineEntry['type'];
  sort_by?: 'created_at' | 'updated_at' | 'likes_count' | 'comments_count';
  sort_desc?: boolean;
}

export interface CreateCommentReq { content: string }

class TimelineService {
  async getMyTimeline(params?: TimelineListParams) {
    const resp = await httpClient.get(endpoints.timeline.me, { params });
    return resp.data;
  }
  async getFeed(params?: TimelineListParams) {
    const resp = await httpClient.get(endpoints.timeline.feed, { params });
    return resp.data;
    }
  async getPublic(params?: TimelineListParams) {
    const resp = await httpClient.get(endpoints.timeline.public, { params });
    return resp.data;
  }
  async create(data: CreateTimelineEntry) {
    const resp = await httpClient.post(endpoints.timeline.create, data);
    return resp.data;
  }
  async update(id: string, data: UpdateTimelineEntry) {
    const resp = await httpClient.put(endpoints.timeline.update(id), data);
    return resp.data;
  }
  async remove(id: string) {
    const resp = await httpClient.delete(endpoints.timeline.delete(id));
    return resp.data;
  }
  async like(id: string) {
    const resp = await httpClient.post(endpoints.timeline.like(id));
    return resp.data;
  }
  async unlike(id: string) {
    const resp = await httpClient.delete(endpoints.timeline.like(id));
    return resp.data;
  }
  async getComments(id: string, params?: { page?: number; limit?: number; sort_by?: 'created_at' | 'likes_count'; sort_desc?: boolean }) {
    const resp = await httpClient.get(endpoints.timeline.comments(id), { params });
    return resp.data;
  }
  async addComment(id: string, data: CreateCommentReq) {
    const resp = await httpClient.post(endpoints.timeline.comments(id), data);
    return resp.data;
  }
  async updateComment(commentId: string, data: CreateCommentReq) {
    const resp = await httpClient.put(endpoints.timeline.comment(commentId), data);
    return resp.data;
  }
  async deleteComment(commentId: string) {
    const resp = await httpClient.delete(endpoints.timeline.comment(commentId));
    return resp.data;
  }
  async getStats() {
    const resp = await httpClient.get(endpoints.timeline.stats);
    return resp.data;
  }
  async getSummary(params: { start_date: string; end_date: string; type?: CreateTimelineEntry['type'] }) {
    const resp = await httpClient.get(endpoints.timeline.summary, { params });
    return resp.data;
  }
  async getEngagement(id: string) {
    const resp = await httpClient.get(endpoints.timeline.engagement(id));
    return resp.data;
  }
  async getPrivacy() {
    const resp = await httpClient.get(endpoints.timeline.privacy);
    return resp.data;
  }
  async updatePrivacy(data: any) {
    const resp = await httpClient.put(endpoints.timeline.privacy, data);
    return resp.data;
  }
}

const timelineService = new TimelineService();
export default timelineService;

