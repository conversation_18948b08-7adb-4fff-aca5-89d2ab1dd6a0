FROM golang:1.24-alpine AS builder

RUN apk add --no-cache upx
RUN apk --no-cache add tzdata

WORKDIR /src

# Copy only backend sources from the build context root (panel/)
COPY . .

RUN go mod download

RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o kpss-plus main.go
RUN upx kpss-plus


FROM scratch

# take env from build args
ARG VERSION
ENV APP_VERSION=$VERSION
ENV DEV_MODE=true

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

WORKDIR /bin/kpss-plus

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /src/kpss-plus .

# Copy the config file from backend folder (relative to context root)
COPY config.yaml /bin/kpss-plus/

# Copy the frontend files (use backend/dist from context root)
COPY dist/ /bin/kpss-plus/dist/

CMD ["./kpss-plus"]
