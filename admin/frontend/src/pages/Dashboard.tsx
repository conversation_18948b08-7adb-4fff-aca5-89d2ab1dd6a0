import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout';
import { apiGet } from '../lib/api';
import {
  UsersIcon,
  AcademicCapIcon,
  BookOpenIcon,
  DocumentTextIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

interface Stats {
  users: number;
  areas: number;
  subjects: number;
  topics: number;
  contents: number;
  questions: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<Stats>({
    users: 0,
    areas: 0,
    subjects: 0,
    topics: 0,
    contents: 0,
    questions: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      // Load stats from different endpoints
      const [usersRes, areasRes, subjectsRes, topicsRes, contentsRes, questionsRes] = await Promise.allSettled([
        apiGet<{ data: any[] }>('/users'),
        apiGet<{ data: any[] }>('/areas'),
        apiGet<{ data: any[] }>('/subjects'),
        apiGet<{ data: any[]; total: number }>('/topics?limit=1'),
        apiGet<{ data: any[]; total: number }>('/contents?limit=1'),
        apiGet<{ data: any[]; total: number }>('/questions?limit=1')
      ]);

      setStats({
        users: usersRes.status === 'fulfilled' ? usersRes.value.data?.length || 0 : 0,
        areas: areasRes.status === 'fulfilled' ? areasRes.value.data?.length || 0 : 0,
        subjects: subjectsRes.status === 'fulfilled' ? subjectsRes.value.data?.length || 0 : 0,
        topics: topicsRes.status === 'fulfilled' ? topicsRes.value.total || 0 : 0,
        contents: contentsRes.status === 'fulfilled' ? contentsRes.value.total || 0 : 0,
        questions: questionsRes.status === 'fulfilled' ? questionsRes.value.total || 0 : 0
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      name: 'Kullanıcılar',
      value: stats.users,
      icon: UsersIcon,
      gradient: 'from-blue-500 to-cyan-500',
      iconBg: 'bg-blue-500/20',
      href: '/users'
    },
    {
      name: 'Alanlar',
      value: stats.areas,
      icon: AcademicCapIcon,
      gradient: 'from-emerald-500 to-teal-500',
      iconBg: 'bg-emerald-500/20',
      href: '/areas'
    },
    {
      name: 'Dersler',
      value: stats.subjects,
      icon: BookOpenIcon,
      gradient: 'from-purple-500 to-pink-500',
      iconBg: 'bg-purple-500/20',
      href: '/subjects'
    },
    {
      name: 'Konular',
      value: stats.topics,
      icon: BookOpenIcon,
      gradient: 'from-amber-500 to-orange-500',
      iconBg: 'bg-amber-500/20',
      href: '/topics'
    },
    {
      name: 'İçerikler',
      value: stats.contents,
      icon: DocumentTextIcon,
      gradient: 'from-rose-500 to-red-500',
      iconBg: 'bg-rose-500/20',
      href: '/contents'
    },
    {
      name: 'Sorular',
      value: stats.questions,
      icon: QuestionMarkCircleIcon,
      gradient: 'from-indigo-500 to-purple-500',
      iconBg: 'bg-indigo-500/20',
      href: '/questions'
    }
  ];

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="mt-4 text-lg text-slate-300 max-w-2xl mx-auto">
            KPSS Plus yönetim paneline hoş geldiniz. Sistem istatistiklerinizi ve hızlı işlemlerinizi buradan takip edebilirsiniz.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {statCards.map((card) => {
            const Icon = card.icon;
            return (
              <div
                key={card.name}
                className="group relative bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6 hover:bg-slate-800/80 transition-all duration-300 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/10"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-400 uppercase tracking-wider">
                      {card.name}
                    </p>
                    <p className="mt-2 text-3xl font-bold text-white">
                      {loading ? (
                        <div className="animate-pulse bg-slate-700 h-8 w-20 rounded-lg"></div>
                      ) : (
                        <span className={`bg-gradient-to-r ${card.gradient} bg-clip-text text-transparent`}>
                          {card.value.toLocaleString()}
                        </span>
                      )}
                    </p>
                  </div>
                  <div className={`${card.iconBg} p-3 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className={`h-8 w-8 bg-gradient-to-r ${card.gradient} bg-clip-text text-transparent`} />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <span className="text-slate-400">Son güncelleme: Az önce</span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Hızlı İşlemler
            </h3>
            <p className="mt-2 text-slate-400">En sık kullanılan işlemlere hızlı erişim</p>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <a
              href="/areas"
              className="group relative bg-slate-700/50 backdrop-blur-sm p-6 rounded-xl border border-slate-600/50 hover:border-emerald-500/50 hover:bg-slate-700/70 transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <AcademicCapIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Alan Ekle
                </h3>
                <p className="text-sm text-slate-400">
                  Yeni KPSS alanı oluşturun
                </p>
              </div>
            </a>

            <a
              href="/subjects"
              className="group relative bg-slate-700/50 backdrop-blur-sm p-6 rounded-xl border border-slate-600/50 hover:border-purple-500/50 hover:bg-slate-700/70 transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <BookOpenIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Ders Ekle
                </h3>
                <p className="text-sm text-slate-400">
                  Yeni ders oluşturun
                </p>
              </div>
            </a>

            <a
              href="/contents"
              className="group relative bg-slate-700/50 backdrop-blur-sm p-6 rounded-xl border border-slate-600/50 hover:border-rose-500/50 hover:bg-slate-700/70 transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-rose-500 to-red-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <DocumentTextIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  İçerik Ekle
                </h3>
                <p className="text-sm text-slate-400">
                  Yeni içerik oluşturun
                </p>
              </div>
            </a>

            <a
              href="/questions"
              className="group relative bg-slate-700/50 backdrop-blur-sm p-6 rounded-xl border border-slate-600/50 hover:border-indigo-500/50 hover:bg-slate-700/70 transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <QuestionMarkCircleIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Soru Ekle
                </h3>
                <p className="text-sm text-slate-400">
                  Yeni soru oluşturun
                </p>
              </div>
            </a>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
