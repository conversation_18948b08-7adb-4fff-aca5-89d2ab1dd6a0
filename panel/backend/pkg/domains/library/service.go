package library

import "github.com/kpss-plus-backend/pkg/dtos"

type Service interface {
    CreateFolder(userID string, name string) (*dtos.FolderResponse, error)
    ListFolders(userID string) (*dtos.FoldersResponse, error)
    UpdateFolder(userID string, folderID string, name string) (*dtos.FolderResponse, error)
    DeleteFolder(userID string, folderID string) error

    AddItem(userID string, itemType string, itemID string, folderID *string) (*dtos.LibraryItemResponse, error)
    RemoveItem(userID string, itemType string, itemID string) error
    List(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListResponse, error)
    ListWithDetails(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListWithDetailsResponse, error)
}

type service struct{ repo Repository }

func NewService(r Repository) Service { return &service{repo: r} }

func (s *service) CreateFolder(userID string, name string) (*dtos.FolderResponse, error) {
    return s.repo.CreateFolder(userID, name)
}

func (s *service) ListFolders(userID string) (*dtos.FoldersResponse, error) {
    return s.repo.ListFolders(userID)
}

func (s *service) UpdateFolder(userID string, folderID string, name string) (*dtos.FolderResponse, error) {
    return s.repo.UpdateFolder(userID, folderID, name)
}

func (s *service) DeleteFolder(userID string, folderID string) error {
    return s.repo.DeleteFolder(userID, folderID)
}

func (s *service) AddItem(userID string, itemType string, itemID string, folderID *string) (*dtos.LibraryItemResponse, error) {
    return s.repo.AddItem(userID, itemType, itemID, folderID)
}

func (s *service) RemoveItem(userID string, itemType string, itemID string) error {
    return s.repo.RemoveItem(userID, itemType, itemID)
}

func (s *service) List(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListResponse, error) {
    return s.repo.List(userID, req)
}

func (s *service) ListWithDetails(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListWithDetailsResponse, error) {
    return s.repo.ListWithDetails(userID, req)
}
