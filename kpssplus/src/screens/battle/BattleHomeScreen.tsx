import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import {Card} from '../../components/ui/Card';
import {Button} from '../../components/ui/Button';
import {Icon} from '../../components/ui';
import {colors} from '../../theme/colors';
import {typography} from '../../theme/typography';
import {spacing} from '../../theme/spacing';
import {useBattle, useAuth} from '../../hooks';
import {StyleSheet} from 'react-native';

type Props = {};

export const BattleHomeScreen: React.FC<Props> = () => {
  const {isAuthenticated, isGuest, user} = useAuth();
  const {groupBattles, friendChallenges, battleStats, refreshData} =
    useBattle();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      refreshData();
    }
  }, [isAuthenticated, refreshData]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshData();
    setRefreshing(false);
  };

  const handleGroupBattlePress = () => {
    console.log('Navigate to GroupBattle');
  };

  const handleFriendChallengePress = () => {
    console.log('Navigate to FriendChallenge');
  };

  const handleBattleStatsPress = () => {
    console.log('Navigate to BattleStats');
  };

  if (isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.guestContainer}>
          <Icon name="trophy-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.guestTitle}>Kapışma Merkezi</Text>
          <Text style={styles.guestDescription}>
            Arkadaşlarınızla grup kapışması yapın, 1v1 meydan okuma gönderin ve
            rekabetçi quiz deneyimi yaşayın.
          </Text>
          <Button
            title="Giriş Yap"
            onPress={() => console.log('Navigate to Login')}
            style={styles.loginButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  const activeBattles = groupBattles.filter(
    b => b.status === 'active' || b.status === 'waiting',
  );
  const pendingChallenges = friendChallenges.filter(
    c => c.status === 'pending' && c.challengedId === user?.id,
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Kapışma Merkezi</Text>
          <Text style={styles.headerSubtitle}>
            Arkadaşlarınızla rekabet edin ve yeteneklerinizi test edin!
          </Text>
        </View>

        {/* Quick Stats */}
        {battleStats && (
          <Card style={styles.statsCard} variant="elevated">
            <View style={styles.statsHeader}>
              <Text style={styles.statsTitle}>İstatistiklerim</Text>
              <TouchableOpacity onPress={handleBattleStatsPress}>
                <Icon
                  name="chevron-forward-outline"
                  size={20}
                  color={colors.primary}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {battleStats.totalGroupBattles}
                </Text>
                <Text style={styles.statLabel}>Grup Kapışması</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {battleStats.totalFriendChallenges}
                </Text>
                <Text style={styles.statLabel}>Arkadaş Meydan Okuması</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>%{battleStats.winRate}</Text>
                <Text style={styles.statLabel}>Kazanma Oranı</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>#{battleStats.bestRank}</Text>
                <Text style={styles.statLabel}>En İyi Sıralama</Text>
              </View>
            </View>
          </Card>
        )}

        {/* Notifications */}
        {(pendingChallenges.length > 0 || activeBattles.length > 0) && (
          <Card style={styles.notificationCard} variant="outlined">
            <View style={styles.notificationHeader}>
              <Icon
                name="notifications-outline"
                size={24}
                color={colors.warning}
              />
              <Text style={styles.notificationTitle}>Bekleyen Aktiviteler</Text>
            </View>

            {pendingChallenges.length > 0 && (
              <TouchableOpacity
                style={styles.notificationItem}
                onPress={handleFriendChallengePress}>
                <Text style={styles.notificationText}>
                  {pendingChallenges.length} yeni meydan okuma
                </Text>
                <Icon
                  name="chevron-forward-outline"
                  size={16}
                  color={colors.text.secondary}
                />
              </TouchableOpacity>
            )}

            {activeBattles.length > 0 && (
              <TouchableOpacity
                style={styles.notificationItem}
                onPress={handleGroupBattlePress}>
                <Text style={styles.notificationText}>
                  {activeBattles.length} aktif grup kapışması
                </Text>
                <Icon
                  name="chevron-forward-outline"
                  size={16}
                  color={colors.text.secondary}
                />
              </TouchableOpacity>
            )}
          </Card>
        )}

        {/* Main Actions */}
        <View style={styles.actionsGrid}>
          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={handleGroupBattlePress}>
            <View style={styles.actionIcon}>
              <Icon name="people-outline" size={32} color={colors.primary} />
            </View>
            <Text style={styles.actionTitle}>Grup Kapışması</Text>
            <Text style={styles.actionDescription}>
              Arkadaşlarınızla grup halinde quiz çözün ve kapışın
            </Text>
            <View style={styles.actionStats}>
              <Text style={styles.actionStatsText}>
                {activeBattles.length} aktif kapışma
              </Text>
            </View>
          </Card>

          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={handleFriendChallengePress}>
            <View style={styles.actionIcon}>
              <Icon name="flash-outline" size={32} color={colors.secondary} />
            </View>
            <Text style={styles.actionTitle}>Arkadaş Kapışması</Text>
            <Text style={styles.actionDescription}>
              1v1 meydan okuma gönderin ve kabul edin
            </Text>
            <View style={styles.actionStats}>
              <Text style={styles.actionStatsText}>
                {pendingChallenges.length} bekleyen meydan okuma
              </Text>
            </View>
          </Card>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Son Aktiviteler</Text>

          {/* Recent Group Battles */}
          {activeBattles.slice(0, 2).map(battle => (
            <Card
              key={battle.id}
              style={styles.activityCard}
              variant="outlined"
              onPress={() => console.log('Navigation disabled')}>
              <View style={styles.activityHeader}>
                <Icon name="people-outline" size={20} color={colors.primary} />
                <Text style={styles.activityTitle}>{battle.name}</Text>
                <View
                  style={[
                    styles.activityStatus,
                    styles[`status${battle.status}`],
                  ]}>
                  <Text
                    style={[
                      styles.activityStatusText,
                      styles[`statusText${battle.status}`],
                    ]}>
                    {battle.status === 'waiting' ? 'Bekliyor' : 'Aktif'}
                  </Text>
                </View>
              </View>
              <Text style={styles.activityDescription}>
                {battle.currentParticipants}/{battle.maxParticipants} katılımcı
                • {battle.quiz.title}
              </Text>
            </Card>
          ))}

          {/* Recent Friend Challenges */}
          {friendChallenges.slice(0, 2).map(challenge => (
            <Card
              key={challenge.id}
              style={styles.activityCard}
              variant="outlined"
              onPress={() =>
                console.log('Navigate to ChallengeDetail', challenge.id)
              }>
              <View style={styles.activityHeader}>
                <Icon name="flash-outline" size={20} color={colors.secondary} />
                <Text style={styles.activityTitle}>
                  {challenge.challengerId === user?.id
                    ? `${challenge.challenged.firstName}'e meydan okuma`
                    : `${challenge.challenger.firstName}'den meydan okuma`}
                </Text>
                <View
                  style={[
                    styles.activityStatus,
                    styles[`status${challenge.status}`],
                  ]}>
                  <Text
                    style={[
                      styles.activityStatusText,
                      styles[`statusText${challenge.status}`],
                    ]}>
                    {challenge.status === 'pending' && 'Bekliyor'}
                    {challenge.status === 'accepted' && 'Kabul Edildi'}
                    {challenge.status === 'completed' && 'Tamamlandı'}
                  </Text>
                </View>
              </View>
              <Text style={styles.activityDescription}>
                {challenge.quiz.title} • {challenge.quiz.questionCount} soru
              </Text>
            </Card>
          ))}

          {activeBattles.length === 0 && friendChallenges.length === 0 && (
            <Card style={styles.emptyCard} variant="outlined">
              <Icon name="trophy-outline" size={48} color={colors.gray[400]} />
              <Text style={styles.emptyTitle}>Henüz aktivite yok</Text>
              <Text style={styles.emptyDescription}>
                İlk kapışmanızı başlatın ve rekabete katılın!
              </Text>
            </Card>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Button
            title="Hızlı Grup Oluştur"
            variant="outline"
            onPress={() => console.log('Navigation disabled')}
            style={styles.quickActionButton}
          />
          <Button
            title="Arkadaş Davet Et"
            variant="outline"
            onPress={() => console.log('Navigation disabled')}
            style={styles.quickActionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    padding: spacing[4],
  },
  guestContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing[6],
  },
  guestTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  guestDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[6],
    lineHeight: 24,
  },
  loginButton: {
    minWidth: 120,
  },
  header: {
    marginBottom: spacing[6],
  },
  headerTitle: {
    ...typography.h3,
    marginBottom: spacing[1],
  },
  headerSubtitle: {
    ...typography.body1,
    color: colors.text.secondary,
  },
  statsCard: {
    marginBottom: spacing[4],
    padding: spacing[4],
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  statsTitle: {
    ...typography.h6,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    ...typography.h5,
    color: colors.primary,
    fontWeight: 'bold',
    marginBottom: spacing[1],
  },
  statLabel: {
    ...typography.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  notificationCard: {
    marginBottom: spacing[4],
    padding: spacing[4],
    borderLeftWidth: 4,
    borderLeftColor: colors.warning,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  notificationTitle: {
    ...typography.body1,
    fontWeight: '600',
    marginLeft: spacing[2],
  },
  notificationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[1],
  },
  notificationText: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  actionsGrid: {
    flexDirection: 'row',
    gap: spacing[3],
    marginBottom: spacing[6],
  },
  actionCard: {
    flex: 1,
    padding: spacing[4],
    alignItems: 'center',
  },
  actionIcon: {
    marginBottom: spacing[3],
  },
  actionTitle: {
    ...typography.h6,
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  actionDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[3],
  },
  actionStats: {
    backgroundColor: colors.primaryMuted,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 12,
  },
  actionStatsText: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: '600',
  },
  section: {
    marginBottom: spacing[6],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  activityCard: {
    marginBottom: spacing[2],
    padding: spacing[3],
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  activityTitle: {
    ...typography.body1,
    fontWeight: '600',
    flex: 1,
    marginLeft: spacing[2],
  },
  activityStatus: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[0.5],
    borderRadius: 8,
  },
  statuswaiting: {
    backgroundColor: colors.warning + '20',
  },
  statusactive: {
    backgroundColor: colors.success + '20',
  },
  statuspending: {
    backgroundColor: colors.warning + '20',
  },
  statusaccepted: {
    backgroundColor: colors.success + '20',
  },
  statuscompleted: {
    backgroundColor: colors.primary + '20',
  },
  activityStatusText: {
    ...typography.caption,
    fontWeight: '600',
  },
  statusTextwaiting: {
    color: colors.warning,
  },
  statusTextactive: {
    color: colors.success,
  },
  statusTextpending: {
    color: colors.warning,
  },
  statusTextaccepted: {
    color: colors.success,
  },
  statusTextcompleted: {
    color: colors.primary,
  },
  activityDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    marginLeft: spacing[6],
  },
  emptyCard: {
    alignItems: 'center',
    padding: spacing[6],
  },
  emptyTitle: {
    ...typography.h6,
    marginTop: spacing[3],
    marginBottom: spacing[1],
  },
  emptyDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    gap: spacing[3],
    marginBottom: spacing[4],
  },
  quickActionButton: {
    flex: 1,
  },
  // Additional status styles
  statuscancelled: {
    backgroundColor: colors.error + '20',
  },
  statusdeclined: {
    backgroundColor: colors.error + '20',
  },
  statusexpired: {
    backgroundColor: colors.gray[500] + '20',
  },
  // Additional status text styles
  statusTextcancelled: {
    color: colors.error,
  },
  statusTextdeclined: {
    color: colors.error,
  },
  statusTextexpired: {
    color: colors.gray[500],
  },
});
