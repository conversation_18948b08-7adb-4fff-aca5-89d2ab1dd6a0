package library

import (
    "errors"
    "math"
    "strings"

    "github.com/google/uuid"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/entities"
    "gorm.io/gorm"
)

type Repository interface {
    CreateFolder(userID string, name string) (*dtos.FolderResponse, error)
    ListFolders(userID string) (*dtos.FoldersResponse, error)
    UpdateFolder(userID string, folderID string, name string) (*dtos.FolderResponse, error)
    DeleteFolder(userID string, folderID string) error

    AddItem(userID string, itemType string, itemID string, folderID *string) (*dtos.LibraryItemResponse, error)
    RemoveItem(userID string, itemType string, itemID string) error
    List(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListResponse, error)
    ListWithDetails(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListWithDetailsResponse, error)
}

type repository struct{ db *gorm.DB }

func NewRepo(db *gorm.DB) Repository { return &repository{db: db} }

func (r *repository) CreateFolder(userID string, name string) (*dtos.FolderResponse, error) {
    uid := uuid.MustParse(userID)
    rec := entities.LibraryFolder{UserID: &uid, Name: name}
    if err := r.db.Create(&rec).Error; err != nil { return nil, err }
    return &dtos.FolderResponse{ID: rec.ID, Name: rec.Name}, nil
}

func (r *repository) ListFolders(userID string) (*dtos.FoldersResponse, error) {
    var rows []entities.LibraryFolder
    if err := r.db.Where("user_id = ?", userID).Order("created_at ASC").Find(&rows).Error; err != nil { return nil, err }
    out := make([]dtos.FolderResponse, len(rows))
    for i, f := range rows { out[i] = dtos.FolderResponse{ID: f.ID, Name: f.Name} }
    return &dtos.FoldersResponse{Folders: out}, nil
}

func (r *repository) UpdateFolder(userID string, folderID string, name string) (*dtos.FolderResponse, error) {
    var f entities.LibraryFolder
    if err := r.db.Where("id = ? AND user_id = ?", folderID, userID).First(&f).Error; err != nil { return nil, err }
    if err := r.db.Model(&f).Update("name", name).Error; err != nil { return nil, err }
    return &dtos.FolderResponse{ID: f.ID, Name: name}, nil
}

func (r *repository) DeleteFolder(userID string, folderID string) error {
    // Null folder references for items in this folder
    _ = r.db.Model(&entities.LibraryItem{}).Where("user_id = ? AND folder_id = ?", userID, folderID).Update("folder_id", nil).Error
    return r.db.Where("id = ? AND user_id = ?", folderID, userID).Delete(&entities.LibraryFolder{}).Error
}

func (r *repository) AddItem(userID string, itemType string, itemID string, folderID *string) (*dtos.LibraryItemResponse, error) {
    itemType = strings.ToLower(itemType)
    if itemType != "content" && itemType != "topic" && itemType != "quiz" { return nil, errors.New("invalid item type") }
    uid := uuid.MustParse(userID)
    iid := uuid.MustParse(itemID)
    var fid *uuid.UUID
    if folderID != nil && *folderID != "" { tmp := uuid.MustParse(*folderID); fid = &tmp }
    rec := entities.LibraryItem{UserID: &uid, ItemType: itemType, ItemID: &iid, FolderID: fid}
    // upsert-like: avoid duplicates
    var existing entities.LibraryItem
    err := r.db.Where("user_id = ? AND item_type = ? AND item_id = ?", uid, itemType, iid).First(&existing).Error
    if errors.Is(err, gorm.ErrRecordNotFound) {
        if err := r.db.Create(&rec).Error; err != nil { return nil, err }
        return &dtos.LibraryItemResponse{ID: rec.ID, ItemType: rec.ItemType, ItemID: *rec.ItemID, FolderID: rec.FolderID}, nil
    } else if err != nil {
        return nil, err
    }
    // update folder only
    if err := r.db.Model(&existing).Update("folder_id", fid).Error; err != nil { return nil, err }
    return &dtos.LibraryItemResponse{ID: existing.ID, ItemType: existing.ItemType, ItemID: *existing.ItemID, FolderID: existing.FolderID}, nil
}

func (r *repository) RemoveItem(userID string, itemType string, itemID string) error {
    return r.db.Where("user_id = ? AND item_type = ? AND item_id = ?", userID, strings.ToLower(itemType), itemID).Delete(&entities.LibraryItem{}).Error
}

func (r *repository) List(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListResponse, error) {
    page := req.Page; if page < 1 { page = 1 }
    limit := req.Limit; if limit < 1 || limit > 100 { limit = 20 }
    offset := (page-1)*limit
    q := r.db.Model(&entities.LibraryItem{}).Where("user_id = ?", userID)
    if req.Type != "" { q = q.Where("item_type = ?", strings.ToLower(req.Type)) }
    if req.FolderID != nil { q = q.Where("folder_id = ?", *req.FolderID) }
    var total int64
    q.Count(&total)
    var rows []entities.LibraryItem
    if err := q.Offset(offset).Limit(limit).Order("created_at DESC").Find(&rows).Error; err != nil { return nil, err }
    items := make([]dtos.LibraryItemResponse, len(rows))
    for i, rrow := range rows { items[i] = dtos.LibraryItemResponse{ID: rrow.ID, ItemType: rrow.ItemType, ItemID: *rrow.ItemID, FolderID: rrow.FolderID} }
    tp := int(math.Ceil(float64(total)/float64(limit)))
    return &dtos.LibraryListResponse{Items: items, Total: int(total), Page: page, Limit: limit, TotalPages: tp}, nil
}

func (r *repository) ListWithDetails(userID string, req *dtos.GetLibraryRequest) (*dtos.LibraryListWithDetailsResponse, error) {
    base, err := r.List(userID, req)
    if err != nil { return nil, err }
    details := map[string]dtos.LibraryItemDetail{}
    // collect IDs by type
    var contentIDs, topicIDs, quizIDs []uuid.UUID
    for _, it := range base.Items {
        switch it.ItemType {
        case "content": contentIDs = append(contentIDs, it.ItemID)
        case "topic": topicIDs = append(topicIDs, it.ItemID)
        case "quiz": quizIDs = append(quizIDs, it.ItemID)
        }
    }
    if len(contentIDs) > 0 {
        var rows []entities.Content
        _ = r.db.Where("id IN ?", contentIDs).Find(&rows).Error
        for _, c := range rows {
            details[c.ID.String()] = dtos.LibraryItemDetail{Content: &dtos.LibraryDetailContent{ID: c.ID, Title: c.Title, Type: string(c.Type), Subject: c.Subject}}
        }
    }
    if len(topicIDs) > 0 {
        var rows []entities.Topic
        _ = r.db.Where("id IN ?", topicIDs).Find(&rows).Error
        for _, t := range rows {
            details[t.ID.String()] = dtos.LibraryItemDetail{Topic: &dtos.LibraryDetailTopic{ID: t.ID, Title: t.Title, Subject: t.Subject}}
        }
    }
    if len(quizIDs) > 0 {
        var rows []entities.Quiz
        _ = r.db.Where("id IN ?", quizIDs).Find(&rows).Error
        for _, q := range rows {
            details[q.ID.String()] = dtos.LibraryItemDetail{Quiz: &dtos.LibraryDetailQuiz{ID: q.ID, Title: q.Title, Difficulty: q.Difficulty, TimeLimit: q.TimeLimit}}
        }
    }
    return &dtos.LibraryListWithDetailsResponse{Items: base.Items, Details: details, Total: base.Total, Page: base.Page, Limit: base.Limit, TotalPages: base.TotalPages}, nil
}
