package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/pkg/domains/progress"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/middleware"
	"github.com/kpss-plus-backend/pkg/state"
)

func ProgressRoutes(r *gin.RouterGroup, progressService progress.Service) {
	progressGroup := r.Group("/progress")
	progressGroup.Use(middleware.Authorized()) // All progress routes require authentication

	// Progress tracking
	progressGroup.PUT("", UpdateUserProgress(progressService))
	progressGroup.GET("/content/:contentId", GetUserContentProgress(progressService))
	progressGroup.GET("", GetAllUserProgress(progressService))

	// Progress analytics
	progressGroup.GET("/stats", GetProgressStats(progressService))
	progressGroup.GET("/history", GetProgressHistory(progressService))
	progressGroup.GET("/streak", GetStudyStreak(progressService))

	// Study sessions
	progressGroup.POST("/sessions", StartStudySession(progressService))
	progressGroup.PUT("/sessions/:sessionId/end", EndStudySession(progressService))
	progressGroup.GET("/sessions", GetStudySessions(progressService))

	// Progress goals
	progressGroup.POST("/goals", SetProgressGoal(progressService))
	progressGroup.GET("/goals", GetProgressGoals(progressService))
	progressGroup.PUT("/goals/:goalId", UpdateProgressGoal(progressService))
	progressGroup.DELETE("/goals/:goalId", DeleteProgressGoal(progressService))

	// Achievements
	progressGroup.GET("/achievements", GetProgressAchievements(progressService))
}

// Progress tracking handlers
func UpdateUserProgress(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.UpdateProgressRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateProgress(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetUserContentProgress(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		contentID := c.Param("contentId")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetProgress(userID, contentID)
		if err != nil {
			c.AbortWithStatusJSON(404, gin.H{
				"error":  err.Error(),
				"status": 404,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetAllUserProgress(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetProgressListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetUserProgress(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Progress analytics handlers
func GetProgressStats(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		resp, err := s.GetProgressStats(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetProgressHistory(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetProgressHistoryRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetProgressHistory(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetStudyStreak(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		resp, err := s.GetStudyStreak(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Study session handlers
func StartStudySession(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.StartStudySessionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.StartStudySession(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func EndStudySession(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		sessionID := c.Param("sessionId")
		if sessionID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Session ID is required",
				"status": 400,
			})
			return
		}

		resp, err := s.EndStudySession(userID, sessionID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetStudySessions(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetStudySessionsRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetStudySessions(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Progress goals handlers
func SetProgressGoal(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.SetProgressGoalRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.SetProgressGoal(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func GetProgressGoals(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		resp, err := s.GetProgressGoals(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func UpdateProgressGoal(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		goalID := c.Param("goalId")
		if goalID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Goal ID is required",
				"status": 400,
			})
			return
		}

		var req dtos.UpdateProgressGoalRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateProgressGoal(userID, goalID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func DeleteProgressGoal(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		goalID := c.Param("goalId")
		if goalID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Goal ID is required",
				"status": 400,
			})
			return
		}

		err := s.DeleteProgressGoal(userID, goalID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Progress goal deleted successfully",
			"status":  200,
		})
	}
}

// Achievement handlers
func GetProgressAchievements(s progress.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		resp, err := s.GetProgressAchievements(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
