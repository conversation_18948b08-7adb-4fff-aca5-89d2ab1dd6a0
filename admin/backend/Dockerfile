FROM golang:1.24-alpine AS builder

RUN apk add --no-cache upx
RUN apk --no-cache add tzdata

WORKDIR /src

COPY . .

RUN go mod download

RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o kpss-plus-admin main.go
RUN upx kpss-plus-admin


FROM scratch

# take env from build args
ARG VERSION
ENV APP_VERSION=$VERSION
ENV DEV_MODE=true

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

WORKDIR /bin/kpss-plus-admin

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /src/kpss-plus-admin .

# Copy the config file
COPY config.yaml /bin/kpss-plus-admin/

# Copy the frontend files
COPY dist/ /bin/kpss-plus-admin/dist/

CMD ["./kpss-plus-admin"]
