import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {ModalStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {LoginModalScreen} from '../screens/modals/LoginModalScreen';
import {GuestLimitModalScreen} from '../screens/modals/GuestLimitModalScreen';
import {ShareModalScreen} from '../screens/modals/ShareModalScreen';
import {FilterModalScreen} from '../screens/modals/FilterModalScreen';
import {SearchModalScreen} from '../screens/modals/SearchModalScreen';

const Stack = createStackNavigator<ModalStackParamList>();

export const ModalStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.white,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackTitle: '',
        cardStyle: {
          backgroundColor: colors.background,
        },
        presentation: 'modal',
      }}>
      <Stack.Screen
        name="LoginModal"
        component={LoginModalScreen}
        options={{
          title: 'Giriş Yapın',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="GuestLimitModal"
        component={GuestLimitModalScreen}
        options={{
          title: 'Limit Aşıldı',
        }}
      />
      <Stack.Screen
        name="ShareModal"
        component={ShareModalScreen}
        options={{
          title: 'Paylaş',
        }}
      />
      <Stack.Screen
        name="FilterModal"
        component={FilterModalScreen}
        options={{
          title: 'Filtrele',
        }}
      />
      <Stack.Screen
        name="SearchModal"
        component={SearchModalScreen}
        options={{
          title: 'Ara',
        }}
      />
    </Stack.Navigator>
  );
};
