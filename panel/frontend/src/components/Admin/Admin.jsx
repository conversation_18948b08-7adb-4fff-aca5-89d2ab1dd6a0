import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { Login } from './Login';
import { Dashboard } from './Dashboard';
import { Users } from './Users';
import { StudyContent } from './StudyContent';
import { Quizzes } from './Quizzes';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { AdminRoute } from './AdminRoute';
import './Admin.css';

const AdminPanel = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const token = localStorage.getItem('adminToken');
  const user = localStorage.getItem('adminUser');

  // If not authenticated, redirect to login page
  if (!token || !user) {
    // Check if current path is not already /admin/login to avoid redirect loops
    if (window.location.pathname !== '/admin/login') {
      // Pass the current location in state so we can redirect back after login
      navigate('/admin/login', { 
        state: { from: location },
        replace: true 
      });
      return null; // Don't render anything while redirecting
    }
    return <Login />; // Only render login if we're already on the login page
  }

  // If authenticated, show admin panel
  return (
    <div className="admin-panel">
      <Sidebar />
      <div className="main-content">
        <Header />
        <div className="content">
          <Routes>
            <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
            <Route path="/dashboard" element={
              <AdminRoute>
                <Dashboard />
              </AdminRoute>
            } />
            <Route path="/users" element={
              <AdminRoute>
                <Users />
              </AdminRoute>
            } />
            <Route path="/study-content" element={
              <AdminRoute>
                <StudyContent />
              </AdminRoute>
            } />
            <Route path="/quizzes" element={
              <AdminRoute>
                <Quizzes />
              </AdminRoute>
            } />
            <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
          </Routes>
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;