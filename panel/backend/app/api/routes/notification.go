package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/kpss-plus-backend/pkg/domains/notification"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/middleware"
    "github.com/kpss-plus-backend/pkg/state"
)

func NotificationRoutes(r *gin.RouterGroup, s notification.Service) {
    grp := r.Group("/notifications")
    grp.Use(middleware.Authorized())

    // List notifications
    grp.GET("", GetNotifications(s))

    // Unread count
    grp.GET("/unread", GetUnreadCount(s))

    // Mark as read
    grp.PUT("/:id/read", MarkAsRead(s))

    // Mark all as read
    grp.PUT("/read-all", MarkAllAsRead(s))

    // Delete notification
    grp.DELETE("/:id", DeleteNotification(s))

    // Preferences
    grp.GET("/settings", GetNotificationPreferences(s))
    grp.PUT("/settings", UpdateNotificationPreferences(s))
}

func GetNotifications(s notification.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" {
            c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401})
            return
        }

        var req dtos.GetNotificationsRequest
        if err := c.ShouldBindQuery(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }

        resp, err := s.GetUserNotifications(userID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func GetUnreadCount(s notification.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" {
            c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401})
            return
        }

        resp, err := s.GetUnreadNotificationCount(userID)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func MarkAsRead(s notification.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" {
            c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401})
            return
        }

        id := c.Param("id")
        if id == "" {
            c.AbortWithStatusJSON(400, gin.H{"error": "Notification ID is required", "status": 400})
            return
        }

        if err := s.MarkNotificationAsRead(userID, id); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "Notification marked as read"}, "status": 200})
    }
}

func MarkAllAsRead(s notification.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" {
            c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401})
            return
        }

        if err := s.MarkAllNotificationsAsRead(userID); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "All notifications marked as read"}, "status": 200})
    }
}

func DeleteNotification(s notification.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" {
            c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401})
            return
        }

        id := c.Param("id")
        if id == "" {
            c.AbortWithStatusJSON(400, gin.H{"error": "Notification ID is required", "status": 400})
            return
        }

        if err := s.DeleteNotification(userID, id); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "Notification deleted"}, "status": 200})
    }
}

func GetNotificationPreferences(s notification.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" {
            c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401})
            return
        }

        resp, err := s.GetNotificationPreferences(userID)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func UpdateNotificationPreferences(s notification.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" {
            c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401})
            return
        }

        var req dtos.UpdateNotificationPreferencesRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }

        resp, err := s.UpdateNotificationPreferences(userID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}
