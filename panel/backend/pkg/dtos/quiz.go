package dtos

import (
	"time"

	"github.com/google/uuid"
)

// Quiz Management DTOs
type CreateQuizRequest struct {
	Title       string  `json:"title" validate:"required,min=1,max=200"`
	Description *string `json:"description" validate:"omitempty,max=1000"`
	Subject     *string `json:"subject" validate:"omitempty,max=100"`
	Difficulty  *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	TimeLimit   *int    `json:"time_limit" validate:"omitempty,min=60,max=7200"` // 1 minute to 2 hours
	IsPublic    bool    `json:"is_public"`
	Type        string  `json:"type" validate:"required,oneof=personal shared official"`
}

type UpdateQuizRequest struct {
	Title       *string `json:"title" validate:"omitempty,min=1,max=200"`
	Description *string `json:"description" validate:"omitempty,max=1000"`
	Subject     *string `json:"subject" validate:"omitempty,max=100"`
	Difficulty  *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	TimeLimit   *int    `json:"time_limit" validate:"omitempty,min=60,max=7200"`
	IsPublic    *bool   `json:"is_public"`
	IsActive    *bool   `json:"is_active"`
}

type QuizResponse struct {
	ID          uuid.UUID `json:"id"`
	Title       string    `json:"title"`
	Description *string   `json:"description"`
	Type        string    `json:"type"`
	Subject     *string   `json:"subject"`
	Difficulty  *string   `json:"difficulty"`
	TimeLimit   *int      `json:"time_limit"`
	IsPublic    bool      `json:"is_public"`
	IsActive    bool      `json:"is_active"`
	CreatorID   string    `json:"creator_id"`

	// Statistics
	QuestionCount  int     `json:"question_count"`
	AttemptCount   int     `json:"attempt_count"`
	AverageScore   float64 `json:"average_score"`
	CompletionRate float64 `json:"completion_rate"`

	// User-specific data (if user is authenticated)
	UserBestScore *float64   `json:"user_best_score"`
	UserAttempts  int        `json:"user_attempts"`
	LastAttemptAt *time.Time `json:"last_attempt_at"`

	// Creator info
	Creator UserInfo `json:"creator"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type QuizDetailResponse struct {
	QuizResponse
	Questions []QuestionResponse `json:"questions"`
}

type GetQuizListRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=created_at updated_at title popularity difficulty"`
	SortDesc bool   `json:"sort_desc"`

	// Filters
	Type       *string `json:"type" validate:"omitempty,oneof=personal shared official"`
	Subject    *string `json:"subject"`
	Difficulty *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	IsPublic   *bool   `json:"is_public"`
	CreatorID  *string `json:"creator_id"`
}

type QuizListResponse struct {
	Quizzes    []QuizResponse `json:"quizzes"`
	Total      int            `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
}

type SearchQuizzesRequest struct {
	Query string `json:"query" validate:"required,min=2"`
	Page  int    `json:"page" validate:"omitempty,min=1"`
	Limit int    `json:"limit" validate:"omitempty,min=1,max=100"`

	// Filters
	Type       *string `json:"type" validate:"omitempty,oneof=personal shared official"`
	Subject    *string `json:"subject"`
	Difficulty *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	IsPublic   *bool   `json:"is_public"`
}

// Question Management DTOs
type AddQuestionRequest struct {
	Text          string  `json:"text" validate:"required,min=10,max=1000"`
	OptionA       string  `json:"option_a" validate:"required,min=1,max=200"`
	OptionB       string  `json:"option_b" validate:"required,min=1,max=200"`
	OptionC       string  `json:"option_c" validate:"required,min=1,max=200"`
	OptionD       string  `json:"option_d" validate:"required,min=1,max=200"`
	OptionE       *string `json:"option_e" validate:"omitempty,min=1,max=200"`
	CorrectAnswer string  `json:"correct_answer" validate:"required,oneof=A B C D E"`
	Explanation   *string `json:"explanation" validate:"omitempty,max=1000"`
	Subject       *string `json:"subject" validate:"omitempty,max=100"`
	Year          *int    `json:"year" validate:"omitempty,min=1990,max=2030"`
	OrderIndex    *int    `json:"order_index" validate:"omitempty,min=0"`
}

type UpdateQuestionRequest struct {
	Text          *string `json:"text" validate:"omitempty,min=10,max=1000"`
	OptionA       *string `json:"option_a" validate:"omitempty,min=1,max=200"`
	OptionB       *string `json:"option_b" validate:"omitempty,min=1,max=200"`
	OptionC       *string `json:"option_c" validate:"omitempty,min=1,max=200"`
	OptionD       *string `json:"option_d" validate:"omitempty,min=1,max=200"`
	OptionE       *string `json:"option_e" validate:"omitempty,min=1,max=200"`
	CorrectAnswer *string `json:"correct_answer" validate:"omitempty,oneof=A B C D E"`
	Explanation   *string `json:"explanation" validate:"omitempty,max=1000"`
	Subject       *string `json:"subject" validate:"omitempty,max=100"`
	Year          *int    `json:"year" validate:"omitempty,min=1990,max=2030"`
	OrderIndex    *int    `json:"order_index" validate:"omitempty,min=0"`
}

type QuestionResponse struct {
	ID            uuid.UUID  `json:"id"`
	QuizID        *uuid.UUID `json:"quiz_id"`
	Text          string     `json:"text"`
	OptionA       string     `json:"option_a"`
	OptionB       string     `json:"option_b"`
	OptionC       string     `json:"option_c"`
	OptionD       string     `json:"option_d"`
	OptionE       *string    `json:"option_e"`
	CorrectAnswer string     `json:"correct_answer,omitempty"` // Only shown to quiz creator or after completion
	Explanation   *string    `json:"explanation,omitempty"`    // Only shown after answering
	Subject       *string    `json:"subject"`
	Year          *int       `json:"year"`
	OrderIndex    int        `json:"order_index"`

	// User-specific data (if applicable)
	UserAnswer *string `json:"user_answer,omitempty"`
	IsCorrect  *bool   `json:"is_correct,omitempty"`
	TimeSpent  *int    `json:"time_spent,omitempty"` // in seconds

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type QuizQuestionsResponse struct {
	QuizID    uuid.UUID          `json:"quiz_id"`
	Questions []QuestionResponse `json:"questions"`
	Total     int                `json:"total"`
}

// Quiz Taking DTOs
type QuizSessionResponse struct {
	ID              uuid.UUID  `json:"id"`
	UserID          uuid.UUID  `json:"user_id"`
	QuizID          uuid.UUID  `json:"quiz_id"`
	StartTime       time.Time  `json:"start_time"`
	EndTime         *time.Time `json:"end_time"`
	TimeLimit       *int       `json:"time_limit"` // in seconds
	IsCompleted     bool       `json:"is_completed"`
	CurrentQuestion int        `json:"current_question"`
	TotalQuestions  int        `json:"total_questions"`

	// Quiz info
	Quiz QuizResponse `json:"quiz"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type SubmitAnswerRequest struct {
	QuestionID uuid.UUID `json:"question_id" validate:"required"`
	Answer     *string   `json:"answer" validate:"omitempty,oneof=A B C D E"`
	TimeSpent  *int      `json:"time_spent" validate:"omitempty,min=0"` // in seconds
}

type AnswerResponse struct {
	ID         uuid.UUID `json:"id"`
	QuestionID uuid.UUID `json:"question_id"`
	UserAnswer *string   `json:"user_answer"`
	IsCorrect  bool      `json:"is_correct"`
	TimeSpent  *int      `json:"time_spent"`

	// Question info (with correct answer and explanation if applicable)
	Question QuestionResponse `json:"question"`

	CreatedAt time.Time `json:"created_at"`
}

type QuizResultResponse struct {
	ID             uuid.UUID `json:"id"`
	UserID         uuid.UUID `json:"user_id"`
	QuizID         uuid.UUID `json:"quiz_id"`
	Score          int       `json:"score"`
	TotalQuestions int       `json:"total_questions"`
	Percentage     float64   `json:"percentage"`
	TimeSpent      *int      `json:"time_spent"` // in seconds
	CompletedAt    time.Time `json:"completed_at"`

	// Detailed results
	Answers []AnswerResponse `json:"answers"`

	// Quiz info
	Quiz QuizResponse `json:"quiz"`

	// Performance analysis
	CorrectAnswers  int     `json:"correct_answers"`
	WrongAnswers    int     `json:"wrong_answers"`
	SkippedAnswers  int     `json:"skipped_answers"`
	AverageTimePerQ float64 `json:"average_time_per_question"`

	// Ranking
	Rank       *int `json:"rank"`        // User's rank for this quiz
	TotalUsers int  `json:"total_users"` // Total users who took this quiz

	CreatedAt time.Time `json:"created_at"`
}

type GetQuizResultsRequest struct {
	Page     int        `json:"page" validate:"omitempty,min=1"`
	Limit    int        `json:"limit" validate:"omitempty,min=1,max=100"`
	QuizID   *uuid.UUID `json:"quiz_id"`
	Subject  *string    `json:"subject"`
	MinScore *float64   `json:"min_score" validate:"omitempty,min=0,max=100"`
	MaxScore *float64   `json:"max_score" validate:"omitempty,min=0,max=100"`
	SortBy   string     `json:"sort_by" validate:"omitempty,oneof=completed_at percentage score"`
	SortDesc bool       `json:"sort_desc"`
}

type QuizResultsResponse struct {
	Results    []QuizResultResponse `json:"results"`
	Total      int                  `json:"total"`
	Page       int                  `json:"page"`
	Limit      int                  `json:"limit"`
	TotalPages int                  `json:"total_pages"`

	// Summary statistics
	AverageScore   float64 `json:"average_score"`
	BestScore      float64 `json:"best_score"`
	TotalQuizzes   int     `json:"total_quizzes"`
	TotalTimeSpent int     `json:"total_time_spent"` // in seconds
}

// Quiz Statistics DTOs
type QuizStatisticsResponse struct {
	QuizID         uuid.UUID `json:"quiz_id"`
	TotalAttempts  int       `json:"total_attempts"`
	UniqueUsers    int       `json:"unique_users"`
	AverageScore   float64   `json:"average_score"`
	CompletionRate float64   `json:"completion_rate"`

	// Question-level statistics
	QuestionStats []QuestionStatistics `json:"question_stats"`

	// Time-based statistics
	DailyAttempts  []DailyAttemptStat  `json:"daily_attempts"`
	WeeklyAttempts []WeeklyAttemptStat `json:"weekly_attempts"`

	// Score distribution
	ScoreDistribution []ScoreDistributionStat `json:"score_distribution"`
}

type QuestionStatistics struct {
	QuestionID     uuid.UUID `json:"question_id"`
	TotalAnswers   int       `json:"total_answers"`
	CorrectAnswers int       `json:"correct_answers"`
	AccuracyRate   float64   `json:"accuracy_rate"`
	AverageTime    float64   `json:"average_time"`
	OptionACount   int       `json:"option_a_count"`
	OptionBCount   int       `json:"option_b_count"`
	OptionCCount   int       `json:"option_c_count"`
	OptionDCount   int       `json:"option_d_count"`
	OptionECount   int       `json:"option_e_count"`
	SkippedCount   int       `json:"skipped_count"`
}

type DailyAttemptStat struct {
	Date     time.Time `json:"date"`
	Attempts int       `json:"attempts"`
}

type WeeklyAttemptStat struct {
	WeekStart time.Time `json:"week_start"`
	Attempts  int       `json:"attempts"`
}

// Quiz Leaderboard DTOs - Using shared types from analytics.go and badge.go

type QuizLeaderboardResponse struct {
	QuizID  uuid.UUID          `json:"quiz_id"`
	Entries []LeaderboardEntry `json:"entries"`
	Total   int                `json:"total"`
	Page    int                `json:"page"`
	Limit   int                `json:"limit"`

	// User's position (if authenticated)
	UserRank     *int     `json:"user_rank"`
	UserScore    *float64 `json:"user_score"`
	UserAttempts *int     `json:"user_attempts"`
}

// Quiz Sharing DTOs
type ShareQuizRequest struct {
	FriendIDs []uuid.UUID `json:"friend_ids" validate:"required,min=1"`
	Message   *string     `json:"message" validate:"omitempty,max=500"`
}

type InviteToQuizRequest struct {
	UserIDs []uuid.UUID `json:"user_ids" validate:"required,min=1"`
	Message *string     `json:"message" validate:"omitempty,max=500"`
}

// Note: MessageResponse is defined in user.go
