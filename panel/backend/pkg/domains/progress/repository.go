package progress

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	// Progress tracking
	UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error)
	GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error)
	GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error)

	// Progress analytics
	GetProgressStats(userID string) (*dtos.ProgressStatsResponse, error)
	GetProgressHistory(userID string, payload *dtos.GetProgressHistoryRequest) (*dtos.ProgressHistoryResponse, error)
	GetStudyStreak(userID string) (*dtos.StudyStreakResponse, error)

	// Study sessions
	StartStudySession(userID string, payload *dtos.StartStudySessionRequest) (*dtos.StudySessionResponse, error)
	EndStudySession(userID string, sessionID string) (*dtos.StudySessionResponse, error)
	GetStudySessions(userID string, payload *dtos.GetStudySessionsRequest) (*dtos.StudySessionsResponse, error)
	UpdateUserStatsFromSession(userID string, session *dtos.StudySessionResponse) error

	// Timeline integration
	CreateProgressTimelineEntry(userID string, progressID string) error
	CreateStudySessionTimelineEntry(userID string, sessionID string) error

	// Progress goals
	SetProgressGoal(userID string, payload *dtos.SetProgressGoalRequest) (*dtos.ProgressGoalResponse, error)
	GetProgressGoals(userID string) (*dtos.ProgressGoalsResponse, error)
	UpdateProgressGoal(userID string, goalID string, payload *dtos.UpdateProgressGoalRequest) (*dtos.ProgressGoalResponse, error)
	DeleteProgressGoal(userID string, goalID string) error

	// Progress achievements
	CheckProgressAchievements(userID string) ([]dtos.AchievementResponse, error)
	GetProgressAchievements(userID string) (*dtos.AchievementsResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Progress tracking
func (r *repository) UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error) {
	var progress entities.Progress

	// Try to find existing progress
	err := r.db.Where("user_id = ? AND content_id = ?", userID, payload.ContentID).First(&progress).Error
	if err != nil {
		// Create new progress record
		userUUID := uuid.MustParse(userID)
		progress = entities.Progress{
			UserID:    &userUUID,
			ContentID: &payload.ContentID,
		}
	}

	// Update fields if provided
	if payload.CurrentPage != nil {
		progress.CurrentPage = payload.CurrentPage
	}
	if payload.CurrentTime != nil {
		progress.CurrentTime = payload.CurrentTime
	}
	if payload.Notes != nil {
		progress.Notes = payload.Notes
	}
	if payload.IsCompleted != nil {
		progress.IsCompleted = *payload.IsCompleted
	}

	// Calculate percentage based on content type and progress
	var content entities.Content
	r.db.Where("id = ?", payload.ContentID).First(&content)

	percentage := float64(0)
	if content.TotalPages != nil && progress.CurrentPage != nil {
		percentage = float64(*progress.CurrentPage) / float64(*content.TotalPages) * 100
	} else if content.TotalTime != nil && progress.CurrentTime != nil {
		percentage = float64(*progress.CurrentTime) / float64(*content.TotalTime) * 100
	}

	if percentage > 100 {
		percentage = 100
	}
	progress.Percentage = percentage

	// Save progress
	if progress.ID == uuid.Nil {
		err = r.db.Create(&progress).Error
	} else {
		err = r.db.Save(&progress).Error
	}

	if err != nil {
		return nil, errors.New("Failed to update progress")
	}

	// Update daily stats
	r.updateDailyStats(userID, &progress)

	return r.GetProgress(userID, payload.ContentID.String())
}

func (r *repository) GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error) {
	var progress entities.Progress
	err := r.db.Where("user_id = ? AND content_id = ?", userID, contentID).First(&progress).Error
	if err != nil {
		return nil, errors.New("Progress not found")
	}

	// Get content info
	var content entities.Content
	r.db.Where("id = ?", contentID).First(&content)

	contentResponse := dtos.ContentResponse{
		ID:          content.ID,
		Title:       content.Title,
		Description: content.Description,
		Type:        string(content.Type),
		URL:         content.URL,
		TotalPages:  content.TotalPages,
		TotalTime:   content.TotalTime,
		Subject:     content.Subject,
		Difficulty:  content.Difficulty,
		IsOfficial:  content.IsOfficial,
		CreatedAt:   content.CreatedAt,
		UpdatedAt:   content.UpdatedAt,
	}

	return &dtos.ProgressResponse{
		ID:          progress.ID,
		UserID:      progress.UserID,
		ContentID:   progress.ContentID,
		CurrentPage: progress.CurrentPage,
		CurrentTime: progress.CurrentTime,
		Percentage:  progress.Percentage,
		IsCompleted: progress.IsCompleted,
		Notes:       progress.Notes,
		Content:     contentResponse,
		CreatedAt:   progress.CreatedAt,
		UpdatedAt:   progress.UpdatedAt,
	}, nil
}

func (r *repository) GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error) {
	// This is a simplified implementation
	// In a real implementation, you would handle pagination, filtering, and sorting
	var progressRecords []entities.Progress
	query := r.db.Where("user_id = ?", userID)

	if payload.IsCompleted != nil {
		query = query.Where("is_completed = ?", *payload.IsCompleted)
	}

	err := query.Find(&progressRecords).Error
	if err != nil {
		return nil, errors.New("Failed to get user progress")
	}

	progressResponses := make([]dtos.ProgressResponse, len(progressRecords))
	for i, progress := range progressRecords {
		// Get content info
		var content entities.Content
		r.db.Where("id = ?", progress.ContentID).First(&content)

		contentResponse := dtos.ContentResponse{
			ID:          content.ID,
			Title:       content.Title,
			Description: content.Description,
			Type:        string(content.Type),
			URL:         content.URL,
			TotalPages:  content.TotalPages,
			TotalTime:   content.TotalTime,
			Subject:     content.Subject,
			Difficulty:  content.Difficulty,
			IsOfficial:  content.IsOfficial,
			CreatedAt:   content.CreatedAt,
			UpdatedAt:   content.UpdatedAt,
		}

		progressResponses[i] = dtos.ProgressResponse{
			ID:          progress.ID,
			UserID:      progress.UserID,
			ContentID:   progress.ContentID,
			CurrentPage: progress.CurrentPage,
			CurrentTime: progress.CurrentTime,
			Percentage:  progress.Percentage,
			IsCompleted: progress.IsCompleted,
			Notes:       progress.Notes,
			Content:     contentResponse,
			CreatedAt:   progress.CreatedAt,
			UpdatedAt:   progress.UpdatedAt,
		}
	}

	return &dtos.ProgressListResponse{
		Progress:   progressResponses,
		Total:      len(progressResponses),
		Page:       1,
		Limit:      len(progressResponses),
		TotalPages: 1,
	}, nil
}

// Helper method to update daily stats
func (r *repository) updateDailyStats(userID string, _ *entities.Progress) {
	today := time.Now().Truncate(24 * time.Hour)

	var dailyStats entities.DailyStats
	err := r.db.Where("user_id = ? AND date = ?", userID, today).First(&dailyStats).Error
	if err != nil {
		// Create new daily stats record
		userUUID := uuid.MustParse(userID)
		dailyStats = entities.DailyStats{
			UserID: &userUUID,
			Date:   today,
		}
		r.db.Create(&dailyStats)
	}

	// Update progress-related stats
	// This is a simplified implementation
	r.db.Model(&dailyStats).Updates(map[string]interface{}{
		"books_progress":  dailyStats.BooksProgress + 1,
		"videos_progress": dailyStats.VideosProgress + 1,
	})
}

// Progress analytics (placeholder implementations)
func (r *repository) GetProgressStats(userID string) (*dtos.ProgressStatsResponse, error) {
	// TODO: Implement comprehensive progress statistics
	return &dtos.ProgressStatsResponse{
		UserID:             uuid.MustParse(userID),
		TotalContent:       0,
		CompletedContent:   0,
		InProgressContent:  0,
		CompletionRate:     0,
		TotalStudyTime:     0,
		WeeklyStudyTime:    0,
		MonthlyStudyTime:   0,
		AverageSessionTime: 0,
		CurrentStreak:      0,
		LongestStreak:      0,
		LastStudyDate:      nil,
		BookProgress:       dtos.ProgressBreakdown{},
		VideoProgress:      dtos.ProgressBreakdown{},
		QuestionProgress:   dtos.ProgressBreakdown{},
		SubjectProgress:    []dtos.SubjectProgressInfo{},
		RecentSessions:     []dtos.RecentSessionInfo{},
		RecentProgress:     []dtos.RecentProgressInfo{},
	}, nil
}

func (r *repository) GetProgressHistory(userID string, payload *dtos.GetProgressHistoryRequest) (*dtos.ProgressHistoryResponse, error) {
	// TODO: Implement progress history tracking
	return &dtos.ProgressHistoryResponse{
		History:    []dtos.ProgressHistoryEntry{},
		Total:      0,
		Page:       1,
		Limit:      20,
		TotalPages: 0,
	}, nil
}

func (r *repository) GetStudyStreak(userID string) (*dtos.StudyStreakResponse, error) {
	// TODO: Implement study streak calculation
	return &dtos.StudyStreakResponse{
		UserID:        uuid.MustParse(userID),
		CurrentStreak: 0,
		LongestStreak: 0,
		LastStudyDate: nil,
		StreakHistory: []dtos.StreakHistoryEntry{},
	}, nil
}

// Study sessions (placeholder implementations)
func (r *repository) StartStudySession(userID string, payload *dtos.StartStudySessionRequest) (*dtos.StudySessionResponse, error) {
	userUUID := uuid.MustParse(userID)
	session := entities.StudySession{
		UserID:      &userUUID,
		ContentID:   payload.ContentID,
		QuizID:      payload.QuizID,
		StartTime:   time.Now(),
		SessionType: payload.SessionType,
		DeviceType:  "web", // Default
		Platform:    "web", // Default
	}

	if payload.DeviceType != nil {
		session.DeviceType = *payload.DeviceType
	}
	if payload.Platform != nil {
		session.Platform = *payload.Platform
	}

	err := r.db.Create(&session).Error
	if err != nil {
		return nil, errors.New("Failed to start study session")
	}

	return &dtos.StudySessionResponse{
		ID:             session.ID,
		UserID:         session.UserID,
		ContentID:      session.ContentID,
		QuizID:         session.QuizID,
		SessionType:    session.SessionType,
		StartTime:      session.StartTime,
		EndTime:        session.EndTime,
		Duration:       session.Duration,
		ProgressBefore: session.ProgressBefore,
		ProgressAfter:  session.ProgressAfter,
		DeviceType:     session.DeviceType,
		Platform:       session.Platform,
		CreatedAt:      session.CreatedAt,
		UpdatedAt:      session.UpdatedAt,
	}, nil
}

func (r *repository) EndStudySession(userID string, sessionID string) (*dtos.StudySessionResponse, error) {
	var session entities.StudySession
	err := r.db.Where("id = ? AND user_id = ?", sessionID, userID).First(&session).Error
	if err != nil {
		return nil, errors.New("Study session not found")
	}

	now := time.Now()
	duration := int(now.Sub(session.StartTime).Seconds())

	session.EndTime = &now
	session.Duration = &duration

	err = r.db.Save(&session).Error
	if err != nil {
		return nil, errors.New("Failed to end study session")
	}

	return &dtos.StudySessionResponse{
		ID:             session.ID,
		UserID:         session.UserID,
		ContentID:      session.ContentID,
		QuizID:         session.QuizID,
		SessionType:    session.SessionType,
		StartTime:      session.StartTime,
		EndTime:        session.EndTime,
		Duration:       session.Duration,
		ProgressBefore: session.ProgressBefore,
		ProgressAfter:  session.ProgressAfter,
		DeviceType:     session.DeviceType,
		Platform:       session.Platform,
		CreatedAt:      session.CreatedAt,
		UpdatedAt:      session.UpdatedAt,
	}, nil
}

func (r *repository) GetStudySessions(userID string, payload *dtos.GetStudySessionsRequest) (*dtos.StudySessionsResponse, error) {
	// TODO: Implement comprehensive study session retrieval with filtering
	return &dtos.StudySessionsResponse{
		Sessions:           []dtos.StudySessionResponse{},
		Total:              0,
		Page:               1,
		Limit:              20,
		TotalPages:         0,
		TotalStudyTime:     0,
		AverageSessionTime: 0,
		CompletedSessions:  0,
		OngoingSessions:    0,
	}, nil
}

func (r *repository) UpdateUserStatsFromSession(userID string, session *dtos.StudySessionResponse) error {
	// TODO: Update user statistics based on completed study session
	return nil
}

// Timeline integration (placeholder implementations)
func (r *repository) CreateProgressTimelineEntry(userID string, progressID string) error {
	// TODO: Create timeline entry for progress updates
	return nil
}

func (r *repository) CreateStudySessionTimelineEntry(userID string, sessionID string) error {
	// TODO: Create timeline entry for study sessions
	return nil
}

// Progress goals (placeholder implementations)
func (r *repository) SetProgressGoal(userID string, payload *dtos.SetProgressGoalRequest) (*dtos.ProgressGoalResponse, error) {
	// TODO: Implement progress goal creation
	return &dtos.ProgressGoalResponse{
		ID:           uuid.New(),
		UserID:       uuid.MustParse(userID),
		Type:         payload.Type,
		Title:        payload.Title,
		Description:  payload.Description,
		TargetValue:  payload.TargetValue,
		CurrentValue: 0,
		Unit:         payload.Unit,
		Progress:     0,
		IsCompleted:  false,
		IsActive:     true,
		StartDate:    payload.StartDate,
		EndDate:      payload.EndDate,
		ContentID:    payload.ContentID,
		Subject:      payload.Subject,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}, nil
}

func (r *repository) GetProgressGoals(userID string) (*dtos.ProgressGoalsResponse, error) {
	// TODO: Implement progress goals retrieval
	return &dtos.ProgressGoalsResponse{
		Goals:     []dtos.ProgressGoalResponse{},
		Total:     0,
		Active:    0,
		Completed: 0,
		Overdue:   0,
	}, nil
}

func (r *repository) UpdateProgressGoal(userID string, goalID string, payload *dtos.UpdateProgressGoalRequest) (*dtos.ProgressGoalResponse, error) {
	// TODO: Implement progress goal updates
	return &dtos.ProgressGoalResponse{
		ID:           uuid.MustParse(goalID),
		UserID:       uuid.MustParse(userID),
		Type:         "daily",
		Title:        "Updated Goal",
		Description:  payload.Description,
		TargetValue:  100,
		CurrentValue: 0,
		Unit:         "minutes",
		Progress:     0,
		IsCompleted:  false,
		IsActive:     true,
		StartDate:    time.Now(),
		EndDate:      time.Now().AddDate(0, 0, 7),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}, nil
}

func (r *repository) DeleteProgressGoal(userID string, goalID string) error {
	// TODO: Implement progress goal deletion
	return nil
}

// Progress achievements (placeholder implementations)
func (r *repository) CheckProgressAchievements(userID string) ([]dtos.AchievementResponse, error) {
	// TODO: Implement achievement checking logic
	return []dtos.AchievementResponse{}, nil
}

func (r *repository) GetProgressAchievements(userID string) (*dtos.AchievementsResponse, error) {
	// TODO: Implement achievements retrieval
	return &dtos.AchievementsResponse{
		Achievements:         []dtos.AchievementResponse{},
		Total:                0,
		TotalPoints:          0,
		StudyAchievements:    0,
		ProgressAchievements: 0,
		SocialAchievements:   0,
		QuizAchievements:     0,
	}, nil
}
