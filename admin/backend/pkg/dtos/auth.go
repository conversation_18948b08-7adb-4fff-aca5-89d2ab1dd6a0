package dtos

type AuthLoginRequest struct {
    Username string `json:"username"`
    Password string `json:"password"`
}

type TokenResponse struct {
    Token string `json:"token"`
}

type AdminCreateRequest struct {
    Username string `json:"username"`
    Password string `json:"password"`
    Role     string `json:"role"`
}

type AdminUpdateRequest struct {
    Username *string `json:"username"`
    Password *string `json:"password"`
    Role     *string `json:"role"`
}

type AdminResponse struct {
    ID       string `json:"id"`
    Username string `json:"username"`
    Role     string `json:"role"`
}

type AdminListResponse struct {
    Data []AdminResponse `json:"data"`
}

