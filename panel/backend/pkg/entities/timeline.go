package entities

import (
	"github.com/google/uuid"
)

// TimelineEntryType represents different types of timeline entries
type TimelineEntryType string

const (
	TimelineTypeStudy       TimelineEntryType = "study"
	TimelineTypeQuiz        TimelineEntryType = "quiz"
	TimelineTypeProgress    TimelineEntryType = "progress"
	TimelineTypeAchievement TimelineEntryType = "achievement"
	TimelineTypeSocial      TimelineEntryType = "social"
	TimelineTypeCustom      TimelineEntryType = "custom"
)

// TimelineVisibility represents visibility levels for timeline entries
type TimelineVisibility string

const (
	TimelineVisibilityPublic  TimelineVisibility = "public"
	TimelineVisibilityFriends TimelineVisibility = "friends"
	TimelineVisibilityPrivate TimelineVisibility = "private"
)

// TimelineEntry represents activities in user's timeline/feed
type TimelineEntry struct {
	Base
	UserID      uuid.UUID          `json:"user_id" gorm:"type:uuid;not null;index"`
	Type        TimelineEntryType  `json:"type" gorm:"type:varchar(20);not null"`
	Title       string             `json:"title" gorm:"not null"`
	Description *string            `json:"description"`
	Visibility  TimelineVisibility `json:"visibility" gorm:"type:varchar(20);default:'public'"`

	// Reference IDs for different types of activities
	ContentID *uuid.UUID `json:"content_id" gorm:"type:uuid"`
	QuizID    *uuid.UUID `json:"quiz_id" gorm:"type:uuid"`

	// Activity metadata
	Metadata map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
}

// TimelineLike represents likes on timeline entries
type TimelineLike struct {
	Base
	TimelineEntryID uuid.UUID `json:"timeline_entry_id" gorm:"type:uuid;not null;index"`
	UserID          uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
}

// TimelineComment represents comments on timeline entries
type TimelineComment struct {
	Base
	TimelineEntryID uuid.UUID  `json:"timeline_entry_id" gorm:"type:uuid;not null;index"`
	UserID          uuid.UUID  `json:"user_id" gorm:"type:uuid;not null;index"`
	Content         string     `json:"content" gorm:"not null"`
	ParentID        *uuid.UUID `json:"parent_id" gorm:"type:uuid"` // For reply comments
}

func (TimelineEntry) TableName() string {
	return "timeline_entries"
}

func (TimelineLike) TableName() string {
	return "timeline_likes"
}

func (TimelineComment) TableName() string {
	return "timeline_comments"
}
