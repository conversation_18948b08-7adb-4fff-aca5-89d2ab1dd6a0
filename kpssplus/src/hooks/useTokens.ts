import {useState, useEffect, useCallback} from 'react';
import {Alert} from 'react-native';
import {tokenService} from '../services/tokenService';
import {TokenTransaction, TokenPackage} from '../types';
import {useAuth} from './useAuth';

interface TokenState {
  balance: number;
  isLoading: boolean;
  error: string | null;
  transactions: TokenTransaction[];
  packages: TokenPackage[];
  dailyBonusAvailable: boolean;
  nextBonusAt?: string;
}

interface TokenActions {
  refreshBalance: () => Promise<void>;
  spendTokens: (
    amount: number,
    reason: string,
    relatedId?: string,
  ) => Promise<boolean>;
  checkTokens: (requiredAmount: number) => Promise<boolean>;
  purchaseTokens: (packageId: string) => Promise<boolean>;
  claimDailyBonus: () => Promise<boolean>;
  loadTransactionHistory: () => Promise<void>;
  loadTokenPackages: () => Promise<void>;
  showInsufficientTokensAlert: (requiredAmount: number) => void;
}

const INITIAL_STATE: TokenState = {
  balance: 0,
  isLoading: false,
  error: null,
  transactions: [],
  packages: [],
  dailyBonusAvailable: false,
};

export const useTokens = (): TokenState & TokenActions => {
  const [state, setState] = useState<TokenState>(INITIAL_STATE);
  const {isAuthenticated} = useAuth();

  // Refresh token balance
  const refreshBalance = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setState(prev => ({...prev, isLoading: true, error: null}));
      const balance = await tokenService.getTokenBalance();
      setState(prev => ({...prev, balance, isLoading: false}));
    } catch (error) {
      console.error('Error refreshing token balance:', error);
      setState(prev => ({
        ...prev,
        error: 'Token bakiyesi yüklenemedi',
        isLoading: false,
      }));
    }
  }, [isAuthenticated]);

  // Spend tokens
  const spendTokens = useCallback(
    async (
      amount: number,
      reason: string,
      relatedId?: string,
    ): Promise<boolean> => {
      if (!isAuthenticated) {
        return false;
      }

      try {
        const hasEnough = await tokenService.hasEnoughTokens(amount);
        if (!hasEnough) {
          showInsufficientTokensAlert(amount);
          return false;
        }

        const result = await tokenService.spendTokens(
          amount,
          reason,
          relatedId,
        );
        if (result.success) {
          setState(prev => ({...prev, balance: result.remainingTokens}));
          return true;
        }
        return false;
      } catch (error) {
        console.error('Error spending tokens:', error);
        Alert.alert('Hata', 'Jeton harcama işlemi başarısız oldu');
        return false;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isAuthenticated],
  );

  // Check if user has enough tokens
  const checkTokens = useCallback(
    async (requiredAmount: number): Promise<boolean> => {
      if (!isAuthenticated) {
        return false;
      }
      return await tokenService.hasEnoughTokens(requiredAmount);
    },
    [isAuthenticated],
  );

  // Purchase tokens
  const purchaseTokens = useCallback(
    async (packageId: string): Promise<boolean> => {
      if (!isAuthenticated) {
        return false;
      }

      try {
        setState(prev => ({...prev, isLoading: true}));
        const result = await tokenService.purchaseTokens(packageId);

        if (result.success) {
          setState(prev => ({
            ...prev,
            balance: result.newBalance,
            isLoading: false,
          }));
          Alert.alert('Başarılı', 'Jeton satın alma işlemi tamamlandı!');
          return true;
        }

        setState(prev => ({...prev, isLoading: false}));
        Alert.alert('Hata', 'Satın alma işlemi başarısız oldu');
        return false;
      } catch (error) {
        console.error('Error purchasing tokens:', error);
        setState(prev => ({...prev, isLoading: false}));
        Alert.alert('Hata', 'Satın alma işlemi başarısız oldu');
        return false;
      }
    },
    [isAuthenticated],
  );

  // Claim daily bonus
  const claimDailyBonus = useCallback(async (): Promise<boolean> => {
    if (!isAuthenticated) {
      return false;
    }

    try {
      const result = await tokenService.claimDailyBonus();
      if (result.success) {
        setState(prev => ({
          ...prev,
          balance: result.newBalance,
          dailyBonusAvailable: false,
          nextBonusAt: result.nextClaimAt,
        }));
        Alert.alert(
          'Günlük Bonus!',
          `${result.tokensEarned} jeton kazandınız!`,
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error claiming daily bonus:', error);
      Alert.alert('Hata', 'Günlük bonus alınamadı');
      return false;
    }
  }, [isAuthenticated]);

  // Load transaction history
  const loadTransactionHistory = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      const transactions = await tokenService.getTokenHistory();
      setState(prev => ({...prev, transactions}));
    } catch (error) {
      console.error('Error loading transaction history:', error);
    }
  }, [isAuthenticated]);

  // Load token packages
  const loadTokenPackages = useCallback(async () => {
    try {
      const packages = await tokenService.getTokenPackages();
      setState(prev => ({...prev, packages}));
    } catch (error) {
      console.error('Error loading token packages:', error);
    }
  }, []);

  // Show insufficient tokens alert
  const showInsufficientTokensAlert = useCallback(
    (requiredAmount: number) => {
      Alert.alert(
        'Yetersiz Jeton',
        `Bu işlem için ${requiredAmount} jetona ihtiyacınız var. Mevcut bakiyeniz: ${state.balance}`,
        [
          {text: 'İptal', style: 'cancel'},
          {
            text: 'Jeton Al',
            onPress: () => {
              // Navigate to token purchase screen
              // This will be implemented when we create the purchase screen
            },
          },
        ],
      );
    },
    [state.balance],
  );

  // Check daily bonus availability
  const checkDailyBonus = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      const bonusStatus = await tokenService.isDailyBonusAvailable();
      setState(prev => ({
        ...prev,
        dailyBonusAvailable: bonusStatus.available,
        nextBonusAt: bonusStatus.nextClaimAt,
      }));
    } catch (error) {
      console.error('Error checking daily bonus:', error);
    }
  }, [isAuthenticated]);

  // Initialize token data when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      refreshBalance();
      checkDailyBonus();
      loadTokenPackages();
    } else {
      setState(INITIAL_STATE);
    }
  }, [isAuthenticated, refreshBalance, checkDailyBonus, loadTokenPackages]);

  return {
    ...state,
    refreshBalance,
    spendTokens,
    checkTokens,
    purchaseTokens,
    claimDailyBonus,
    loadTransactionHistory,
    loadTokenPackages,
    showInsufficientTokensAlert,
  };
};
