package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/database"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

func QuizRoutes(r *gin.RouterGroup) {
	quizzes := r.Group("/quizzes")
	{
		quizzes.GET("", GetQuizzes())
		quizzes.GET("/:id", GetQuiz())
		quizzes.POST("", CreateQuiz())
		quizzes.PUT("/:id", UpdateQuiz())
		quizzes.DELETE("/:id", DeleteQuiz())
	}
}

func GetQuizzes() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.DB()
		
		limitStr := c.DefaultQuery("limit", "20")
		offsetStr := c.<PERSON>ult<PERSON>("offset", "0")
		search := c.Query("q")

		limit, _ := strconv.Atoi(limitStr)
		offset, _ := strconv.Atoi(offsetStr)

		var quizzes []entities.Quiz
		var total int64

		query := db.Model(&entities.Quiz{})
		
		if search != "" {
			query = query.Where("title LIKE ? OR description LIKE ?", "%"+search+"%", "%"+search+"%")
		}

		// Get total count
		query.Count(&total)

		// Get paginated results
		if err := query.Offset(offset).Limit(limit).Find(&quizzes).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": quizzes, "total": total})
	}
}

func GetQuiz() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.DB()
		
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		var quiz entities.Quiz
		if err := db.First(&quiz, "id = ?", id).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
			return
		}
		
		c.JSON(http.StatusOK, quiz)
	}
}

func CreateQuiz() gin.HandlerFunc {
    return func(c *gin.Context) {
        db := database.DB()
        type qBody struct {
            Title       string  `json:"title"`
            Description *string `json:"description"`
            Type        string  `json:"type"`
            IsPublic    bool    `json:"is_public"`
            IsActive    bool    `json:"is_active"`
            CreatorID   *string `json:"creator_id"`
        }
        var body qBody
        if err := c.ShouldBindJSON(&body); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
            return
        }

        // try to set creator_id from token claims if available
        tokenAdminID := c.GetString("admin_id")
        // Use dynamic insert to avoid schema mismatches
        data := map[string]any{
            "id":         uuid.New(),
            "title":      body.Title,
            "type":       body.Type,
            "is_public":  body.IsPublic,
            "is_active":  body.IsActive,
        }
        if body.Description != nil { data["description"] = body.Description }
        if body.CreatorID != nil {
            if uid, err := uuid.Parse(*body.CreatorID); err == nil {
                data["creator_id"] = uid
            }
        } else if tokenAdminID != "" {
            if uid, err := uuid.Parse(tokenAdminID); err == nil {
                data["creator_id"] = uid
            }
        }
        if err := db.Model(&entities.Quiz{}).Create(data).Error; err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
        c.JSON(http.StatusCreated, data)
    }
}

func UpdateQuiz() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.DB()
		
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		var quiz entities.Quiz
		if err := c.ShouldBindJSON(&quiz); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		quiz.ID = id
		if err := db.Model(&entities.Quiz{}).Where("id = ?", id).Updates(quiz).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		
		c.JSON(http.StatusOK, quiz)
	}
}

func DeleteQuiz() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.DB()
		
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		if err := db.Delete(&entities.Quiz{}, "id = ?", id).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		
		c.JSON(http.StatusOK, gin.H{"message": "Quiz deleted successfully"})
	}
}
