{"info": {"name": "KPSS Plus - New Features", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Preferences - Get (public)", "request": {"method": "GET", "url": "{{BASE_URL}}/preferences", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}", "disabled": true}]}}, {"name": "Preferences - Update", "request": {"method": "PUT", "url": "{{BASE_URL}}/preferences", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"interests\": [\"Genel<PERSON>ultur\", \"Tarih\"],\n  \"subject_weights\": {\"Tarih\":0.7, \"<PERSON>ğrafya\":0.3},\n  \"difficulty_pref\": \"Medium\"\n}"}}}, {"name": "Library - List (details)", "request": {"method": "GET", "url": "{{BASE_URL}}/library?details=true", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}]}}, {"name": "Library - Add Content", "request": {"method": "POST", "url": "{{BASE_URL}}/library/content/{{CONTENT_ID}}", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"folder_id\": null\n}"}}}, {"name": "Library - Folders - Create", "request": {"method": "POST", "url": "{{BASE_URL}}/library/folders", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"KPSS 2025\"\n}"}}}, {"name": "Quiz - Start Session", "request": {"method": "POST", "url": "{{BASE_URL}}/quiz/{{QUIZ_ID}}/start", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}]}}, {"name": "Quiz - Submit Answer", "request": {"method": "POST", "url": "{{BASE_URL}}/quiz/sessions/{{SESSION_ID}}/answer", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"question_id\": \"{{QUESTION_ID}}\",\n  \"answer\": \"A\",\n  \"time_spent\": 20\n}"}}}, {"name": "Quiz - Finish Session", "request": {"method": "POST", "url": "{{BASE_URL}}/quiz/sessions/{{SESSION_ID}}/finish", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}]}}, {"name": "Quiz - <PERSON>t", "request": {"method": "GET", "url": "{{BASE_URL}}/quiz/results/{{RESULT_ID}}", "header": [{"key": "Authorization", "value": "Bearer {{TOKEN}}"}]}}]}