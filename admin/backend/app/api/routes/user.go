package routes

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "github.com/nocytech/kpss-plus-admin/pkg/domains/user"
    "github.com/nocytech/kpss-plus-admin/pkg/dtos"
)

// RegisterUserRoutes wires user-related admin endpoints
func RegisterUserRoutes(authGroup *gin.RouterGroup, s user.Service) {
    authGroup.GET("/users", listUsersHandler(s))
}

func listUsersHandler(s user.Service) gin.HandlerFunc {
    return func(c *gin.Context) {
        q := c.Query("q")
        limit, offset, order := parseListParams(c, 50, 200)
        list, total, err := s.List(q, limit, offset, order)
        if err != nil {
            c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
        c.<PERSON>(200, dtos.UserListResponse{Data: list, Total: total})
    }
}

// parseListParams is a light copy of server helper to avoid circular deps
func parseListParams(c *gin.Context, defLimit, maxLimit int) (int, int, string) {
    limit := defLimit
    if v := c.Query("limit"); v != "" {
        if n, err := strconv.Atoi(v); err == nil {
            if n < 1 {
                n = 1
            }
            if n > maxLimit {
                n = maxLimit
            }
            limit = n
        }
    }
    offset := 0
    if v := c.Query("offset"); v != "" {
        if n, err := strconv.Atoi(v); err == nil && n >= 0 {
            offset = n
        }
    }
    order := "created_at DESC"
    if s := c.Query("sort"); s != "" {
        if s == "created_at.asc" {
            order = "created_at ASC"
        }
        if s == "created_at.desc" {
            order = "created_at DESC"
        }
    }
    return limit, offset, order
}
