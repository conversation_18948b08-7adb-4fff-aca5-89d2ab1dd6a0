import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {BattleStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens
import {BattleHomeScreen} from '../screens/battle/BattleHomeScreen';
import {GroupBattleScreen} from '../screens/battle/GroupBattleScreen';
import {FriendChallengeScreen} from '../screens/battle/FriendChallengeScreen';

const Stack = createStackNavigator<BattleStackParamList>();

export const BattleStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.white,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackTitle: '',
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="BattleHome"
        component={BattleHomeScreen}
        options={{
          title: '<PERSON><PERSON><PERSON><PERSON>ma',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="GroupBattle"
        component={GroupBattleScreen}
        options={{
          title: 'Grup Kapışması',
        }}
      />
      <Stack.Screen
        name="FriendChallenge"
        component={FriendChallengeScreen}
        options={{
          title: 'Arkadaş Kapışması',
        }}
      />
      {/* Placeholder screens - these would be implemented later */}
      <Stack.Screen
        name="GroupBattleDetail"
        component={BattleHomeScreen} // Placeholder
        options={{
          title: 'Kapışma Detayı',
        }}
      />
      <Stack.Screen
        name="CreateGroupBattle"
        component={BattleHomeScreen} // Placeholder
        options={{
          title: 'Grup Oluştur',
        }}
      />
      <Stack.Screen
        name="JoinBattleByCode"
        component={BattleHomeScreen} // Placeholder
        options={{
          title: 'Kod ile Katıl',
        }}
      />
      <Stack.Screen
        name="ChallengeDetail"
        component={BattleHomeScreen} // Placeholder
        options={{
          title: 'Meydan Okuma',
        }}
      />
      <Stack.Screen
        name="SendChallenge"
        component={BattleHomeScreen} // Placeholder
        options={{
          title: 'Meydan Okuma Gönder',
        }}
      />
      <Stack.Screen
        name="BattleStats"
        component={BattleHomeScreen} // Placeholder
        options={{
          title: 'İstatistikler',
        }}
      />
    </Stack.Navigator>
  );
};
