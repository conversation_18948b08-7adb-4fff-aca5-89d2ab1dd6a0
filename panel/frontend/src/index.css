@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
* {
  border-color: rgb(203 213 225);
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

body {
  background-color: rgb(15 23 42);
  color: rgb(248 250 252);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
  width: 100%;
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(30 41 59);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgb(71 85 105);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(100 116 139);
}

/* Custom utilities */
.bg-background {
  background-color: rgb(15 23 42);
}

.bg-surface {
  background-color: rgb(30 41 59);
}

.bg-surface-hover {
  background-color: rgb(51 65 85);
}

.text-primary {
  color: rgb(248 250 252);
}

.text-secondary {
  color: rgb(203 213 225);
}

.text-muted {
  color: rgb(148 163 184);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Glass morphism effect */
.glass {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.glass-light {
  background: rgba(248, 250, 252, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(248, 250, 252, 0.1);
}

/* Button variants */
.btn-primary {
  @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-gradient-to-r from-secondary-600 to-secondary-700 hover:from-secondary-700 hover:to-secondary-800 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
}

.btn-success {
  @apply bg-gradient-to-r from-success-600 to-success-700 hover:from-success-700 hover:to-success-800 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
}

.btn-danger {
  @apply bg-gradient-to-r from-danger-600 to-danger-700 hover:from-danger-700 hover:to-danger-800 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
}

.btn-outline {
  @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium px-4 py-2 rounded-lg transition-all duration-200;
}

/* Card styles */
.card {
  @apply bg-slate-800 border border-slate-600 rounded-xl p-6 shadow-lg;
}

.card-hover {
  @apply card hover:bg-slate-700 hover:shadow-xl transition-all duration-300;
}

/* Input styles */
.input-primary {
  @apply bg-slate-800 border border-slate-600 text-white placeholder-slate-400 rounded-lg px-4 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200;
}

/* Input with icon - add more padding to prevent overlap */
.input-primary.pl-10 {
  padding-left: 3rem;
}

.input-primary.pr-10 {
  padding-right: 3rem;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-dark-600 border-t-primary-500;
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent;
}

/* Status indicators */
.status-online {
  @apply w-3 h-3 bg-success-500 rounded-full;
}

.status-offline {
  @apply w-3 h-3 bg-dark-500 rounded-full;
}

.status-away {
  @apply w-3 h-3 bg-warning-500 rounded-full;
}

.status-busy {
  @apply w-3 h-3 bg-danger-500 rounded-full;
}
