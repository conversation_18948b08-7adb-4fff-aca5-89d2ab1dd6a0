import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {AuthStackParamList} from '../../navigation/types';
import {Button, Input} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';

type Props = StackScreenProps<AuthStackParamList, 'Login'>;

interface LoginForm {
  email: string;
  password: string;
}

interface LoginErrors {
  email?: string;
  password?: string;
}

export const LoginScreen: React.FC<Props> = () => {
  const {login, isLoading, clearError} = useAuth();
  const [form, setForm] = useState<LoginForm>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<LoginErrors>({});

  const validateForm = (): boolean => {
    const newErrors: LoginErrors = {};

    // Email validation
    if (!form.email.trim()) {
      newErrors.email = 'E-posta adresi gerekli';
    } else if (!/\S+@\S+\.\S+/.test(form.email)) {
      newErrors.email = 'Geçerli bir e-posta adresi girin';
    }

    // Password validation
    if (!form.password) {
      newErrors.password = 'Şifre gerekli';
    } else if (form.password.length < 6) {
      newErrors.password = 'Şifre en az 6 karakter olmalı';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      clearError();
      await login({
        identifier: form.email.trim().toLowerCase(),
        password: form.password,
      });

      // Navigation will be handled by RootNavigator based on auth state
      console.log('Go back');
    } catch (err: any) {
      Alert.alert(
        'Giriş Hatası',
        err.message || 'Giriş yapılırken bir hata oluştu',
        [{text: 'Tamam'}],
      );
    }
  };

  const updateForm = (field: keyof LoginForm, value: string) => {
    setForm(prev => ({...prev, [field]: value}));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({...prev, [field]: undefined}));
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <Text style={styles.title}>Hoş Geldin!</Text>
            <Text style={styles.subtitle}>
              Hesabına giriş yap ve öğrenmeye devam et
            </Text>
          </View>

          <View style={styles.form}>
            <Input
              label="E-posta"
              placeholder="E-posta adresinizi girin"
              value={form.email}
              onChangeText={value => updateForm('email', value)}
              error={errors.email}
              leftIcon="mail-outline"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              textContentType="emailAddress"
            />

            <Input
              label="Şifre"
              placeholder="Şifrenizi girin"
              value={form.password}
              onChangeText={value => updateForm('password', value)}
              error={errors.password}
              leftIcon="lock-closed-outline"
              secureTextEntry
              textContentType="password"
            />

            <Button
              title="Giriş Yap"
              onPress={handleLogin}
              loading={isLoading}
              disabled={isLoading}
              fullWidth
              style={styles.loginButton}
            />

            <Button
              title="Şifremi Unuttum"
              onPress={() => console.log('Navigation disabled')}
              variant="ghost"
              size="small"
              style={styles.forgotButton}
            />
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Hesabın yok mu?</Text>
            <Button
              title="Kayıt Ol"
              onPress={() => console.log('Navigation disabled')}
              variant="ghost"
              size="small"
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing[8],
  },
  title: {
    ...typography.h2,
    marginBottom: spacing[2],
    textAlign: 'center',
    color: colors.text.primary,
  },
  subtitle: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
    marginBottom: spacing[6],
  },
  loginButton: {
    marginTop: spacing[4],
    marginBottom: spacing[2],
  },
  forgotButton: {
    alignSelf: 'center',
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: spacing[4],
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  footerText: {
    ...typography.body2,
    color: colors.text.secondary,
    marginRight: spacing[2],
  },
});
