// Spacing system based on 4px grid
export const spacing = {
  0: 0,
  0.5: 2,
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  11: 44,
  12: 48,
  14: 56,
  16: 64,
  20: 80,
  24: 96,
  28: 112,
  32: 128,
  36: 144,
  40: 160,
  44: 176,
  48: 192,
  52: 208,
  56: 224,
  60: 240,
  64: 256,
  72: 288,
  80: 320,
  96: 384,
} as const;

// Common spacing patterns
export const layout = {
  // Screen padding
  screenPadding: spacing[4], // 16px
  screenPaddingHorizontal: spacing[4], // 16px
  screenPaddingVertical: spacing[6], // 24px

  // Card spacing
  cardPadding: spacing[4], // 16px
  cardMargin: spacing[4], // 16px
  cardRadius: 12,

  // List spacing
  listItemPadding: spacing[4], // 16px
  listItemMargin: spacing[2], // 8px
  listSeparator: spacing[3], // 12px

  // Button spacing
  buttonPadding: spacing[4], // 16px
  buttonPaddingSmall: spacing[3], // 12px
  buttonPaddingLarge: spacing[5], // 20px
  buttonRadius: 8,
  buttonRadiusSmall: 6,
  buttonRadiusLarge: 12,

  // Input spacing
  inputPadding: spacing[4], // 16px
  inputMargin: spacing[3], // 12px
  inputRadius: 8,

  // Header spacing
  headerHeight: 56,
  headerPadding: spacing[4], // 16px

  // Tab bar spacing
  tabBarHeight: 88,
  tabBarPadding: spacing[2], // 8px

  // Modal spacing
  modalPadding: spacing[6], // 24px
  modalRadius: 16,

  // Section spacing
  sectionSpacing: spacing[6], // 24px
  sectionPadding: spacing[4], // 16px

  // Icon sizes
  iconSmall: 16,
  iconMedium: 24,
  iconLarge: 32,
  iconXLarge: 48,

  // Avatar sizes
  avatarSmall: 32,
  avatarMedium: 48,
  avatarLarge: 64,
  avatarXLarge: 96,

  // Border radius
  radiusSmall: 4,
  radiusMedium: 8,
  radiusLarge: 12,
  radiusXLarge: 16,
  radiusRound: 9999,

  // Shadow
  shadowRadius: 4,
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.1,
  elevation: 2,
} as const;

// Responsive breakpoints (for future use)
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
} as const;

// Safe area insets (will be overridden by actual safe area values)
export const safeArea = {
  top: 44,
  bottom: 34,
  left: 0,
  right: 0,
} as const;
