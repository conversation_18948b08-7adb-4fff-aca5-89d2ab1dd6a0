package routes

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "github.com/google/uuid"
    "github.com/kpss-plus-backend/pkg/domains/badge"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/middleware"
    "github.com/kpss-plus-backend/pkg/state"
)

func BadgeRoutes(r *gin.RouterGroup, badgeService badge.Service) {
	badgeGroup := r.Group("/badges")

    // Public badge routes
    badgeGroup.GET("", GetBadges(badgeService))
    // register static paths before dynamic :id to avoid conflicts
    badgeGroup.GET("/categories", GetBadgeCategories(badgeService))
    badgeGroup.GET("/:id", GetBadge(badgeService))

	// Protected badge routes
	protectedGroup := badgeGroup.Use(middleware.Authorized())
	{
		// User badge management
		protectedGroup.GET("/user", GetUserBadges(badgeService))
		protectedGroup.GET("/user/progress", GetUserBadgeProgress(badgeService))
		protectedGroup.POST("/:id/claim", ClaimBadge(badgeService))

		// Badge creation and management (admin only)
		adminGroup := protectedGroup.Use(middleware.AdminAuthorized())
		{
			adminGroup.POST("", CreateBadge(badgeService))
			adminGroup.PUT("/:id", UpdateBadge(badgeService))
			adminGroup.DELETE("/:id", DeleteBadge(badgeService))
			adminGroup.POST("/:id/award/:userId", AwardBadge(badgeService))
			adminGroup.DELETE("/:id/revoke/:userId", RevokeBadge(badgeService))
		}
	}
}

func GetBadges(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
        category := c.Query("category")
        sortBy := c.DefaultQuery("sort_by", "")
        sortDesc := c.DefaultQuery("sort_desc", "") == "true"
        badgeType := c.DefaultQuery("type", "")
        var payload = &dtos.GetBadgesRequest{
            Page:     page,
            Limit:    limit,
            Category: category,
            SortBy:   sortBy,
            SortDesc: sortDesc,
            Type:     badgeType,
        }
        badges, err := s.GetBadges(payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get badges"})
            return
        }

        c.JSON(http.StatusOK, gin.H{
            "data":        badges.Badges,
            "total":       badges.Total,
            "page":        badges.Page,
            "limit":       badges.Limit,
            "total_pages": badges.TotalPages,
        })
    }
}

func GetBadge(s badge.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		badge, err := s.GetBadge(id)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Badge not found"})
			return
		}

		c.JSON(http.StatusOK, badge)
	}
}

func GetBadgeCategories(s badge.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		categories, err := s.GetBadgeCategories()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get badge categories"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": categories})
	}
}

func GetUserBadges(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID) // From JWT middleware
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
        category := c.DefaultQuery("category", "")
        badgeType := c.DefaultQuery("type", "")
        sortBy := c.DefaultQuery("sort_by", "")
        sortDesc := c.DefaultQuery("sort_desc", "") == "true"
        var payload = &dtos.GetUserBadgesRequest{
            Page:     page,
            Limit:    limit,
            Category: category,
            Type:     badgeType,
            SortBy:   sortBy,
            SortDesc: sortDesc,
        }
        badges, err := s.GetUserBadges(userID, payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user badges"})
            return
        }

        c.JSON(http.StatusOK, gin.H{"data": badges})
    }
}

func GetUserBadgeProgress(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID) // From JWT middleware
        badgeID := c.Query("badge_id")
        if badgeID == "" {
            c.JSON(http.StatusBadRequest, gin.H{"error": "badge_id is required"})
            return
        }
        progress, err := s.GetBadgeProgress(userID, badgeID)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get badge progress"})
            return
        }

        c.JSON(http.StatusOK, gin.H{"data": progress})
    }
}

func ClaimBadge(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        badgeID := c.Param("id")
        userID := c.GetString(state.CurrentUserID) // From JWT middleware
        bid, err := uuid.Parse(badgeID)
        if err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": "invalid badge id"})
            return
        }
        payload := &dtos.AwardBadgeRequest{BadgeID: bid}
        if _, err := s.AwardBadge(userID, payload); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
            return
        }
        c.JSON(http.StatusOK, gin.H{"message": "Badge claimed"})
    }
}

func CreateBadge(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        var payload dtos.CreateBadgeRequest
        if err := c.ShouldBindJSON(&payload); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
            return
        }
        res, err := s.CreateBadge(&payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create badge"})
            return
        }
        c.JSON(http.StatusCreated, res)
    }
}

func UpdateBadge(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        id := c.Param("id")
        var payload dtos.UpdateBadgeRequest
        if err := c.ShouldBindJSON(&payload); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
            return
        }
        if _, err := s.UpdateBadge(id, &payload); err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update badge"})
            return
        }

        c.JSON(http.StatusOK, gin.H{"message": "Badge updated successfully"})
    }
}

func DeleteBadge(s badge.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		if err := s.DeleteBadge(id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete badge"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Badge deleted successfully"})
	}
}

func AwardBadge(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        badgeID := c.Param("id")
        userID := c.Param("userId")
        bid, err := uuid.Parse(badgeID)
        if err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": "invalid badge id"})
            return
        }
        var body dtos.AwardBadgeRequest
        if err := c.ShouldBindJSON(&body); err == nil && body.Progress > 0 {
            // use provided progress
        } else {
            body.Progress = 0
        }
        body.BadgeID = bid
        if _, err := s.AwardBadge(userID, &body); err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award badge"})
            return
        }
        c.JSON(http.StatusOK, gin.H{"message": "Badge awarded successfully"})
    }
}

func RevokeBadge(s badge.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        badgeID := c.Param("id")
        userID := c.Param("userId")
        // No explicit revoke; reset progress to 0
        if _, err := s.UpdateBadgeProgress(userID, badgeID, &dtos.UpdateBadgeProgressRequest{Progress: 0}); err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to revoke badge"})
            return
        }
        c.JSON(http.StatusOK, gin.H{"message": "Badge revoked successfully"})
    }
}
