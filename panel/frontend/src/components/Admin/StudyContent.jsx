import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

const StudyContent = () => {
  const [content, setContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedContent, setSelectedContent] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const API_URL = process.env.REACT_APP_ADMIN_API_URL || 'http://localhost:8100/api';

  const fetchContent = useCallback(async () => {
    const token = localStorage.getItem('adminToken');
    
    try {
      const response = await axios.get(`${API_URL}/contents?limit=20`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setContent(response.data.data || []);
    } catch (err) {
      console.error('Failed to fetch content:', err);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  useEffect(() => {
    fetchContent();
  }, [fetchContent]);

  const handleDelete = async (contentId) => {
    if (!window.confirm('Are you sure you want to delete this content?')) {
      return;
    }

    const token = localStorage.getItem('adminToken');
    
    try {
      await axios.delete(`${API_URL}/admin/content/${contentId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      fetchContent();
    } catch (err) {
      console.error('Failed to delete content:', err);
    }
  };

  const handleEdit = (contentItem) => {
    setSelectedContent(contentItem);
    setShowModal(true);
  };

  const handleCreate = () => {
    setSelectedContent({
      title: '',
      description: '',
      type: 'book',
      url: '',
      subject: '',
      difficulty: 'medium',
      is_official: false,
      author: '',
      publisher: '',
      year: new Date().getFullYear(),
      total_pages: null,
      total_time: null,
      channel_name: '',
      duration: '',
      isbn: ''
    });
    setShowModal(true);
  };

  const handleSave = async (e) => {
    e.preventDefault();
    const token = localStorage.getItem('adminToken');
    
    try {
      const payload = { ...selectedContent };
      
      // Remove empty fields
      Object.keys(payload).forEach(key => {
        if (payload[key] === '' || payload[key] === null) {
          delete payload[key];
        }
      });

      if (selectedContent.id) {
        await axios.put(`${API_URL}/admin/content/${selectedContent.id}`, payload, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } else {
        await axios.post(`${API_URL}/admin/content`, payload, {
          headers: { Authorization: `Bearer ${token}` }
        });
      }
      
      setShowModal(false);
      fetchContent();
    } catch (err) {
      console.error('Failed to save content:', err);
      console.error('Token:', token);
      console.error('Error response:', err.response);
      alert('Error: ' + (err.response?.data?.error || 'Failed to save content'));
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'book': return '📚';
      case 'video': return '🎥';
      case 'playlist': return '📋';
      case 'question': return '❓';
      case 'pdf': return '📄';
      default: return '📄';
    }
  };

  if (loading) {
    return <div className="loading">Loading study content...</div>;
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>Study Content</h2>
        <button className="btn btn-success" onClick={handleCreate}>
          Add New Content
        </button>
      </div>
      
      <div className="table-container">
        <h3>Manage Study Materials</h3>
        <table>
          <thead>
            <tr>
              <th>Type</th>
              <th>Title</th>
              <th>Subject</th>
              <th>Difficulty</th>
              <th>Creator</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {content.map((item) => (
              <tr key={item.id}>
                <td>{getTypeIcon(item.type)} {item.type}</td>
                <td>{item.title}</td>
                <td>{item.subject || '-'}</td>
                <td>{item.difficulty || '-'}</td>
                <td>{item.author || item.channel_name || '-'}</td>
                <td>{new Date(item.created_at).toLocaleDateString()}</td>
                <td className="actions">
                  <button className="btn btn-primary" onClick={() => handleEdit(item)}>
                    Edit
                  </button>
                  <button className="btn btn-danger" onClick={() => handleDelete(item.id)}>
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {showModal && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{selectedContent.id ? 'Edit Content' : 'Add New Content'}</h3>
              <button className="close-btn" onClick={() => setShowModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={handleSave}>
              <div className="form-group">
                <label>Content Type</label>
                <select
                  value={selectedContent.type}
                  onChange={(e) => setSelectedContent({...selectedContent, type: e.target.value})}
                  required
                >
                  <option value="book">📚 Book</option>
                  <option value="video">🎥 Video</option>
                  <option value="playlist">📋 Playlist</option>
                  <option value="question">❓ Question Bank</option>
                  <option value="pdf">📄 PDF</option>
                </select>
              </div>

              <div className="form-group">
                <label>Title *</label>
                <input
                  type="text"
                  value={selectedContent.title}
                  onChange={(e) => setSelectedContent({...selectedContent, title: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={selectedContent.description || ''}
                  onChange={(e) => setSelectedContent({...selectedContent, description: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Subject</label>
                  <select
                    value={selectedContent.subject || ''}
                    onChange={(e) => setSelectedContent({...selectedContent, subject: e.target.value})}
                  >
                    <option value="">Select subject</option>
                    <option value="Genel Kültür">Genel Kültür</option>
                    <option value="Genel Yetenek">Genel Yetenek</option>
                    <option value="Eğitim Bilimleri">Eğitim Bilimleri</option>
                    <option value="ÖABT">ÖABT</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>Difficulty</label>
                  <select
                    value={selectedContent.difficulty || ''}
                    onChange={(e) => setSelectedContent({...selectedContent, difficulty: e.target.value})}
                  >
                    <option value="">Select difficulty</option>
                    <option value="easy">Easy</option>
                    <option value="medium">Medium</option>
                    <option value="hard">Hard</option>
                  </select>
                </div>
              </div>

              {/* Type-specific fields */}
              {selectedContent.type === 'book' && (
                <>
                  <div className="form-row">
                    <div className="form-group">
                      <label>Author</label>
                      <input
                        type="text"
                        value={selectedContent.author || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, author: e.target.value})}
                      />
                    </div>
                    <div className="form-group">
                      <label>Publisher</label>
                      <input
                        type="text"
                        value={selectedContent.publisher || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, publisher: e.target.value})}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label>Year</label>
                      <input
                        type="number"
                        value={selectedContent.year || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, year: parseInt(e.target.value)})}
                      />
                    </div>
                    <div className="form-group">
                      <label>Total Pages</label>
                      <input
                        type="number"
                        value={selectedContent.total_pages || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, total_pages: parseInt(e.target.value)})}
                      />
                    </div>
                  </div>
                  <div className="form-group">
                    <label>ISBN</label>
                    <input
                      type="text"
                      value={selectedContent.isbn || ''}
                      onChange={(e) => setSelectedContent({...selectedContent, isbn: e.target.value})}
                    />
                  </div>
                </>
              )}

              {selectedContent.type === 'video' && (
                <>
                  <div className="form-group">
                    <label>Video URL</label>
                    <input
                      type="url"
                      value={selectedContent.url || ''}
                      onChange={(e) => setSelectedContent({...selectedContent, url: e.target.value})}
                      placeholder="https://youtube.com/..."
                    />
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label>Channel Name</label>
                      <input
                        type="text"
                        value={selectedContent.channel_name || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, channel_name: e.target.value})}
                      />
                    </div>
                    <div className="form-group">
                      <label>Duration</label>
                      <input
                        type="text"
                        value={selectedContent.duration || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, duration: e.target.value})}
                        placeholder="1:30:45"
                      />
                    </div>
                  </div>
                  <div className="form-group">
                    <label>Total Time (seconds)</label>
                    <input
                      type="number"
                      value={selectedContent.total_time || ''}
                      onChange={(e) => setSelectedContent({...selectedContent, total_time: parseInt(e.target.value)})}
                    />
                  </div>
                </>
              )}

              {selectedContent.type === 'pdf' && (
                <>
                  <div className="form-group">
                    <label>PDF URL</label>
                    <input
                      type="url"
                      value={selectedContent.url || ''}
                      onChange={(e) => setSelectedContent({...selectedContent, url: e.target.value})}
                      placeholder="https://example.com/file.pdf"
                    />
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label>Total Pages</label>
                      <input
                        type="number"
                        value={selectedContent.total_pages || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, total_pages: parseInt(e.target.value)})}
                      />
                    </div>
                    <div className="form-group">
                      <label>Author</label>
                      <input
                        type="text"
                        value={selectedContent.author || ''}
                        onChange={(e) => setSelectedContent({...selectedContent, author: e.target.value})}
                      />
                    </div>
                  </div>
                </>
              )}

              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    checked={selectedContent.is_official || false}
                    onChange={(e) => setSelectedContent({...selectedContent, is_official: e.target.checked})}
                  />
                  Official KPSS Content
                </label>
              </div>

              <div className="actions">
                <button type="submit" className="btn btn-success">
                  Save
                </button>
                <button type="button" className="btn btn-danger" onClick={() => setShowModal(false)}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export { StudyContent };