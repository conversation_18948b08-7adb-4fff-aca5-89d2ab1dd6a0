package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/kpss-plus-backend/pkg/domains/topic"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/middleware"
    "github.com/kpss-plus-backend/pkg/state"
    "github.com/google/uuid"
)

func TopicRoutes(r *gin.RouterGroup, s topic.Service) {
    g := r.Group("/topics")
    // Optional auth for personalization (e.g., user progress badges in listings)
    g.Use(middleware.OptionalAuthorized())

    // Public
    g.GET("", getTopics(s))
    // Single
    g.GET("/:id", getTopic(s))

    // Protected
    p := g.Group("")
    p.Use(middleware.Authorized())
    p.POST("", createTopic(s))
    p.PUT("/:id", updateTopic(s))
    p.DELETE("/:id", deleteTopic(s))

    // Progress
    p.PUT("/:id/progress", updateTopicProgress(s))
    p.GET("/:id/progress", getTopicProgress(s))
}

func createTopic(s topic.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        var req dtos.CreateTopicRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return
        }
        resp, err := s.CreateTopic(&req)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(201, gin.H{"data": resp, "status": 201})
    }
}

func updateTopic(s topic.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        id := c.Param("id")
        var req dtos.UpdateTopicRequest
        if err := c.ShouldBindJSON(&req); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        resp, err := s.UpdateTopic(id, &req)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func deleteTopic(s topic.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        id := c.Param("id")
        if id == "" { c.AbortWithStatusJSON(400, gin.H{"error": "Topic ID is required", "status": 400}); return }
        if err := s.DeleteTopic(id); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"status": 200})
    }
}

func getTopic(s topic.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        id := c.Param("id")
        var userID *string
        if uid := c.GetString(state.CurrentUserID); uid != "" { userID = &uid }
        resp, err := s.GetTopic(id, userID)
        if err != nil { c.AbortWithStatusJSON(404, gin.H{"error": err.Error(), "status": 404}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func getTopics(s topic.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        var req dtos.TopicListRequest
        if err := c.ShouldBindQuery(&req); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        var userID *string
        if uid := c.GetString(state.CurrentUserID); uid != "" { userID = &uid }
        resp, err := s.GetTopics(&req, userID)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func updateTopicProgress(s topic.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" { c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401}); return }
        var req dtos.UpdateTopicProgressRequest
        if err := c.ShouldBindJSON(&req); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        // Allow path param to override
        if tid := c.Param("id"); tid != "" {
            if parsed, err := uuid.Parse(tid); err == nil {
                req.TopicID = parsed
            }
        }
        resp, err := s.UpdateProgress(userID, &req)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func getTopicProgress(s topic.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        if userID == "" { c.AbortWithStatusJSON(401, gin.H{"error": "User not authenticated", "status": 401}); return }
        topicID := c.Param("id")
        resp, err := s.GetProgress(userID, topicID)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}
