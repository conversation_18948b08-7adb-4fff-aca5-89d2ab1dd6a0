import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';
import { PreferencesResponse, UpdatePreferencesRequest } from '../types/preferences';

export const preferencesService = {
  async get(): Promise<ApiResponse<PreferencesResponse>> {
    return http.get<PreferencesResponse>(endpoints.preferences!.get);
  },

  async update(payload: UpdatePreferencesRequest): Promise<ApiResponse<PreferencesResponse>> {
    return http.put<PreferencesResponse>(endpoints.preferences!.update, payload);
  },
};

export default preferencesService;

