import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Clock, 
  ChevronLeft, 
  ChevronRight, 
  Flag,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import { Question, QuizSession as QuizSessionType, QuizAnswer } from '../../types/quiz';
import { quizService } from '../../services/quizService';
import { quizSessionService } from '../../services/quizSessionService';

const QuizSession: React.FC = () => {
  const { quizId, sessionId } = useParams<{ quizId: string; sessionId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({});
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmFinish, setShowConfirmFinish] = useState(false);

  const currentQuestion = questions[currentQuestionIndex];

  useEffect(() => {
    if (quizId) {
      loadQuestions();
    }
  }, [quizId]);

  useEffect(() => {
    if (timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleFinishQuiz();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
    return undefined;
  }, [timeRemaining]);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load questions using actual API
      const response = await quizService.getQuizQuestions(quizId!);

      if (response.success && response.data) {
        setQuestions(response.data);

        // Set time remaining based on quiz time limit
        const quizResponse = await quizService.getQuiz(quizId!);
        if (quizResponse.success && quizResponse.data) {
          setTimeRemaining(quizResponse.data.time_limit * 60); // Convert minutes to seconds
        }
      } else {
        throw new Error('Failed to load questions');
      }
    } catch (err) {
      setError('An error occurred while loading questions');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId: string, answer: string | string[]) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmitAnswer = async (questionId: string, answer: string | string[]) => {
    if (!sessionId) return;

    try {
      // Submit answer using actual API
      await quizSessionService.submitAnswer(sessionId, {
        question_id: questionId,
        answer: answer,
        time_taken: 60 // You might want to track actual time taken
      });
    } catch (err) {
      console.error('Failed to submit answer:', err);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestion && answers[currentQuestion.id]) {
      handleSubmitAnswer(currentQuestion.id, answers[currentQuestion.id]);
    }
    
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleFinishQuiz = async () => {
    if (!sessionId) return;

    try {
      setSubmitting(true);

      // Submit current answer if exists
      if (currentQuestion && answers[currentQuestion.id]) {
        await handleSubmitAnswer(currentQuestion.id, answers[currentQuestion.id]);
      }

      // Finish quiz using actual API
      const response = await quizSessionService.finishQuizSession(sessionId);

      if (response.success && response.data) {
        navigate(`/quizzes/${quizId}/results/${response.data.id}`);
      } else {
        throw new Error('Failed to finish quiz');
      }

    } catch (err) {
      setError('An error occurred while finishing the quiz');
    } finally {
      setSubmitting(false);
      setShowConfirmFinish(false);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    return ((currentQuestionIndex + 1) / questions.length) * 100;
  };

  const getAnsweredCount = () => {
    return Object.keys(answers).length;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error || !currentQuestion) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-2">Error loading quiz</div>
        <p className="text-dark-400">{error}</p>
        <button
          onClick={() => navigate(`/quizzes/${quizId}`)}
          className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          Back to Quiz
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold text-white">
              Question {currentQuestionIndex + 1} of {questions.length}
            </h1>
            <div className="flex items-center gap-2 text-dark-300">
              <CheckCircle size={16} />
              <span>{getAnsweredCount()} answered</span>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 px-3 py-1 rounded-lg ${
              timeRemaining < 300 ? 'bg-red-600/20 text-red-400' : 'bg-dark-700 text-dark-300'
            }`}>
              <Clock size={16} />
              <span className="font-mono">{formatTime(timeRemaining)}</span>
            </div>
            
            <button
              onClick={() => setShowConfirmFinish(true)}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
            >
              <Flag size={16} />
              Finish
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-dark-700 rounded-full h-2">
          <div 
            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>
      </div>

      {/* Question */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-3">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              currentQuestion.difficulty === 'easy' ? 'bg-green-400/10 text-green-400' :
              currentQuestion.difficulty === 'medium' ? 'bg-yellow-400/10 text-yellow-400' :
              'bg-red-400/10 text-red-400'
            }`}>
              {currentQuestion.difficulty}
            </span>
            <span className="text-dark-400 text-sm">{currentQuestion.points} points</span>
          </div>
          
          <h2 className="text-xl font-semibold text-white mb-4">
            {currentQuestion.question_text}
          </h2>

          {currentQuestion.media && (
            <div className="mb-4">
              {currentQuestion.media.type === 'image' && (
                <img 
                  src={currentQuestion.media.url} 
                  alt={currentQuestion.media.alt_text}
                  className="max-w-full h-auto rounded-lg"
                />
              )}
            </div>
          )}
        </div>

        {/* Answer Options */}
        <div className="space-y-3">
          {currentQuestion.question_type === 'multiple_choice' && currentQuestion.options && (
            <>
              {currentQuestion.options.map((option) => (
                <label
                  key={option.id}
                  className={`flex items-center p-4 rounded-lg border cursor-pointer transition-all ${
                    answers[currentQuestion.id] === option.id
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-dark-600 bg-dark-700 hover:border-dark-500'
                  }`}
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion.id}`}
                    value={option.id}
                    checked={answers[currentQuestion.id] === option.id}
                    onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                    answers[currentQuestion.id] === option.id
                      ? 'border-primary-500 bg-primary-500'
                      : 'border-dark-400'
                  }`}>
                    {answers[currentQuestion.id] === option.id && (
                      <div className="w-2 h-2 rounded-full bg-white"></div>
                    )}
                  </div>
                  <span className="text-white">{option.text}</span>
                </label>
              ))}
            </>
          )}

          {currentQuestion.question_type === 'true_false' && (
            <>
              {['true', 'false'].map((option) => (
                <label
                  key={option}
                  className={`flex items-center p-4 rounded-lg border cursor-pointer transition-all ${
                    answers[currentQuestion.id] === option
                      ? 'border-primary-500 bg-primary-500/10'
                      : 'border-dark-600 bg-dark-700 hover:border-dark-500'
                  }`}
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion.id}`}
                    value={option}
                    checked={answers[currentQuestion.id] === option}
                    onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                    answers[currentQuestion.id] === option
                      ? 'border-primary-500 bg-primary-500'
                      : 'border-dark-400'
                  }`}>
                    {answers[currentQuestion.id] === option && (
                      <div className="w-2 h-2 rounded-full bg-white"></div>
                    )}
                  </div>
                  <span className="text-white capitalize">{option}</span>
                </label>
              ))}
            </>
          )}

          {currentQuestion.question_type === 'fill_blank' && (
            <input
              type="text"
              value={answers[currentQuestion.id] as string || ''}
              onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
              placeholder="Type your answer here..."
              className="w-full p-4 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:border-primary-500"
            />
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <button
          onClick={handlePreviousQuestion}
          disabled={currentQuestionIndex === 0}
          className="flex items-center gap-2 px-4 py-2 bg-dark-700 hover:bg-dark-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          <ChevronLeft size={20} />
          Previous
        </button>

        <div className="flex items-center gap-2">
          {questions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentQuestionIndex(index)}
              className={`w-8 h-8 rounded-full text-sm font-medium transition-colors ${
                index === currentQuestionIndex
                  ? 'bg-primary-500 text-white'
                  : answers[questions[index].id]
                  ? 'bg-green-600 text-white'
                  : 'bg-dark-700 text-dark-300 hover:bg-dark-600'
              }`}
            >
              {index + 1}
            </button>
          ))}
        </div>

        <button
          onClick={handleNextQuestion}
          disabled={currentQuestionIndex === questions.length - 1}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          Next
          <ChevronRight size={20} />
        </button>
      </div>

      {/* Confirm Finish Modal */}
      {showConfirmFinish && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-dark-800 rounded-xl p-6 max-w-md w-full mx-4 border border-dark-700">
            <div className="flex items-center gap-3 mb-4">
              <AlertCircle className="text-yellow-400" size={24} />
              <h3 className="text-lg font-semibold text-white">Finish Quiz?</h3>
            </div>
            
            <p className="text-dark-300 mb-6">
              Are you sure you want to finish this quiz? You have answered {getAnsweredCount()} out of {questions.length} questions.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={() => setShowConfirmFinish(false)}
                className="flex-1 px-4 py-2 bg-dark-700 hover:bg-dark-600 text-white rounded-lg transition-colors"
              >
                Continue Quiz
              </button>
              <button
                onClick={handleFinishQuiz}
                disabled={submitting}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors"
              >
                {submitting ? 'Finishing...' : 'Finish Quiz'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuizSession;
