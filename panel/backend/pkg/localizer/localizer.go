package localizer

import (
	"encoding/json"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
)

func GetTranslated(keyword string, langTag string, templateDate map[string]interface{}) string {
	bundle := i18n.NewBundle(language.English)
	bundle.RegisterUnmarshalFunc("json", i18n.UnmarshalFunc(json.Unmarshal))

	// Try to load locale files if they exist
	localeFiles := []string{
		"./locale/en.json",
		"./locale/tr.json",
		"./locale/pl.json",
		"./locale/es.json",
	}

	for _, file := range localeFiles {
		bundle.LoadMessageFile(file) // Ignore errors if files don't exist
	}

	localizer := i18n.NewLocalizer(bundle, langTag)

	localizedMessage, _ := localizer.Localize(&i18n.LocalizeConfig{
		MessageID:    keyword,
		TemplateData: templateDate,
	})

	// If no translation found, return the keyword itself
	if localizedMessage == "" {
		return keyword
	}

	return localizedMessage
}
