package entities

import (
	"time"

	"github.com/google/uuid"
)

type Base struct {
	ID        uuid.UUID `gorm:"type:uuid;default:gen_random_uuid();primaryKey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type User struct {
	Base
	Username string  `json:"username"`
	Email    *string `json:"email"`
	Name     string  `json:"name"`
	IsActive bool    `json:"is_active"`
}

func (User) TableName() string { return "users" }

// Alan (<PERSON>l Yet<PERSON>k, Genel Kültür, Eğitim Bilimleri, ÖABT...)
type Area struct {
	Base
	Name        string  `json:"name"`
	Code        string  `json:"code"`
	Description *string `json:"description"`
	Order       int     `json:"order"`
	IsActive    bool    `json:"is_active"`
}

func (Area) TableName() string { return "areas" }

// Ders (Türkçe, Matematik, Tarih, Coğrafya, Vatandaşlık...)
type Subject struct {
	Base
	AreaID      uuid.UUID `json:"area_id" gorm:"type:uuid"`
	Name        string    `json:"name"`
	Code        string    `json:"code"`
	Description *string   `json:"description"`
	Order       int       `json:"order"`
	IsActive    bool      `json:"is_active"`
}

func (Subject) TableName() string { return "subjects" }

// Konu (ünite/alt-konu ağaç yapısı)
type Topic struct {
	Base
	SubjectID   *uuid.UUID `json:"subject_id" gorm:"type:uuid"`
	ParentID    *uuid.UUID `json:"parent_id" gorm:"type:uuid"`
	Title       string     `json:"title"`
	Description *string    `json:"description"`
	Body        *string    `json:"body"`
	Order       int        `json:"order"`
	IsPublished bool       `json:"is_published"`

	// MEB Kazanım Kodu
	AchievementCode *string `json:"achievement_code"`
}

func (Topic) TableName() string { return "topics" }

// İçerik Tipleri
const (
	ContentTypeExplanation = "explanation" // Konu Anlatımı
	ContentTypeQuestion    = "question"    // Soru
	ContentTypeMiniTest    = "mini_test"   // Mini Test
	ContentTypeExam        = "exam"        // Deneme Sınavı
	ContentTypePDF         = "pdf"         // PDF/Ek
	ContentTypeVideo       = "video"       // Video
	ContentTypeDocument    = "document"    // Doküman
)

// Zorluk Seviyeleri
const (
	DifficultyEasy   = "easy"   // Kolay
	DifficultyMedium = "medium" // Orta
	DifficultyHard   = "hard"   // Zor
)

type Content struct {
	Base
	TopicID     *uuid.UUID `json:"topic_id" gorm:"type:uuid"`
	Title       string     `json:"title"`
	Description *string    `json:"description"`
	Body        *string    `json:"body"` // HTML content for explanations

	// İçerik Tipi
	Type string `json:"type"` // explanation, question, mini_test, exam, pdf, video, document

	// Dosya/URL bilgileri
	URL        *string `json:"url"`
	FilePath   *string `json:"file_path"`
	TotalPages *int    `json:"total_pages"`
	TotalTime  *int    `json:"total_time"`
	Duration   *string `json:"duration"`

	// Etiketler
	Difficulty      *string `json:"difficulty"`       // easy, medium, hard
	AchievementCode *string `json:"achievement_code"` // MEB kazanım kodu
	Year            *int    `json:"year"`
	Tags            *string `json:"tags"` // JSON array as string

	// Yayın bilgileri
	IsOfficial  bool    `json:"is_official"`
	Author      *string `json:"author"`
	Publisher   *string `json:"publisher"`
	ISBN        *string `json:"isbn"`
	ChannelName *string `json:"channel_name"`

	// Sıralama ve durum
	Order       int  `json:"order"`
	IsPublished bool `json:"is_published"`
}

func (Content) TableName() string { return "contents" }

// Soru Tipleri
const (
	QuestionTypeMultipleChoice = "multiple_choice" // Çoktan seçmeli
	QuestionTypeTrueFalse      = "true_false"      // Doğru/Yanlış
	QuestionTypeFillBlank      = "fill_blank"      // Boşluk doldurma
	QuestionTypeEssay          = "essay"           // Açık uçlu
)

type Question struct {
	Base
	QuizID *uuid.UUID `json:"quiz_id"` // Opsiyonel quiz referansı

	// Soru içeriği
	Text          string  `json:"text" gorm:"not null"`
	OptionA       string  `json:"option_a"`
	OptionB       string  `json:"option_b"`
	OptionC       string  `json:"option_c"`
	OptionD       string  `json:"option_d"`
	OptionE       *string `json:"option_e"`
	CorrectAnswer string  `json:"correct_answer"`

	// Açıklama ve ipuçları
	Explanation *string `json:"explanation"`
	Subject     *string `json:"subject"`
}

func (Question) TableName() string { return "questions" }

// Quiz Tipleri
const (
	QuizTypeOfficial = "official" // Resmi sınav
	QuizTypePractice = "practice" // Deneme sınavı
	QuizTypeMini     = "mini"     // Mini test
)

type Quiz struct {
	Base
	Title       string  `json:"title"`
	Description *string `json:"description"`
	Type        string  `json:"type"`       // official, practice, mini
	Subject     *string `json:"subject"`    // Hangi ders/alan
	Difficulty  *string `json:"difficulty"` // easy, medium, hard
	TimeLimit   *int    `json:"time_limit"` // Dakika cinsinden
	IsPublic    bool    `json:"is_public"`
	IsActive    bool    `json:"is_active"`
}

func (Quiz) TableName() string { return "quizzes" }

type Log struct {
	Base
	Title   string `json:"title"`
	Message string `json:"message"`
	Entity  string `json:"entity"`
	Type    string `json:"type"`
	Proto   string `json:"proto"`
	Ip      string `json:"ip"`
}

func (Log) TableName() string { return "logs" }
