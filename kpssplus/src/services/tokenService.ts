import {apiClient} from './api';
import {TokenTransaction, TokenPackage} from '../types';

export class TokenService {
  // Get user's current token balance
  async getTokenBalance(): Promise<number> {
    const response = await apiClient.get<{tokens: number}>('/user/tokens');
    return response.data.tokens;
  }

  // Get token transaction history
  async getTokenHistory(page = 1, limit = 20): Promise<TokenTransaction[]> {
    const response = await apiClient.get<TokenTransaction[]>(
      `/user/tokens/history?page=${page}&limit=${limit}`,
    );
    return response.data;
  }

  // Spend tokens for an action
  async spendTokens(
    amount: number,
    reason: string,
    relatedId?: string,
  ): Promise<{success: boolean; remainingTokens: number}> {
    const response = await apiClient.post<{
      success: boolean;
      remainingTokens: number;
    }>('/user/tokens/spend', {
      amount,
      reason,
      relatedId,
    });
    return response.data;
  }

  // Check if user has enough tokens
  async hasEnoughTokens(requiredAmount: number): Promise<boolean> {
    try {
      const balance = await this.getTokenBalance();
      return balance >= requiredAmount;
    } catch (error) {
      console.error('Error checking token balance:', error);
      return false;
    }
  }

  // Get available token packages for purchase
  async getTokenPackages(): Promise<TokenPackage[]> {
    const response = await apiClient.get<TokenPackage[]>('/tokens/packages');
    return response.data;
  }

  // Purchase token package
  async purchaseTokens(packageId: string): Promise<{
    success: boolean;
    transactionId: string;
    newBalance: number;
  }> {
    const response = await apiClient.post<{
      success: boolean;
      transactionId: string;
      newBalance: number;
    }>('/tokens/purchase', {
      packageId,
    });
    return response.data;
  }

  // Earn tokens for completing actions
  async earnTokens(
    amount: number,
    reason: string,
    relatedId?: string,
  ): Promise<{success: boolean; newBalance: number}> {
    const response = await apiClient.post<{
      success: boolean;
      newBalance: number;
    }>('/user/tokens/earn', {
      amount,
      reason,
      relatedId,
    });
    return response.data;
  }

  // Get daily bonus tokens
  async claimDailyBonus(): Promise<{
    success: boolean;
    tokensEarned: number;
    newBalance: number;
    nextClaimAt: string;
  }> {
    const response = await apiClient.post<{
      success: boolean;
      tokensEarned: number;
      newBalance: number;
      nextClaimAt: string;
    }>('/user/tokens/daily-bonus');
    return response.data;
  }

  // Check if daily bonus is available
  async isDailyBonusAvailable(): Promise<{
    available: boolean;
    nextClaimAt?: string;
  }> {
    const response = await apiClient.get<{
      available: boolean;
      nextClaimAt?: string;
    }>('/user/tokens/daily-bonus/status');
    return response.data;
  }
}

export const tokenService = new TokenService();
