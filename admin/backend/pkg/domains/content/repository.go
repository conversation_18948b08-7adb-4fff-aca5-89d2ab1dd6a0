package content

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Repository interface {
	GetAll(limit, offset int, search string) ([]entities.Content, int64, error)
	GetByTopicID(topicID uuid.UUID) ([]entities.Content, error)
	GetByType(contentType string) ([]entities.Content, error)
	GetByID(id uuid.UUID) (*entities.Content, error)
	Create(content *entities.Content) error
	Update(content *entities.Content) error
	Delete(id uuid.UUID) error
	Publish(id uuid.UUID) error
	Unpublish(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetAll(limit, offset int, search string) ([]entities.Content, int64, error) {
	var contents []entities.Content
	var total int64

    query := r.db.Model(&entities.Content{})

	if search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

    err = query.Order("title ASC, created_at DESC").Limit(limit).Offset(offset).Find(&contents).Error
	return contents, total, err
}

func (r *repository) GetByTopicID(topicID uuid.UUID) ([]entities.Content, error) {
	var contents []entities.Content
    err := r.db.
        Where("topic_id = ?", topicID).
        Order("title ASC, created_at DESC").Find(&contents).Error
	return contents, err
}

func (r *repository) GetByType(contentType string) ([]entities.Content, error) {
	var contents []entities.Content
    err := r.db.
        Where("type = ?", contentType).
        Order("title ASC, created_at DESC").Find(&contents).Error
	return contents, err
}

func (r *repository) GetByID(id uuid.UUID) (*entities.Content, error) {
	var content entities.Content
    err := r.db.
        First(&content, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &content, nil
}

func (r *repository) Create(content *entities.Content) error {
	return r.db.Create(content).Error
}

func (r *repository) Update(content *entities.Content) error {
	return r.db.Save(content).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Content{}, "id = ?", id).Error
}

func (r *repository) Publish(id uuid.UUID) error {
	return r.db.Model(&entities.Content{}).Where("id = ?", id).Update("is_published", true).Error
}

func (r *repository) Unpublish(id uuid.UUID) error {
	return r.db.Model(&entities.Content{}).Where("id = ?", id).Update("is_published", false).Error
}
