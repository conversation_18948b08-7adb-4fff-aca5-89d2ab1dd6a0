import {apiClient} from './api';
import {
  Content,
  ContentProgress,
  PaginatedResponse,
  SearchParams,
  ApiResponse,
} from '../types';

export class ContentService {
  // Get content list with pagination and filters
  async getContentList(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      '/content',
      params,
    );
    return response.data;
  }

  // Get single content by ID
  async getContent(id: string): Promise<Content> {
    const response = await apiClient.get<Content>(`/content/${id}`);
    return response.data;
  }

  // Search content
  async searchContent(
    params: SearchParams,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      '/content/search',
      params,
    );
    return response.data;
  }

  // Get content by type
  async getContentByType(
    type: string,
    params?: SearchParams,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/type/${type}`,
      params,
    );
    return response.data;
  }

  // Get content by subject
  async getContentBySubject(
    subject: string,
    params?: SearchParams,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/subject/${subject}`,
      params,
    );
    return response.data;
  }

  // Get popular content
  async getPopularContent(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      '/content/popular',
      params,
    );
    return response.data;
  }

  // Get content statistics
  async getContentStats(id: string): Promise<any> {
    const response = await apiClient.get(`/content/${id}/stats`);
    return response.data;
  }

  // Get recommended content (requires auth)
  async getRecommendedContent(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      '/content/recommended',
      params,
    );
    return response.data;
  }

  // User's content library (requires auth)
  async getUserLibrary(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      '/content/library',
      params,
    );
    return response.data;
  }

  // Add content to library (requires auth)
  async addToLibrary(contentId: string): Promise<ApiResponse> {
    return await apiClient.post(`/content/${contentId}/library`);
  }

  // Remove content from library (requires auth)
  async removeFromLibrary(contentId: string): Promise<ApiResponse> {
    return await apiClient.delete(`/content/${contentId}/library`);
  }

  // Progress tracking (requires auth)
  async updateProgress(
    contentId: string,
    progress: number,
    timeSpent: number,
    notes?: string,
  ): Promise<ContentProgress> {
    const response = await apiClient.put<ContentProgress>('/content/progress', {
      contentId,
      progress,
      timeSpent,
      notes,
    });
    return response.data;
  }

  // Get content progress (requires auth)
  async getContentProgress(contentId: string): Promise<ContentProgress> {
    const response = await apiClient.get<ContentProgress>(
      `/content/${contentId}/progress`,
    );
    return response.data;
  }

  // Get all user progress (requires auth)
  async getAllUserProgress(
    params?: SearchParams,
  ): Promise<PaginatedResponse<ContentProgress>> {
    const response = await apiClient.get<PaginatedResponse<ContentProgress>>(
      '/content/progress',
      params,
    );
    return response.data;
  }

  // Content creation (requires auth - admin/creator)
  async createContent(contentData: Partial<Content>): Promise<Content> {
    const response = await apiClient.post<Content>('/content', contentData);
    return response.data;
  }

  // Content update (requires auth - admin/creator)
  async updateContent(
    id: string,
    contentData: Partial<Content>,
  ): Promise<Content> {
    const response = await apiClient.put<Content>(
      `/content/${id}`,
      contentData,
    );
    return response.data;
  }

  // Content deletion (requires auth - admin/creator)
  async deleteContent(id: string): Promise<ApiResponse> {
    return await apiClient.delete(`/content/${id}`);
  }

  // Guest mode helpers
  async getGuestContent(limit: number = 5): Promise<Content[]> {
    const guestData = await apiClient.getGuestData();
    const viewedIds = guestData?.viewedContents || [];

    // Get popular content excluding already viewed
    const response = await this.getPopularContent({
      limit: limit + viewedIds.length,
    });

    return response.data
      .filter(content => !viewedIds.includes(content.id))
      .slice(0, limit);
  }

  async trackGuestView(contentId: string): Promise<void> {
    const guestData = (await apiClient.getGuestData()) || {
      viewedContents: [],
      attemptedQuizzes: [],
      lastViewedAt: new Date().toISOString(),
      limitReached: false,
    };

    if (!guestData.viewedContents.includes(contentId)) {
      guestData.viewedContents.push(contentId);
      guestData.lastViewedAt = new Date().toISOString();

      // Check if limit reached (e.g., 10 contents for guest users)
      if (guestData.viewedContents.length >= 10) {
        guestData.limitReached = true;
      }

      await apiClient.setGuestData(guestData);
    }
  }

  async isGuestLimitReached(): Promise<boolean> {
    const guestData = await apiClient.getGuestData();
    return guestData?.limitReached || false;
  }

  async getGuestViewCount(): Promise<number> {
    const guestData = await apiClient.getGuestData();
    return guestData?.viewedContents?.length || 0;
  }
}

// Export singleton instance
export const contentService = new ContentService();
