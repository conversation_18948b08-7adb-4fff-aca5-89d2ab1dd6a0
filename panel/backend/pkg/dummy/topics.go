package dummy

import (
    "log"

    "github.com/kpss-plus-backend/pkg/database"
    "github.com/kpss-plus-backend/pkg/entities"
)

// SeedTopicsIfEmpty creates a small demo topic tree if there are no topics.
func SeedTopicsIfEmpty() {
    db := database.DBClient()

    var cnt int64
    if err := db.Model(&entities.Topic{}).Count(&cnt).Error; err != nil {
        log.Println("seed topics count error:", err)
        return
    }
    if cnt > 0 {
        return
    }

    // Root topics
    vatan := entities.Topic{Title: "Vatandaşlık", Subject: strPtr("GYGK"), Order: 1}
    tarih := entities.Topic{Title: "Tarih", Subject: strPtr("GYGK"), Order: 2}
    co := db.Create(&vatan)
    if co.Error != nil { log.Println("seed create topic error:", co.Error); return }
    if err := db.Create(&tarih).Error; err != nil { log.Println("seed create topic error:", err); return }

    // Children of Vatandaşlık
    th := entities.Topic{Title: "Temel Haklar", Subject: vatan.Subject, ParentID: &vatan.ID, Order: 1}
    yasama := entities.Topic{Title: "Yasama", Subject: vatan.Subject, ParentID: &vatan.ID, Order: 2}
    yurutme := entities.Topic{Title: "Yürütme", Subject: vatan.Subject, ParentID: &vatan.ID, Order: 3}
    yargi := entities.Topic{Title: "Yargı", Subject: vatan.Subject, ParentID: &vatan.ID, Order: 4}
    if err := db.Create(&[]entities.Topic{th, yasama, yurutme, yargi}).Error; err != nil {
        log.Println("seed create children error:", err)
    }
}

func strPtr(s string) *string { return &s }

