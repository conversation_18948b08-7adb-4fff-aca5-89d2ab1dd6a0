# New APIs Overview (Preferences, Library, Quiz Sessions)

## Preferences
- GET `/api/v1/preferences` (public)
  - Returns: `{ data: PreferencesResponse, status: 200 }`
- PUT `/api/v1/preferences` (auth)
  - Body: `UpdatePreferencesRequest`
  - Returns updated preferences.

Example PUT body:
```
{
  "interests": ["GenelKultur", "Tarih"],
  "subject_weights": {"Tarih": 0.7, "Cografya": 0.3},
  "difficulty_pref": "Medium"
}
```

## Library
- GET `/api/v1/library?type=&folder_id=&page=&limit=&details=true|false`
  - `details=true` → returns item details for rendering cards
- POST `/api/v1/library/{type}/{id}`
  - Body (optional): `{ "folder_id": null | "<uuid>" }`
- DELETE `/api/v1/library/{type}/{id}`
- Folders: GET/POST/PUT/DELETE `/api/v1/library/folders[/:id]`

## Quiz Sessions
- POST `/api/v1/quiz/{id}/start` → creates session
- POST `/api/v1/quiz/sessions/{sessionId}/answer` → upserts answer
- POST `/api/v1/quiz/sessions/{sessionId}/finish` → computes result
- GET `/api/v1/quiz/results/{resultId}` → detailed result
- GET `/api/v1/quiz/results` → list results

Notes
- Time limit enforcement: If quiz has `time_limit`, submitting after it expires returns `time limit exceeded`.
- See Postman collection: `backend/docs/postman/new_features.postman_collection.json`.
