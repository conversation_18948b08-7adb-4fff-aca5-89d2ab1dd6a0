package preferences

import (
    "encoding/json"
    "github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
    Get(userID string) (*dtos.PreferencesResponse, error)
    Update(userID string, req *dtos.UpdatePreferencesRequest) (*dtos.PreferencesResponse, error)
}

type service struct{ repo Repository }

func NewService(r Repository) Service { return &service{repo: r} }

func (s *service) Get(userID string) (*dtos.PreferencesResponse, error) {
    return s.repo.Get(userID)
}

func (s *service) Update(userID string, req *dtos.UpdatePreferencesRequest) (*dtos.PreferencesResponse, error) {
    // Normalize: empty maps/slices to nil in storage
    if req.SubjectWeights == nil { req.SubjectWeights = map[string]float64{} }
    if req.Interests == nil { req.Interests = []string{} }
    // shallow validation of difficulty
    if req.DifficultyPref != nil {
        v := *req.DifficultyPref
        if v != "Easy" && v != "Medium" && v != "Hard" && v != "Any" {
            any := "Any"
            req.DifficultyPref = &any
        }
    }
    // ensure JSON encodes properly (defensive)
    if _, err := json.Marshal(req.SubjectWeights); err != nil { return nil, err }
    if _, err := json.Marshal(req.Interests); err != nil { return nil, err }
    return s.repo.Upsert(userID, req)
}

