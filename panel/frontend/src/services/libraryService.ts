import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';
import {
  LibraryItemType,
  LibraryListResponse,
  LibraryListWithDetailsResponse,
  FoldersResponse,
  FolderResponse,
  LibraryItemResponse,
} from '../types/library';

export const libraryService = {
  async getLibraryItems(params: { type?: LibraryItemType; folder_id?: string; page?: number; limit?: number; details?: boolean } = {}): Promise<ApiResponse<LibraryListResponse | LibraryListWithDetailsResponse>> {
    return http.get(endpoints.library!.list, { params });
  },

  async list(params: { type?: LibraryItemType; folder_id?: string; page?: number; limit?: number; details?: boolean } = {}): Promise<ApiResponse<LibraryListResponse | LibraryListWithDetailsResponse>> {
    return http.get(endpoints.library!.list, { params });
  },

  async getFavoriteItems(params: { page?: number; limit?: number } = {}): Promise<ApiResponse<any>> {
    return http.get('/library/favorites', { params });
  },

  async getRecentItems(params: { page?: number; limit?: number } = {}): Promise<ApiResponse<any>> {
    return http.get('/library/recent', { params });
  },

  async searchLibraryItems(query: string, params: { type?: LibraryItemType; limit?: number } = {}): Promise<ApiResponse<any>> {
    return http.get('/library/search', { params: { search: query, ...params } });
  },

  async add(type: LibraryItemType, id: string, folder_id?: string | null): Promise<ApiResponse<LibraryItemResponse>> {
    return http.post(endpoints.library!.add(type, id), folder_id !== undefined ? { folder_id } : undefined);
  },

  async remove(type: LibraryItemType, id: string): Promise<ApiResponse<unknown>> {
    return http.delete(endpoints.library!.remove(type, id));
  },

  async getFolders(): Promise<ApiResponse<FoldersResponse>> {
    return http.get(endpoints.library!.folders);
  },

  async folders(): Promise<ApiResponse<FoldersResponse>> {
    return http.get(endpoints.library!.folders);
  },

  async createFolder(name: string): Promise<ApiResponse<FolderResponse>> {
    return http.post(endpoints.library!.createFolder, { name });
  },

  async renameFolder(id: string, name: string): Promise<ApiResponse<FolderResponse>> {
    return http.put(endpoints.library!.updateFolder(id), { name });
  },

  async deleteFolder(id: string): Promise<ApiResponse<unknown>> {
    return http.delete(endpoints.library!.deleteFolder(id));
  },
};

export default libraryService;

