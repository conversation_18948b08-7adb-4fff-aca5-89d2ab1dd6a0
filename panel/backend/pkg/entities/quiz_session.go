package entities

import (
    "time"
    "github.com/google/uuid"
)

type QuizSession struct {
    Base
    UserID    *uuid.UUID `json:"user_id" gorm:"index"`
    QuizID    *uuid.UUID `json:"quiz_id" gorm:"index"`
    StartedAt time.Time  `json:"started_at"`
    EndedAt   *time.Time `json:"ended_at"`
    IsFinished bool      `json:"is_finished" gorm:"default:false"`
}

func (QuizSession) TableName() string { return "quiz_sessions" }

type QuizSessionAnswer struct {
    Base
    SessionID  *uuid.UUID `json:"session_id" gorm:"index"`
    QuestionID *uuid.UUID `json:"question_id" gorm:"index"`
    UserAnswer *string    `json:"user_answer"` // A/B/C/D/E or nil for skip
    TimeSpent  *int       `json:"time_spent"`   // seconds
}

func (QuizSessionAnswer) TableName() string { return "quiz_session_answers" }

