package progress

import (
	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// Progress tracking
	UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error)
	GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error)
	GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error)

	// Progress analytics
	GetProgressStats(userID string) (*dtos.ProgressStatsResponse, error)
	GetProgressHistory(userID string, payload *dtos.GetProgressHistoryRequest) (*dtos.ProgressHistoryResponse, error)
	GetStudyStreak(userID string) (*dtos.StudyStreakResponse, error)

	// Study sessions
	StartStudySession(userID string, payload *dtos.StartStudySessionRequest) (*dtos.StudySessionResponse, error)
	EndStudySession(userID string, sessionID string) (*dtos.StudySessionResponse, error)
	GetStudySessions(userID string, payload *dtos.GetStudySessionsRequest) (*dtos.StudySessionsResponse, error)

	// Timeline integration
	CreateProgressTimelineEntry(userID string, progressID string) error
	CreateStudySessionTimelineEntry(userID string, sessionID string) error

	// Progress goals
	SetProgressGoal(userID string, payload *dtos.SetProgressGoalRequest) (*dtos.ProgressGoalResponse, error)
	GetProgressGoals(userID string) (*dtos.ProgressGoalsResponse, error)
	UpdateProgressGoal(userID string, goalID string, payload *dtos.UpdateProgressGoalRequest) (*dtos.ProgressGoalResponse, error)
	DeleteProgressGoal(userID string, goalID string) error

	// Progress achievements
	CheckProgressAchievements(userID string) ([]dtos.AchievementResponse, error)
	GetProgressAchievements(userID string) (*dtos.AchievementsResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// Progress tracking
func (s *service) UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error) {
	// Update progress
	progress, err := s.repository.UpdateProgress(userID, payload)
	if err != nil {
		return nil, err
	}

	// Create timeline entry for significant progress updates
	if progress.Percentage >= 25 && int(progress.Percentage)%25 == 0 {
		s.CreateProgressTimelineEntry(userID, progress.ID.String())
	}

	// Check for achievements
	s.CheckProgressAchievements(userID)

	return progress, nil
}

func (s *service) GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error) {
	return s.repository.GetProgress(userID, contentID)
}

func (s *service) GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error) {
	return s.repository.GetUserProgress(userID, payload)
}

// Progress analytics
func (s *service) GetProgressStats(userID string) (*dtos.ProgressStatsResponse, error) {
	return s.repository.GetProgressStats(userID)
}

func (s *service) GetProgressHistory(userID string, payload *dtos.GetProgressHistoryRequest) (*dtos.ProgressHistoryResponse, error) {
	return s.repository.GetProgressHistory(userID, payload)
}

func (s *service) GetStudyStreak(userID string) (*dtos.StudyStreakResponse, error) {
	return s.repository.GetStudyStreak(userID)
}

// Study sessions
func (s *service) StartStudySession(userID string, payload *dtos.StartStudySessionRequest) (*dtos.StudySessionResponse, error) {
	return s.repository.StartStudySession(userID, payload)
}

func (s *service) EndStudySession(userID string, sessionID string) (*dtos.StudySessionResponse, error) {
	session, err := s.repository.EndStudySession(userID, sessionID)
	if err != nil {
		return nil, err
	}

	// Create timeline entry for completed study sessions
	if session.Duration != nil && *session.Duration >= 300 { // 5 minutes minimum
		s.CreateStudySessionTimelineEntry(userID, sessionID)
	}

	// Update user stats
	s.repository.UpdateUserStatsFromSession(userID, session)

	return session, nil
}

func (s *service) GetStudySessions(userID string, payload *dtos.GetStudySessionsRequest) (*dtos.StudySessionsResponse, error) {
	return s.repository.GetStudySessions(userID, payload)
}

// Timeline integration
func (s *service) CreateProgressTimelineEntry(userID string, progressID string) error {
	return s.repository.CreateProgressTimelineEntry(userID, progressID)
}

func (s *service) CreateStudySessionTimelineEntry(userID string, sessionID string) error {
	return s.repository.CreateStudySessionTimelineEntry(userID, sessionID)
}

// Progress goals
func (s *service) SetProgressGoal(userID string, payload *dtos.SetProgressGoalRequest) (*dtos.ProgressGoalResponse, error) {
	return s.repository.SetProgressGoal(userID, payload)
}

func (s *service) GetProgressGoals(userID string) (*dtos.ProgressGoalsResponse, error) {
	return s.repository.GetProgressGoals(userID)
}

func (s *service) UpdateProgressGoal(userID string, goalID string, payload *dtos.UpdateProgressGoalRequest) (*dtos.ProgressGoalResponse, error) {
	return s.repository.UpdateProgressGoal(userID, goalID, payload)
}

func (s *service) DeleteProgressGoal(userID string, goalID string) error {
	return s.repository.DeleteProgressGoal(userID, goalID)
}

// Progress achievements
func (s *service) CheckProgressAchievements(userID string) ([]dtos.AchievementResponse, error) {
	return s.repository.CheckProgressAchievements(userID)
}

func (s *service) GetProgressAchievements(userID string) (*dtos.AchievementsResponse, error) {
	return s.repository.GetProgressAchievements(userID)
}
