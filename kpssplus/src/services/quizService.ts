import {apiClient} from './api';
import {
  Quiz,
  Question,
  QuizSession,
  QuizResult,
  PaginatedResponse,
  SearchParams,
  ApiResponse,
} from '../types';

export class QuizService {
  // Get quiz list with pagination and filters
  async getQuizList(params?: SearchParams): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      '/quiz',
      params,
    );
    return response.data;
  }

  // Get single quiz by ID
  async getQuiz(id: string): Promise<Quiz> {
    const response = await apiClient.get<Quiz>(`/quiz/${id}`);
    return response.data;
  }

  // Search quizzes
  async searchQuizzes(params: SearchParams): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      '/quiz/search',
      params,
    );
    return response.data;
  }

  // Get quizzes by subject
  async getQuizzesBySubject(
    subject: string,
    params?: SearchParams,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      `/quiz/subject/${subject}`,
      params,
    );
    return response.data;
  }

  // Get popular quizzes
  async getPopularQuizzes(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      '/quiz/popular',
      params,
    );
    return response.data;
  }

  // Get quiz questions
  async getQuizQuestions(quizId: string): Promise<Question[]> {
    const response = await apiClient.get<Question[]>(
      `/quiz/${quizId}/questions`,
    );
    return response.data;
  }

  // Get quiz statistics
  async getQuizStatistics(quizId: string): Promise<any> {
    const response = await apiClient.get(`/quiz/${quizId}/statistics`);
    return response.data;
  }

  // Get quiz leaderboard
  async getQuizLeaderboard(
    quizId: string,
    params?: {limit?: number; type?: 'global' | 'friends'},
  ): Promise<any[]> {
    const response = await apiClient.get(`/quiz/${quizId}/leaderboard`, params);
    return response.data as any[];
  }

  // Get recommended quizzes (requires auth)
  async getRecommendedQuizzes(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      '/quiz/recommended',
      params,
    );
    return response.data;
  }

  // Get similar quizzes
  async getSimilarQuizzes(
    quizId: string,
    params?: SearchParams,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      `/quiz/${quizId}/similar`,
      params,
    );
    return response.data;
  }

  // Quiz session management (requires auth)
  async startQuizSession(quizId: string): Promise<QuizSession> {
    const response = await apiClient.post<QuizSession>(`/quiz/${quizId}/start`);
    return response.data;
  }

  async submitAnswer(
    sessionId: string,
    questionId: string,
    selectedAnswer: number,
  ): Promise<ApiResponse> {
    return await apiClient.post(`/quiz/sessions/${sessionId}/answer`, {
      questionId,
      selectedAnswer,
    });
  }

  async finishQuizSession(sessionId: string): Promise<QuizResult> {
    const response = await apiClient.post<QuizResult>(
      `/quiz/sessions/${sessionId}/finish`,
    );
    return response.data;
  }

  // Get quiz results
  async getQuizResult(resultId: string): Promise<QuizResult> {
    const response = await apiClient.get<QuizResult>(
      `/quiz/results/${resultId}`,
    );
    return response.data;
  }

  async getUserQuizResults(
    params?: SearchParams,
  ): Promise<PaginatedResponse<QuizResult>> {
    const response = await apiClient.get<PaginatedResponse<QuizResult>>(
      '/quiz/results',
      params,
    );
    return response.data;
  }

  // Quiz management (requires auth - admin/creator)
  async createQuiz(quizData: Partial<Quiz>): Promise<Quiz> {
    const response = await apiClient.post<Quiz>('/quiz', quizData);
    return response.data;
  }

  async updateQuiz(id: string, quizData: Partial<Quiz>): Promise<Quiz> {
    const response = await apiClient.put<Quiz>(`/quiz/${id}`, quizData);
    return response.data;
  }

  async deleteQuiz(id: string): Promise<ApiResponse> {
    return await apiClient.delete(`/quiz/${id}`);
  }

  async getUserQuizzes(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      '/quiz/my',
      params,
    );
    return response.data;
  }

  // Quiz sharing
  async shareQuiz(
    quizId: string,
    shareData: {platform: string; message?: string},
  ): Promise<ApiResponse> {
    return await apiClient.post(`/quiz/${quizId}/share`, shareData);
  }

  async inviteToQuiz(
    quizId: string,
    inviteData: {userIds: string[]; message?: string},
  ): Promise<ApiResponse> {
    return await apiClient.post(`/quiz/${quizId}/invite`, inviteData);
  }

  async getSharedQuizzes(
    params?: SearchParams,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      '/quiz/shared',
      params,
    );
    return response.data;
  }

  // Guest mode helpers
  async getGuestQuizzes(limit: number = 3): Promise<Quiz[]> {
    const guestData = await apiClient.getGuestData();
    const attemptedIds = guestData?.attemptedQuizzes || [];

    // Get popular quizzes excluding already attempted
    const response = await this.getPopularQuizzes({
      limit: limit + attemptedIds.length,
    });

    return response.data
      .filter(quiz => !attemptedIds.includes(quiz.id))
      .slice(0, limit);
  }

  async trackGuestQuizAttempt(quizId: string): Promise<void> {
    const guestData = (await apiClient.getGuestData()) || {
      viewedContents: [],
      attemptedQuizzes: [],
      lastViewedAt: new Date().toISOString(),
      limitReached: false,
    };

    if (!guestData.attemptedQuizzes.includes(quizId)) {
      guestData.attemptedQuizzes.push(quizId);
      guestData.lastViewedAt = new Date().toISOString();

      // Check if limit reached (e.g., 3 quizzes for guest users)
      if (guestData.attemptedQuizzes.length >= 3) {
        guestData.limitReached = true;
      }

      await apiClient.setGuestData(guestData);
    }
  }

  async isGuestQuizLimitReached(): Promise<boolean> {
    const guestData = await apiClient.getGuestData();
    return guestData?.limitReached || false;
  }

  async getGuestQuizAttemptCount(): Promise<number> {
    const guestData = await apiClient.getGuestData();
    return guestData?.attemptedQuizzes?.length || 0;
  }

  // Quiz session helpers for guest mode
  async canStartQuiz(
    _quizId: string,
  ): Promise<{canStart: boolean; reason?: string}> {
    const tokens = await apiClient.getAuthTokens();

    if (tokens) {
      // Authenticated user can always start
      return {canStart: true};
    }

    // Guest user - check limits
    const isLimitReached = await this.isGuestQuizLimitReached();
    if (isLimitReached) {
      return {
        canStart: false,
        reason: 'Guest limit reached. Please login to continue.',
      };
    }

    return {canStart: true};
  }
}

// Export singleton instance
export const quizService = new QuizService();
