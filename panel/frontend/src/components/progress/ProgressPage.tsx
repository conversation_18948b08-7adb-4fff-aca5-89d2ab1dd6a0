import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Target,
  Award,
  Clock,
  BookOpen,
  Flame,
  Calendar,
  BarChart3,
  Plus,
  Star,
  CheckCircle
} from 'lucide-react';
import { 
  ProgressStats, 
  ProgressHistory, 
  ProgressGoal, 
  Achievement,
  StudySession,
  progressService 
} from '../../services/progressService';

const ProgressPage: React.FC = () => {
  const { t } = useTranslation();
  const [stats, setStats] = useState<ProgressStats | null>(null);
  const [history, setHistory] = useState<ProgressHistory[]>([]);
  const [goals, setGoals] = useState<ProgressGoal[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [sessions, setSessions] = useState<StudySession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'goals' | 'achievements' | 'history'>('overview');

  useEffect(() => {
    loadProgressData();
  }, []);

  const loadProgressData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsRes, historyRes, goalsRes, achievementsRes, sessionsRes] = await Promise.all([
        progressService.getProgressStats(),
        progressService.getProgressHistory({ limit: 30 }),
        progressService.getProgressGoals(),
        progressService.getAchievements(),
        progressService.getStudySessions({ limit: 10 })
      ]);

      if (statsRes.success && statsRes.data) setStats(statsRes.data);
      if (historyRes.success && historyRes.data) setHistory(Array.isArray(historyRes.data) ? historyRes.data : []);
      if (goalsRes.success && goalsRes.data) setGoals(Array.isArray(goalsRes.data) ? goalsRes.data : []);
      if (achievementsRes.success && achievementsRes.data) setAchievements(Array.isArray(achievementsRes.data) ? achievementsRes.data : []);
      if (sessionsRes.success && sessionsRes.data) setSessions(Array.isArray(sessionsRes.data) ? sessionsRes.data : []);

    } catch (err) {
      setError('Failed to load progress data');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getGoalProgress = (goal: ProgressGoal) => {
    return Math.min((goal.current_value / goal.target_value) * 100, 100);
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'border-purple-500 bg-purple-500/10 text-purple-400';
      case 'epic': return 'border-orange-500 bg-orange-500/10 text-orange-400';
      case 'rare': return 'border-blue-500 bg-blue-500/10 text-blue-400';
      default: return 'border-gray-500 bg-gray-500/10 text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-2">Error loading progress</div>
        <p className="text-dark-400">{error}</p>
        <button
          onClick={loadProgressData}
          className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">{t('progress.title')}</h1>
          <p className="text-dark-300 mt-1">Track your learning journey and achievements</p>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <div className="flex items-center gap-3 mb-3">
              <Clock className="text-primary-400" size={24} />
              <h3 className="text-white font-semibold">Study Time</h3>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {formatTime(stats.total_study_time)}
            </div>
            <div className="text-dark-400 text-sm">Total time spent</div>
          </div>

          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <div className="flex items-center gap-3 mb-3">
              <BookOpen className="text-green-400" size={24} />
              <h3 className="text-white font-semibold">Quizzes</h3>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {stats.quizzes_completed}
            </div>
            <div className="text-dark-400 text-sm">Completed</div>
          </div>

          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <div className="flex items-center gap-3 mb-3">
              <Target className="text-yellow-400" size={24} />
              <h3 className="text-white font-semibold">Average Score</h3>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {stats.average_score}%
            </div>
            <div className="text-dark-400 text-sm">Across all quizzes</div>
          </div>

          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <div className="flex items-center gap-3 mb-3">
              <Flame className="text-red-400" size={24} />
              <h3 className="text-white font-semibold">Study Streak</h3>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {stats.current_streak}
            </div>
            <div className="text-dark-400 text-sm">Days in a row</div>
          </div>
        </div>
      )}

      {/* Level Progress */}
      {stats && (
        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Star className="text-yellow-400" size={24} />
              <div>
                <h3 className="text-white font-semibold">Level {stats.level}</h3>
                <p className="text-dark-400 text-sm">{stats.total_points} total points</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-white font-semibold">{stats.experience_points} / {stats.next_level_points} XP</div>
              <div className="text-dark-400 text-sm">to next level</div>
            </div>
          </div>
          
          <div className="w-full bg-dark-700 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-primary-500 to-secondary-500 h-3 rounded-full transition-all duration-300"
              style={{ width: `${(stats.experience_points / stats.next_level_points) * 100}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
        {[
          { key: 'overview', label: 'Overview', icon: BarChart3 },
          { key: 'goals', label: 'Goals', icon: Target },
          { key: 'achievements', label: 'Achievements', icon: Award },
          { key: 'history', label: 'History', icon: Calendar }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as typeof activeTab)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === key
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:text-white hover:bg-dark-700'
            }`}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Recent Study Sessions</h3>
              <div className="space-y-3">
                {sessions.slice(0, 5).map((session) => (
                  <div key={session.id} className="bg-dark-700 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-white font-medium">
                          {session.content_type === 'quiz' ? 'Quiz Session' : 'Study Session'}
                        </div>
                        <div className="text-dark-400 text-sm">
                          {formatDate(session.started_at)} • {formatTime(session.duration)}
                        </div>
                      </div>
                      <div className="text-primary-400 font-semibold">
                        +{session.points_earned} points
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Weekly Progress</h3>
              <div className="grid grid-cols-7 gap-2">
                {history.slice(-7).map((day, index) => (
                  <div key={index} className="bg-dark-700 rounded-lg p-3 text-center">
                    <div className="text-dark-400 text-xs mb-1">
                      {new Date(day.date).toLocaleDateString('en', { weekday: 'short' })}
                    </div>
                    <div className="text-white font-semibold">{day.quizzes_completed}</div>
                    <div className="text-dark-400 text-xs">quizzes</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'goals' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Your Goals</h3>
              <button className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                <Plus size={16} />
                Add Goal
              </button>
            </div>
            
            <div className="space-y-3">
              {goals.map((goal) => (
                <div key={goal.id} className="bg-dark-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <div className="text-white font-medium">
                        {goal.target_type.replace('_', ' ')} - {goal.type}
                      </div>
                      <div className="text-dark-400 text-sm">
                        Target: {goal.target_value} • Deadline: {formatDate(goal.deadline)}
                      </div>
                    </div>
                    {goal.is_achieved && (
                      <CheckCircle className="text-green-400" size={20} />
                    )}
                  </div>
                  
                  <div className="w-full bg-dark-600 rounded-full h-2 mb-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        goal.is_achieved ? 'bg-green-500' : 'bg-primary-500'
                      }`}
                      style={{ width: `${getGoalProgress(goal)}%` }}
                    ></div>
                  </div>
                  
                  <div className="text-dark-400 text-sm">
                    {goal.current_value} / {goal.target_value} ({Math.round(getGoalProgress(goal))}%)
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'achievements' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Achievements</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map((achievement) => (
                <div 
                  key={achievement.id} 
                  className={`rounded-lg p-4 border ${
                    achievement.unlocked_at 
                      ? getRarityColor(achievement.rarity)
                      : 'border-dark-600 bg-dark-700 opacity-50'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className="text-2xl">{achievement.icon}</div>
                    <div>
                      <h4 className="text-white font-semibold">{achievement.name}</h4>
                      <p className="text-dark-300 text-sm">{achievement.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(achievement.rarity)}`}>
                      {achievement.rarity}
                    </span>
                    <div className="text-yellow-400 text-sm">+{achievement.points} points</div>
                  </div>
                  
                  {achievement.unlocked_at && (
                    <div className="text-dark-400 text-xs mt-2">
                      Unlocked {formatDate(achievement.unlocked_at)}
                    </div>
                  )}
                  
                  {!achievement.unlocked_at && achievement.progress !== undefined && (
                    <div className="mt-3">
                      <div className="w-full bg-dark-600 rounded-full h-1">
                        <div 
                          className="bg-primary-500 h-1 rounded-full"
                          style={{ width: `${(achievement.progress / (achievement.max_progress || 1)) * 100}%` }}
                        ></div>
                      </div>
                      <div className="text-dark-400 text-xs mt-1">
                        {achievement.progress} / {achievement.max_progress}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Study History</h3>
            
            <div className="space-y-3">
              {history.map((day, index) => (
                <div key={index} className="bg-dark-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-white font-medium">{formatDate(day.date)}</div>
                    <div className="text-primary-400 font-semibold">+{day.points_earned} points</div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-dark-400">Study Time</div>
                      <div className="text-white">{formatTime(day.study_time)}</div>
                    </div>
                    <div>
                      <div className="text-dark-400">Quizzes</div>
                      <div className="text-white">{day.quizzes_completed}</div>
                    </div>
                    <div>
                      <div className="text-dark-400">Activities</div>
                      <div className="text-white">{day.activities.length}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressPage;
