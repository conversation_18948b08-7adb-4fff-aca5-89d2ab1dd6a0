package dummy

import (
	"github.com/kpss-plus-backend/pkg/consts"
	"github.com/kpss-plus-backend/pkg/database"
	"github.com/kpss-plus-backend/pkg/entities"
	"github.com/kpss-plus-backend/pkg/utils"
)

func CreateDummy() {
	CreateDummyUser()
	CreateDummyAdmin()
}

func CreateDummyUser() {
	db := database.DBClient()

	var user entities.User
	db.Where(consts.UserName+" = ?", "samet").First(&user)
	if user.Username != "" {
		return
	}

	pass := utils.Bcrypt("SametAvci05")
	db.Create(&entities.User{
		Username: "samet",
		Password: pass,
		Name:     "Samet Avci",
	})
}

func CreateDummyAdmin() {
	db := database.DBClient()

	var adminUser entities.User
	db.Where("username = ?", "admin").First(&adminUser)
	if adminUser.Username != "" {
		return
	}

	adminPass := utils.Bcrypt("admin123")
	db.Create(&entities.User{
		Username: "admin",
		Password: adminPass,
		Name:     "Admin User",
		IsAdmin:  true,
	})
}
