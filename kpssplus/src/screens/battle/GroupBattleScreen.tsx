import React, {useState, useEffect} from 'react';
import {View, Text, ScrollView, Alert, RefreshControl} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import {Card} from '../../components/ui/Card';
import {Button} from '../../components/ui/Button';
import {Icon} from '../../components/ui';
import {colors} from '../../theme/colors';
import {typography} from '../../theme/typography';
import {spacing} from '../../theme/spacing';
import {useBattle, useAuth} from '../../hooks';
import {GroupBattle} from '../../types';
import {StyleSheet} from 'react-native';

type Props = {};

export const GroupBattleScreen: React.FC<Props> = () => {
  const {isAuthenticated, isGuest} = useAuth();
  const {groupBattles, loadGroupBattles, joinGroupBattle} = useBattle();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      loadGroupBattles();
    }
  }, [isAuthenticated, loadGroupBattles]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadGroupBattles();
    setRefreshing(false);
  };

  const handleCreateBattle = () => {
    if (isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Grup kapışması oluşturmak için giriş yapmalısınız.',
        [
          {text: 'İptal', style: 'cancel'},
          {
            text: 'Giriş Yap',
            onPress: () => console.log('Navigation disabled'),
          },
        ],
      );
      return;
    }
    console.log('Navigation disabled');
  };

  const handleJoinBattle = async (battleId: string) => {
    if (isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Grup kapışmasına katılmak için giriş yapmalısınız.',
        [
          {text: 'İptal', style: 'cancel'},
          {
            text: 'Giriş Yap',
            onPress: () => console.log('Navigation disabled'),
          },
        ],
      );
      return;
    }

    const success = await joinGroupBattle(battleId);
    if (success) {
      console.log('Navigation disabled');
    }
  };

  const handleBattlePress = (_battle: GroupBattle) => {
    console.log('Navigation disabled');
  };

  const renderBattleCard = (battle: GroupBattle) => {
    const isWaiting = battle.status === 'waiting';
    // const isActive = battle.status === 'active';
    // const isCompleted = battle.status === 'completed';

    return (
      <Card
        key={battle.id}
        style={styles.battleCard}
        variant="elevated"
        onPress={() => handleBattlePress(battle)}>
        <View style={styles.battleHeader}>
          <View style={styles.battleInfo}>
            <Text style={styles.battleTitle}>{battle.name}</Text>
            <Text style={styles.battleQuiz}>{battle.quiz.title}</Text>
            {battle.description && (
              <Text style={styles.battleDescription}>{battle.description}</Text>
            )}
          </View>
          <View style={[styles.statusBadge, styles[`status${battle.status}`]]}>
            <Text
              style={[styles.statusText, styles[`statusText${battle.status}`]]}>
              {battle.status === 'waiting' && 'Bekliyor'}
              {battle.status === 'active' && 'Aktif'}
              {battle.status === 'completed' && 'Tamamlandı'}
            </Text>
          </View>
        </View>

        <View style={styles.battleDetails}>
          <View style={styles.detailItem}>
            <Icon
              name="people-outline"
              size={16}
              color={colors.text.secondary}
            />
            <Text style={styles.detailText}>
              {battle.currentParticipants}/{battle.maxParticipants}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Icon name="time-outline" size={16} color={colors.text.secondary} />
            <Text style={styles.detailText}>{battle.duration} dk</Text>
          </View>
          <View style={styles.detailItem}>
            <Icon
              name="help-circle-outline"
              size={16}
              color={colors.text.secondary}
            />
            <Text style={styles.detailText}>
              {battle.quiz.questionCount} soru
            </Text>
          </View>
          {battle.isPrivate && (
            <View style={styles.detailItem}>
              <Icon
                name="lock-closed-outline"
                size={16}
                color={colors.warning}
              />
              <Text style={[styles.detailText, {color: colors.warning}]}>
                Özel
              </Text>
            </View>
          )}
        </View>

        <View style={styles.battleFooter}>
          <View style={styles.creatorInfo}>
            <Text style={styles.creatorText}>
              Oluşturan: {battle.creator.firstName} {battle.creator.lastName}
            </Text>
          </View>
          {isWaiting && battle.currentParticipants < battle.maxParticipants && (
            <Button
              title="Katıl"
              size="small"
              onPress={() => handleJoinBattle(battle.id)}
              style={styles.joinButton}
            />
          )}
        </View>
      </Card>
    );
  };

  if (isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.guestContainer}>
          <Icon name="people-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.guestTitle}>Grup Kapışması</Text>
          <Text style={styles.guestDescription}>
            Arkadaşlarınızla grup halinde quiz çözmek ve kapışmak için giriş
            yapın.
          </Text>
          <Button
            title="Giriş Yap"
            onPress={() => console.log('Navigation disabled')}
            style={styles.loginButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Grup Kapışması</Text>
          <Text style={styles.headerSubtitle}>
            Arkadaşlarınızla birlikte quiz çözün ve kapışın!
          </Text>
        </View>

        {/* Create Battle Button */}
        <Card style={styles.createCard} variant="outlined">
          <View style={styles.createContent}>
            <Icon name="add-circle-outline" size={32} color={colors.primary} />
            <Text style={styles.createTitle}>Yeni Grup Kapışması</Text>
            <Text style={styles.createDescription}>
              Kendi grup kapışmanızı oluşturun ve arkadaşlarınızı davet edin
            </Text>
            <Button
              title="Kapışma Oluştur"
              onPress={handleCreateBattle}
              style={styles.createButton}
            />
          </View>
        </Card>

        {/* Active Battles */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Aktif Kapışmalar</Text>
          {groupBattles.length === 0 ? (
            <Card style={styles.emptyCard} variant="outlined">
              <Icon name="trophy-outline" size={48} color={colors.gray[400]} />
              <Text style={styles.emptyTitle}>Henüz kapışma yok</Text>
              <Text style={styles.emptyDescription}>
                İlk grup kapışmasını siz oluşturun!
              </Text>
            </Card>
          ) : (
            groupBattles.map(renderBattleCard)
          )}
        </View>

        {/* Quick Join */}
        <Card style={styles.quickJoinCard} variant="outlined">
          <View style={styles.quickJoinContent}>
            <Icon name="flash-outline" size={24} color={colors.secondary} />
            <Text style={styles.quickJoinTitle}>Hızlı Katılım</Text>
            <Text style={styles.quickJoinDescription}>
              Davet kodunuz var mı? Hızlıca katılın!
            </Text>
            <Button
              title="Kod ile Katıl"
              variant="outline"
              size="small"
              onPress={() => console.log('Navigation disabled')}
              style={styles.quickJoinButton}
            />
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    padding: spacing[4],
  },
  guestContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing[6],
  },
  guestTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  guestDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[6],
  },
  loginButton: {
    minWidth: 120,
  },
  header: {
    marginBottom: spacing[6],
  },
  headerTitle: {
    ...typography.h3,
    marginBottom: spacing[1],
  },
  headerSubtitle: {
    ...typography.body1,
    color: colors.text.secondary,
  },
  createCard: {
    marginBottom: spacing[6],
  },
  createContent: {
    alignItems: 'center',
    padding: spacing[4],
  },
  createTitle: {
    ...typography.h5,
    marginTop: spacing[2],
    marginBottom: spacing[1],
  },
  createDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[4],
  },
  createButton: {
    minWidth: 150,
  },
  section: {
    marginBottom: spacing[6],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  battleCard: {
    marginBottom: spacing[3],
    padding: spacing[4],
  },
  battleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  battleInfo: {
    flex: 1,
    marginRight: spacing[3],
  },
  battleTitle: {
    ...typography.h6,
    marginBottom: spacing[1],
  },
  battleQuiz: {
    ...typography.body2,
    color: colors.primary,
    marginBottom: spacing[1],
  },
  battleDescription: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  statusBadge: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 12,
  },
  statuswaiting: {
    backgroundColor: colors.warning + '20',
  },
  statusactive: {
    backgroundColor: colors.success + '20',
  },
  statuscompleted: {
    backgroundColor: colors.gray[200],
  },
  statusText: {
    ...typography.caption,
    fontWeight: '600',
  },
  statusTextwaiting: {
    color: colors.warning,
  },
  statusTextactive: {
    color: colors.success,
  },
  statusTextcompleted: {
    color: colors.text.secondary,
  },
  battleDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing[3],
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing[4],
    marginBottom: spacing[1],
  },
  detailText: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  battleFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  creatorInfo: {
    flex: 1,
  },
  creatorText: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  joinButton: {
    minWidth: 80,
  },
  emptyCard: {
    alignItems: 'center',
    padding: spacing[6],
  },
  emptyTitle: {
    ...typography.h6,
    marginTop: spacing[3],
    marginBottom: spacing[1],
  },
  emptyDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  quickJoinCard: {
    marginBottom: spacing[4],
  },
  quickJoinContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[4],
  },
  quickJoinTitle: {
    ...typography.body1,
    fontWeight: '600',
    marginLeft: spacing[3],
    flex: 1,
  },
  quickJoinDescription: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[3],
    flex: 2,
  },
  quickJoinButton: {
    minWidth: 100,
  },
  // Additional status styles
  statuscancelled: {
    backgroundColor: colors.error + '20',
  },
  // Additional status text styles
  statusTextcancelled: {
    color: colors.error,
  },
});
