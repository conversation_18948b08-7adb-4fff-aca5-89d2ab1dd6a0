package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/kpss-plus-backend/pkg/domains/library"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/middleware"
    "github.com/kpss-plus-backend/pkg/state"
)

func LibraryRoutes(r *gin.RouterGroup, s library.Service) {
    g := r.Group("/library")
    g.Use(middleware.Authorized())

    // Items
    g.GET("", listLibrary(s))
    g.POST(":type/:id", addLibraryItem(s))
    g.DELETE(":type/:id", removeLibraryItem(s))

    // Folders
    g.GET("/folders", listFolders(s))
    g.POST("/folders", createFolder(s))
    g.PUT("/folders/:id", updateFolder(s))
    g.DELETE("/folders/:id", deleteFolder(s))
}

func listLibrary(s library.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.GetLibraryRequest
        if err := c.ShouldBindQuery(&req); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        if c.DefaultQuery("details", "false") == "true" {
            resp, err := s.ListWithDetails(userID, &req)
            if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
            c.JSON(200, gin.H{"data": resp, "status": 200})
            return
        }
        resp, err := s.List(userID, &req)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func addLibraryItem(s library.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        itemType := c.Param("type")
        itemID := c.Param("id")
        var body struct{ FolderID *string `json:"folder_id"` }
        _ = c.ShouldBindJSON(&body)
        resp, err := s.AddItem(userID, itemType, itemID, body.FolderID)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(201, gin.H{"data": resp, "status": 201})
    }
}

func removeLibraryItem(s library.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        itemType := c.Param("type")
        itemID := c.Param("id")
        if err := s.RemoveItem(userID, itemType, itemID); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"status": 200})
    }
}

func listFolders(s library.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        resp, err := s.ListFolders(userID)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func createFolder(s library.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.CreateFolderRequest
        if err := c.ShouldBindJSON(&req); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        resp, err := s.CreateFolder(userID, req.Name)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(201, gin.H{"data": resp, "status": 201})
    }
}

func updateFolder(s library.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        folderID := c.Param("id")
        var req dtos.UpdateFolderRequest
        if err := c.ShouldBindJSON(&req); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        resp, err := s.UpdateFolder(userID, folderID, req.Name)
        if err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func deleteFolder(s library.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        folderID := c.Param("id")
        if err := s.DeleteFolder(userID, folderID); err != nil { c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400}); return }
        c.JSON(200, gin.H{"status": 200})
    }
}
