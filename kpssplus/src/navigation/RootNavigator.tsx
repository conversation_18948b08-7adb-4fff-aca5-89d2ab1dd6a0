import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {RootStackParamList} from './types';
import {MainTabNavigator} from './MainTabNavigator';
import {AuthStackNavigator} from './AuthStackNavigator';
import {ModalStackNavigator} from './ModalStackNavigator';
import {useAuth} from '../hooks/useAuth';
import {LoadingScreen} from '../screens/common/LoadingScreen';

const Stack = createStackNavigator<RootStackParamList>();

export const RootNavigator: React.FC = () => {
  const {isLoading} = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            presentation: 'card',
          }}>
          {/* Main App Flow - Always show (supports guest mode) */}
          <Stack.Screen name="Main" component={MainTabNavigator} />

          {/* Auth Flow - Modal presentation */}
          <Stack.Group screenOptions={{presentation: 'modal'}}>
            <Stack.Screen name="Auth" component={AuthStackNavigator} />
          </Stack.Group>

          {/* Other Modals */}
          <Stack.Group screenOptions={{presentation: 'modal'}}>
            <Stack.Screen name="Modal" component={ModalStackNavigator} />
          </Stack.Group>
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
};
