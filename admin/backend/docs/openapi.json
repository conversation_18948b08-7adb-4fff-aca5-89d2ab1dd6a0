{"openapi": "3.0.3", "info": {"title": "KPSS Plus Admin API", "version": "0.1.0"}, "paths": {"/api/healthz": {"get": {"summary": "Health"}}, "/api/auth/login": {"post": {"summary": "Admin login"}}, "/api/users": {"get": {"summary": "List users", "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "schema": {"type": "integer"}}, {"name": "offset", "in": "query", "schema": {"type": "integer"}}, {"name": "sort", "in": "query", "schema": {"type": "string"}}]}}, "/api/topics": {"get": {"summary": "List topics"}, "post": {"summary": "Create topic"}}, "/api/topics/{id}": {"put": {"summary": "Update topic"}, "delete": {"summary": "Delete topic"}}, "/api/topics/{id}/publish": {"patch": {"summary": "Publish topic"}}, "/api/topics/{id}/unpublish": {"patch": {"summary": "Unpublish topic"}}, "/api/contents": {"get": {"summary": "List contents"}, "post": {"summary": "Create content"}}, "/api/contents/{id}": {"put": {"summary": "Update content"}, "delete": {"summary": "Delete content"}}, "/api/questions": {"get": {"summary": "List questions"}, "post": {"summary": "Create question"}}, "/api/questions/{id}": {"put": {"summary": "Update question"}, "delete": {"summary": "Delete question"}}, "/api/questions/{id}/rendered": {"get": {"summary": "Rendered question with shuffled options"}}, "/api/import/questions/csv": {"post": {"summary": "Import questions from CSV (multipart/form-data)"}}, "/api/import/questions/template.csv": {"get": {"summary": "Download CSV template for questions"}}, "/api/auth/admins": {"get": {"summary": "List admin users"}, "post": {"summary": "Create admin user"}}, "/api/auth/admins/{id}": {"put": {"summary": "Update admin user"}, "delete": {"summary": "Delete admin user"}}}}