package dtos

import (
	"time"
	"github.com/google/uuid"
)

// Badge Management DTOs
type GetBadgesRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Category string `json:"category" validate:"omitempty"`
	Type     string `json:"type" validate:"omitempty,oneof=study quiz progress social achievement"`
	IsActive *bool  `json:"is_active"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=name created_at difficulty rarity"`
	SortDesc bool   `json:"sort_desc"`
}

type BadgeResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	Type        string    `json:"type"`
	IconURL     string    `json:"icon_url"`
	Difficulty  string    `json:"difficulty"` // Easy, Medium, Hard, Expert
	Rarity      string    `json:"rarity"`     // Common, Rare, Epic, Legendary
	Points      int       `json:"points"`
	IsActive    bool      `json:"is_active"`
	
	// Requirements
	Requirements map[string]interface{} `json:"requirements"`
	MaxProgress  int                    `json:"max_progress"`
	
	// Statistics
	TotalEarned    int     `json:"total_earned"`
	EarnedRate     float64 `json:"earned_rate"`
	AverageTime    *int    `json:"average_time"` // Average time to earn in days
	
	// User-specific data (if user is authenticated)
	UserProgress   *int  `json:"user_progress,omitempty"`
	UserEarned     bool  `json:"user_earned"`
	UserEarnedAt   *time.Time `json:"user_earned_at,omitempty"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type BadgesResponse struct {
	Badges     []BadgeResponse `json:"badges"`
	Total      int             `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

type CreateBadgeRequest struct {
	Name         string                 `json:"name" validate:"required,min=1,max=100"`
	Description  string                 `json:"description" validate:"required,min=1,max=500"`
	Category     string                 `json:"category" validate:"required,min=1,max=50"`
	Type         string                 `json:"type" validate:"required,oneof=study quiz progress social achievement"`
	IconURL      string                 `json:"icon_url" validate:"required,url"`
	Difficulty   string                 `json:"difficulty" validate:"required,oneof=Easy Medium Hard Expert"`
	Rarity       string                 `json:"rarity" validate:"required,oneof=Common Rare Epic Legendary"`
	Points       int                    `json:"points" validate:"required,min=1,max=10000"`
	Requirements map[string]interface{} `json:"requirements" validate:"required"`
	MaxProgress  int                    `json:"max_progress" validate:"required,min=1"`
	IsActive     bool                   `json:"is_active"`
}

type UpdateBadgeRequest struct {
	Name         *string                `json:"name" validate:"omitempty,min=1,max=100"`
	Description  *string                `json:"description" validate:"omitempty,min=1,max=500"`
	Category     *string                `json:"category" validate:"omitempty,min=1,max=50"`
	Type         *string                `json:"type" validate:"omitempty,oneof=study quiz progress social achievement"`
	IconURL      *string                `json:"icon_url" validate:"omitempty,url"`
	Difficulty   *string                `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard Expert"`
	Rarity       *string                `json:"rarity" validate:"omitempty,oneof=Common Rare Epic Legendary"`
	Points       *int                   `json:"points" validate:"omitempty,min=1,max=10000"`
	Requirements map[string]interface{} `json:"requirements"`
	MaxProgress  *int                   `json:"max_progress" validate:"omitempty,min=1"`
	IsActive     *bool                  `json:"is_active"`
}

// User Badge Management DTOs
type GetUserBadgesRequest struct {
	Page       int    `json:"page" validate:"omitempty,min=1"`
	Limit      int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Category   string `json:"category" validate:"omitempty"`
	Type       string `json:"type" validate:"omitempty,oneof=study quiz progress social achievement"`
	IsEarned   *bool  `json:"is_earned"`
	SortBy     string `json:"sort_by" validate:"omitempty,oneof=earned_at progress name difficulty"`
	SortDesc   bool   `json:"sort_desc"`
}

type UserBadgeResponse struct {
	ID          uuid.UUID `json:"id"`
	UserID      uuid.UUID `json:"user_id"`
	BadgeID     uuid.UUID `json:"badge_id"`
	Progress    int       `json:"progress"`
	MaxProgress int       `json:"max_progress"`
	IsCompleted bool      `json:"is_completed"`
	EarnedAt    *time.Time `json:"earned_at"`
	
	// Badge info
	Badge BadgeResponse `json:"badge"`
	
	// Progress details
	ProgressPercentage float64 `json:"progress_percentage"`
	RemainingProgress  int     `json:"remaining_progress"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type UserBadgesResponse struct {
	UserBadges []UserBadgeResponse `json:"user_badges"`
	Total      int                 `json:"total"`
	Page       int                 `json:"page"`
	Limit      int                 `json:"limit"`
	TotalPages int                 `json:"total_pages"`
	
	// Summary statistics
	TotalEarned    int     `json:"total_earned"`
	TotalInProgress int    `json:"total_in_progress"`
	TotalPoints    int     `json:"total_points"`
	CompletionRate float64 `json:"completion_rate"`
}

type AwardBadgeRequest struct {
	BadgeID  uuid.UUID `json:"badge_id" validate:"required"`
	Progress int       `json:"progress" validate:"omitempty,min=0"`
}

type UpdateBadgeProgressRequest struct {
	Progress int `json:"progress" validate:"required,min=0"`
}

// Achievement Tracking DTOs
type CheckAchievementsRequest struct {
	Type     string                 `json:"type" validate:"required,oneof=study quiz progress social"`
	Data     map[string]interface{} `json:"data" validate:"required"`
	UserData map[string]interface{} `json:"user_data"`
}

type AchievementCheckResponse struct {
	NewBadges     []UserBadgeResponse `json:"new_badges"`
	UpdatedBadges []UserBadgeResponse `json:"updated_badges"`
	TotalPoints   int                 `json:"total_points"`
	Message       string              `json:"message"`
}

type GetAchievementHistoryRequest struct {
	Page     int       `json:"page" validate:"omitempty,min=1"`
	Limit    int       `json:"limit" validate:"omitempty,min=1,max=100"`
	Category string    `json:"category" validate:"omitempty"`
	Type     string    `json:"type" validate:"omitempty,oneof=study quiz progress social achievement"`
	FromDate *time.Time `json:"from_date"`
	ToDate   *time.Time `json:"to_date"`
	SortDesc bool      `json:"sort_desc"`
}

type AchievementHistoryResponse struct {
	Achievements []AchievementHistoryEntry `json:"achievements"`
	Total        int                       `json:"total"`
	Page         int                       `json:"page"`
	Limit        int                       `json:"limit"`
	TotalPages   int                       `json:"total_pages"`
}

type AchievementHistoryEntry struct {
	ID       uuid.UUID `json:"id"`
	UserID   uuid.UUID `json:"user_id"`
	BadgeID  uuid.UUID `json:"badge_id"`
	Badge    BadgeResponse `json:"badge"`
	EarnedAt time.Time `json:"earned_at"`
	Points   int       `json:"points"`
}

type GetLeaderboardRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Period   string `json:"period" validate:"omitempty,oneof=all_time weekly monthly yearly"`
	Category string `json:"category" validate:"omitempty"`
	Type     string `json:"type" validate:"omitempty,oneof=study quiz progress social achievement"`
}

type BadgeLeaderboardResponse struct {
	Entries    []LeaderboardEntry `json:"entries"`
	Total      int                `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
	
	// User's position (if authenticated)
	UserRank   *int `json:"user_rank"`
	UserPoints *int `json:"user_points"`
}

type LeaderboardEntry struct {
	Rank        int      `json:"rank"`
	User        UserInfo `json:"user"`
	TotalPoints int      `json:"total_points"`
	TotalBadges int      `json:"total_badges"`
	RecentBadges []BadgeResponse `json:"recent_badges"`
}

// Badge Categories and Collections DTOs
type BadgeCategoriesResponse struct {
	Categories []BadgeCategory `json:"categories"`
}

type BadgeCategory struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	IconURL     string `json:"icon_url"`
	BadgeCount  int    `json:"badge_count"`
	UserEarned  int    `json:"user_earned"`
}

type GetBadgesByCategoryRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Type     string `json:"type" validate:"omitempty,oneof=study quiz progress social achievement"`
	IsActive *bool  `json:"is_active"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=name created_at difficulty rarity"`
	SortDesc bool   `json:"sort_desc"`
}

type BadgeCollectionsResponse struct {
	Collections []BadgeCollection `json:"collections"`
}

type BadgeCollection struct {
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Badges       []BadgeResponse `json:"badges"`
	TotalBadges  int             `json:"total_badges"`
	EarnedBadges int             `json:"earned_badges"`
	Completion   float64         `json:"completion"`
}

// Badge Statistics DTOs
type BadgeStatsResponse struct {
	BadgeID     uuid.UUID `json:"badge_id"`
	TotalUsers  int       `json:"total_users"`
	EarnedCount int       `json:"earned_count"`
	EarnedRate  float64   `json:"earned_rate"`
	
	// Time-based statistics
	AverageTimeToEarn float64 `json:"average_time_to_earn"` // in days
	FastestTimeToEarn *int    `json:"fastest_time_to_earn"` // in days
	
	// Recent activity
	RecentEarners []UserInfo `json:"recent_earners"`
	DailyEarns    []DailyEarnStat `json:"daily_earns"`
	
	// Progress distribution
	ProgressDistribution []ProgressDistributionStat `json:"progress_distribution"`
}

type DailyEarnStat struct {
	Date  time.Time `json:"date"`
	Count int       `json:"count"`
}

type ProgressDistributionStat struct {
	ProgressRange string `json:"progress_range"` // e.g., "0-20%", "21-40%", etc.
	Count         int    `json:"count"`
}

type UserBadgeStatsResponse struct {
	UserID      uuid.UUID `json:"user_id"`
	TotalBadges int       `json:"total_badges"`
	TotalPoints int       `json:"total_points"`
	
	// Category breakdown
	StudyBadges       int `json:"study_badges"`
	QuizBadges        int `json:"quiz_badges"`
	ProgressBadges    int `json:"progress_badges"`
	SocialBadges      int `json:"social_badges"`
	AchievementBadges int `json:"achievement_badges"`
	
	// Rarity breakdown
	CommonBadges    int `json:"common_badges"`
	RareBadges      int `json:"rare_badges"`
	EpicBadges      int `json:"epic_badges"`
	LegendaryBadges int `json:"legendary_badges"`
	
	// Recent activity
	RecentBadges    []UserBadgeResponse `json:"recent_badges"`
	BadgesInProgress []UserBadgeResponse `json:"badges_in_progress"`
	
	// Rankings
	GlobalRank     *int `json:"global_rank"`
	CategoryRanks  map[string]int `json:"category_ranks"`
}

type GlobalBadgeStatsResponse struct {
	TotalBadges      int     `json:"total_badges"`
	TotalUsers       int     `json:"total_users"`
	TotalEarned      int     `json:"total_earned"`
	AveragePerUser   float64 `json:"average_per_user"`
	
	// Most popular badges
	PopularBadges []BadgePopularityStat `json:"popular_badges"`
	
	// Category statistics
	CategoryStats map[string]CategoryStat `json:"category_stats"`
	
	// Recent activity
	RecentActivity []RecentBadgeActivity `json:"recent_activity"`
}

type BadgePopularityStat struct {
	Badge      BadgeResponse `json:"badge"`
	EarnedCount int          `json:"earned_count"`
	EarnedRate  float64      `json:"earned_rate"`
}

type CategoryStat struct {
	Category    string  `json:"category"`
	TotalBadges int     `json:"total_badges"`
	TotalEarned int     `json:"total_earned"`
	EarnedRate  float64 `json:"earned_rate"`
}

type RecentBadgeActivity struct {
	User      UserInfo      `json:"user"`
	Badge     BadgeResponse `json:"badge"`
	EarnedAt  time.Time     `json:"earned_at"`
}

// Badge Sharing and Social DTOs
type ShareBadgeRequest struct {
	BadgeID   uuid.UUID   `json:"badge_id" validate:"required"`
	FriendIDs []uuid.UUID `json:"friend_ids" validate:"required,min=1"`
	Message   *string     `json:"message" validate:"omitempty,max=500"`
}

type BadgeShowcaseResponse struct {
	UserID        uuid.UUID         `json:"user_id"`
	ShowcaseBadges []UserBadgeResponse `json:"showcase_badges"`
	TotalBadges   int               `json:"total_badges"`
	TotalPoints   int               `json:"total_points"`
	UpdatedAt     time.Time         `json:"updated_at"`
}

type UpdateBadgeShowcaseRequest struct {
	BadgeIDs []uuid.UUID `json:"badge_ids" validate:"required,max=10"`
}
