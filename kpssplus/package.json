{"name": "kpssplus", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "axios": "^1.11.0", "nativewind": "^4.1.23", "react": "18.2.0", "react-native": "0.74.3", "react-native-gesture-handler": "^2.20.2", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-vector-icons": "^10.3.0", "react-query": "^3.39.3", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@types/jest": "^30.0.0", "@types/react": "^18.3.24", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "^5.0.4"}, "engines": {"node": ">=18"}}