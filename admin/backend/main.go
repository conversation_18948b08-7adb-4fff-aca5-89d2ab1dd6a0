package main

import (
	"log"

	"github.com/nocytech/kpss-plus-admin/pkg/config"
	"github.com/nocytech/kpss-plus-admin/pkg/database"
	"github.com/nocytech/kpss-plus-admin/pkg/server"
)

func main() {
    cfg := config.InitConfig()
    database.InitDB(cfg.Database)
    // Seed default admin user if missing
    database.SeedAdminFromConfig(cfg.App)
    log.Printf("admin-backend starting on %s...", cfg.App.Port)
    server.LaunchHttpServer(cfg.App, cfg.Allows)
}
