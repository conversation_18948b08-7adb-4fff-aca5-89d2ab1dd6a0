package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/domains/badge"
	"github.com/kpss-plus-backend/pkg/domains/social"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/middleware"
	"github.com/kpss-plus-backend/pkg/state"
)

func SocialRoutes(r *gin.RouterGroup, socialService social.Service, badgeService badge.Service) {
	socialGroup := r.Group("/social")

	// Public endpoints (no authentication required)
	socialGroup.GET("/leaderboard", middleware.OptionalAuthorized(), GetLeaderboardHandler(badgeService))

	// Protected endpoints (require authentication)
	protectedGroup := socialGroup.Group("")
	protectedGroup.Use(middleware.Authorized())

	// Friendship management
	protectedGroup.POST("/friends/request", SendFriendRequest(socialService))
	protectedGroup.PUT("/friends/request/respond", RespondToFriendRequest(socialService))
	protectedGroup.GET("/friends/requests/:type", GetFriendRequests(socialService))
	protectedGroup.GET("/friends", GetFriends(socialService))
	protectedGroup.DELETE("/friends/:friendId", RemoveFriend(socialService))

	// Follow system
	protectedGroup.POST("/follow", FollowUser(socialService))
	protectedGroup.DELETE("/follow/:userId", UnfollowUser(socialService))
	protectedGroup.GET("/followers", GetFollowers(socialService))
	protectedGroup.GET("/following", GetFollowing(socialService))

	// User search and discovery
	protectedGroup.GET("/search/users", SearchUsersHandler(socialService))
	protectedGroup.GET("/suggestions/friends", GetFriendSuggestionsHandler(socialService))
	protectedGroup.GET("/mutual-friends/:userId", GetMutualFriendsHandler(socialService))

	// User blocking
	protectedGroup.POST("/block", BlockUserHandler(socialService))
	protectedGroup.DELETE("/block/:userId", UnblockUserHandler(socialService))
	protectedGroup.GET("/blocked", GetBlockedUsersHandler(socialService))

	// Friend activity and profiles
	protectedGroup.GET("/activity", GetFriendActivityHandler(socialService))
	protectedGroup.GET("/profile/:userId", GetUserProfileHandler(socialService))

	// Social statistics
	protectedGroup.GET("/stats", GetSocialStatsHandler(socialService))
	protectedGroup.GET("/friendship/history", GetFriendshipHistoryHandler(socialService))

	// Privacy settings
	protectedGroup.PUT("/privacy", UpdatePrivacySettingsHandler(socialService))
	protectedGroup.GET("/privacy", GetPrivacySettingsHandler(socialService))
}

// Public handlers
func GetLeaderboardHandler(s badge.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GetLeaderboardRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Check if user is guest and limit results
		userID := middleware.GetUserIDOrGuest(c)
		if userID == "guest" {
			// Limit to 10 items for guest users
			if req.Limit == 0 || req.Limit > 10 {
				req.Limit = 10
			}
		}

		resp, err := s.GetLeaderboard(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Friendship management handlers
func SendFriendRequest(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.SendFriendRequestRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.SendFriendRequest(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func RespondToFriendRequest(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.RespondToFriendRequestRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.RespondToFriendRequest(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriendRequests(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		requestType := c.Param("type")
		if requestType == "" {
			requestType = "all"
		}

		resp, err := s.GetFriendRequests(userID, requestType)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriends(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetFriendsRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriends(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func RemoveFriend(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		friendID := c.Param("friendId")
		if friendID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Friend ID is required",
				"status": 400,
			})
			return
		}

		err := s.RemoveFriend(userID, friendID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Friend removed successfully",
			"status":  200,
		})
	}
}

// Follow system handlers
func FollowUser(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.FollowUserRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.FollowUser(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func UnfollowUser(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		targetUserID := c.Param("userId")
		if targetUserID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "User ID is required",
				"status": 400,
			})
			return
		}

		err := s.UnfollowUser(userID, targetUserID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "User unfollowed successfully",
			"status":  200,
		})
	}
}

// User search and discovery handlers
func SearchUsersHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		var req dtos.SearchUsersRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.SearchUsers(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriendSuggestionsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		var req dtos.GetFriendSuggestionsRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriendSuggestions(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetMutualFriendsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		targetUserID := c.Param("userId")

		resp, err := s.GetMutualFriends(userID, targetUserID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// User blocking handlers
func BlockUserHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		var req dtos.BlockUserRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid request body",
				"status": 400,
			})
			return
		}

		err := s.BlockUser(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "User blocked successfully",
			"status":  200,
		})
	}
}

func UnblockUserHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		targetUserID := c.Param("userId")

		payload := &dtos.UnblockUserRequest{
			UserID: uuid.MustParse(targetUserID),
		}

		err := s.UnblockUser(userID, payload)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "User unblocked successfully",
			"status":  200,
		})
	}
}

func GetBlockedUsersHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		resp, err := s.GetBlockedUsers(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Social activity handlers
func GetFriendActivityHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		var req dtos.GetFriendActivityRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriendActivity(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetUserProfileHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		targetUserID := c.Param("userId")

		resp, err := s.GetUserProfile(userID, targetUserID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Social statistics handlers
func GetSocialStatsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		resp, err := s.GetSocialStats(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriendshipHistoryHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		var req dtos.GetFriendshipHistoryRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriendshipHistory(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFollowers(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetFollowersRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetFollowers(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFollowing(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetFollowingRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetFollowing(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Privacy settings handlers
func UpdatePrivacySettingsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		var req dtos.UpdatePrivacySettingsRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid request body",
				"status": 400,
			})
			return
		}

		resp, err := s.UpdatePrivacySettings(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetPrivacySettingsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)

		resp, err := s.GetPrivacySettings(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
