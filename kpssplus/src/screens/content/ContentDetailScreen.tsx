import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {ContentStackParamList} from '../../navigation/types';
import {colors, typography, spacing} from '../../theme';

type Props = StackScreenProps<ContentStackParamList, 'ContentDetail'>;

export const ContentDetailScreen: React.FC<Props> = ({route}) => {
  const {contentId} = route.params;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>İçerik Detayı</Text>
        <Text style={styles.subtitle}>İçerik ID: {contentId}</Text>
        <Text style={styles.description}>
          Bu ekran içerik detaylarını gösterecek. İçerik bilgileri, a<PERSON><PERSON><PERSON><PERSON>,
          s<PERSON><PERSON>, zorluk seviyesi ve oynatma butonu burada yer alacak.
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing[4],
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    ...typography.h3,
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  subtitle: {
    ...typography.h6,
    color: colors.text.secondary,
    marginBottom: spacing[4],
    textAlign: 'center',
  },
  description: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
