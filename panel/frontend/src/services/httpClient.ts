import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, ApiError, HttpClient, RequestConfig } from '../types/api';

class HttpClientImpl implements HttpClient {
  private instance: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = 'http://localhost:8000/api/v1';
    
    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp
        (config as any).metadata = { startTime: new Date() };

        console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params,
        });

        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const duration = new Date().getTime() - (response.config as any).metadata?.startTime?.getTime();
        
        console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, {
          status: response.status,
          data: response.data,
        });

        return response;
      },
      (error) => {
        const duration = (error.config as any)?.metadata?.startTime
          ? new Date().getTime() - (error.config as any).metadata.startTime.getTime()
          : 0;

        console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });

        // Handle specific error cases
        if (error.response?.status === 401) {
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }

        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      return {
        error: error.response.data?.error || 'Server Error',
        status: error.response.status,
        message: error.response.data?.message || error.message,
        details: error.response.data,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        error: 'Network Error',
        status: 0,
        message: 'No response from server. Please check your internet connection.',
        details: error.request,
      };
    } else {
      // Something else happened
      return {
        error: 'Request Error',
        status: 0,
        message: error.message || 'An unexpected error occurred',
        details: error,
      };
    }
  }

  private transformResponse<T>(response: AxiosResponse): ApiResponse<T> {
    return {
      data: response.data.data || response.data,
      status: response.status,
      message: response.data.message,
      success: response.status >= 200 && response.status < 300,
    };
  }

  async get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    const response = await this.instance.get(url, {
      params: config?.params,
      headers: config?.headers,
      timeout: config?.timeout,
    } as AxiosRequestConfig);
    
    return this.transformResponse<T>(response);
  }

  async post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    const response = await this.instance.post(url, data, {
      headers: config?.headers,
      timeout: config?.timeout,
    } as AxiosRequestConfig);
    
    return this.transformResponse<T>(response);
  }

  async put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    const response = await this.instance.put(url, data, {
      headers: config?.headers,
      timeout: config?.timeout,
    } as AxiosRequestConfig);
    
    return this.transformResponse<T>(response);
  }

  async delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    const response = await this.instance.delete(url, {
      headers: config?.headers,
      timeout: config?.timeout,
    } as AxiosRequestConfig);
    
    return this.transformResponse<T>(response);
  }

  async patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    const response = await this.instance.patch(url, data, {
      headers: config?.headers,
      timeout: config?.timeout,
    } as AxiosRequestConfig);
    
    return this.transformResponse<T>(response);
  }

  async upload<T = any>(url: string, file: File, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      timeout: config?.timeout || 60000, // Longer timeout for uploads
    } as AxiosRequestConfig);
    
    return this.transformResponse<T>(response);
  }

  // Utility methods
  setAuthToken(token: string): void {
    this.instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('auth_token', token);
  }

  clearAuthToken(): void {
    delete this.instance.defaults.headers.common['Authorization'];
    localStorage.removeItem('auth_token');
  }

  getBaseURL(): string {
    return this.baseURL;
  }

  updateBaseURL(newBaseURL: string): void {
    this.baseURL = newBaseURL;
    this.instance.defaults.baseURL = newBaseURL;
  }
}

// Create and export singleton instance
export const httpClient = new HttpClientImpl();
export default httpClient;
