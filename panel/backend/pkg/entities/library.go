package entities

import (
    "github.com/google/uuid"
)

type LibraryFolder struct {
    Base
    UserID *uuid.UUID `json:"user_id" gorm:"index"`
    Name   string     `json:"name" gorm:"not null"`
}

func (LibraryFolder) TableName() string { return "library_folders" }

// LibraryItemType: content | topic | quiz
type LibraryItem struct {
    Base
    UserID   *uuid.UUID `json:"user_id" gorm:"index"`
    ItemType string     `json:"item_type" gorm:"type:varchar(16);index"`
    ItemID   *uuid.UUID `json:"item_id" gorm:"type:uuid;index"`
    FolderID *uuid.UUID `json:"folder_id" gorm:"type:uuid;index"`
}

func (LibraryItem) TableName() string { return "library_items" }

