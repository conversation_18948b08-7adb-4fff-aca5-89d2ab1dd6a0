import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Users, 
  UserPlus, 
  Search, 
  MessageCircle,
  Trophy,
  Star,
  Clock,
  CheckCircle,
  XCircle,
  UserCheck,
  UserX,
  Activity as ActivityIcon,
  Heart,
  Award
} from 'lucide-react';
import { 
  User, 
  Friend, 
  FriendRequest, 
  SocialStats, 
  Activity,
  socialService 
} from '../../services/socialService';

const SocialPage: React.FC = () => {
  const { t } = useTranslation();
  const [friends, setFriends] = useState<Friend[]>([]);
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([]);
  const [suggestions, setSuggestions] = useState<User[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [stats, setStats] = useState<SocialStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'friends' | 'requests' | 'suggestions' | 'activity'>('friends');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);

  useEffect(() => {
    loadSocialData();
  }, []);

  const loadSocialData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [friendsRes, requestsRes, suggestionsRes, activitiesRes, statsRes] = await Promise.all([
        socialService.getFriends({ limit: 20 }),
        socialService.getFriendRequests('received'),
        socialService.getFriendSuggestions({ limit: 10 }),
        socialService.getActivity({ limit: 20 }),
        socialService.getSocialStats()
      ]);

      if (friendsRes.success && friendsRes.data) setFriends(friendsRes.data);
      if (requestsRes.success && requestsRes.data) setFriendRequests(requestsRes.data);
      if (suggestionsRes.success && suggestionsRes.data) setSuggestions(suggestionsRes.data);
      if (activitiesRes.success && activitiesRes.data) setActivities(activitiesRes.data);
      if (statsRes.success && statsRes.data) setStats(statsRes.data);

    } catch (err) {
      setError('Failed to load social data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    try {
      const response = await socialService.searchUsers(searchQuery, { limit: 10 });
      if (response.success && response.data) {
        setSearchResults(response.data.users);
      }
    } catch (err) {
      console.error('Search failed:', err);
    }
  };

  const handleSendFriendRequest = async (userId: string) => {
    try {
      await socialService.sendFriendRequest({ user_id: userId });
      // Refresh suggestions
      const response = await socialService.getFriendSuggestions({ limit: 10 });
      if (response.success && response.data) {
        setSuggestions(response.data);
      }
    } catch (err) {
      console.error('Failed to send friend request:', err);
    }
  };

  const handleRespondToRequest = async (requestId: string, action: 'accept' | 'reject') => {
    try {
      await socialService.respondToFriendRequest({ request_id: requestId, action });
      // Refresh requests and friends
      const [requestsRes, friendsRes] = await Promise.all([
        socialService.getFriendRequests('received'),
        socialService.getFriends({ limit: 20 })
      ]);
      
      if (requestsRes.success && requestsRes.data) setFriendRequests(requestsRes.data);
      if (friendsRes.success && friendsRes.data) setFriends(friendsRes.data);
    } catch (err) {
      console.error('Failed to respond to friend request:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'quiz_completed': return Trophy;
      case 'achievement_unlocked': return Award;
      case 'level_up': return Star;
      case 'friend_added': return UserPlus;
      case 'content_completed': return CheckCircle;
      default: return ActivityIcon;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'quiz_completed': return 'text-blue-400';
      case 'achievement_unlocked': return 'text-yellow-400';
      case 'level_up': return 'text-purple-400';
      case 'friend_added': return 'text-green-400';
      case 'content_completed': return 'text-primary-400';
      default: return 'text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-2">Error loading social data</div>
        <p className="text-dark-400">{error}</p>
        <button
          onClick={loadSocialData}
          className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Social</h1>
          <p className="text-dark-300 mt-1">Connect with friends and track activities</p>
        </div>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex gap-2 max-w-md w-full lg:w-auto">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400" size={20} />
            <input
              type="text"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:border-primary-500"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <Search size={20} />
          </button>
        </form>
      </div>

      {/* Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <div className="flex items-center gap-3 mb-3">
              <Users className="text-primary-400" size={24} />
              <h3 className="text-white font-semibold">Friends</h3>
            </div>
            <div className="text-2xl font-bold text-white">{stats.friends_count}</div>
          </div>

          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <div className="flex items-center gap-3 mb-3">
              <UserPlus className="text-green-400" size={24} />
              <h3 className="text-white font-semibold">Followers</h3>
            </div>
            <div className="text-2xl font-bold text-white">{stats.followers_count}</div>
          </div>

          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <div className="flex items-center gap-3 mb-3">
              <Heart className="text-red-400" size={24} />
              <h3 className="text-white font-semibold">Following</h3>
            </div>
            <div className="text-2xl font-bold text-white">{stats.following_count}</div>
          </div>
        </div>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <h3 className="text-lg font-semibold text-white mb-4">Search Results</h3>
          <div className="space-y-3">
            {searchResults.map((user) => (
              <UserCard 
                key={user.id} 
                user={user} 
                onSendRequest={() => handleSendFriendRequest(user.id)}
                showActions={true}
              />
            ))}
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
        {[
          { key: 'friends', label: 'Friends', icon: Users, count: friends.length },
          { key: 'requests', label: 'Requests', icon: UserPlus, count: friendRequests.length },
          { key: 'suggestions', label: 'Suggestions', icon: UserCheck, count: suggestions.length },
          { key: 'activity', label: 'Activity', icon: ActivityIcon, count: activities.length }
        ].map(({ key, label, icon: Icon, count }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as typeof activeTab)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === key
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:text-white hover:bg-dark-700'
            }`}
          >
            <Icon size={16} />
            {label}
            {count > 0 && (
              <span className="bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
                {count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        {activeTab === 'friends' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Your Friends</h3>
            {friends.length === 0 ? (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-dark-400 mb-4" />
                <h4 className="text-lg font-medium text-white mb-2">No friends yet</h4>
                <p className="text-dark-400">Start by sending friend requests or accepting suggestions</p>
              </div>
            ) : (
              <div className="space-y-3">
                {friends.map((friend) => (
                  <UserCard 
                    key={friend.id} 
                    user={friend.user} 
                    showActions={false}
                    additionalInfo={`Friends since ${formatDate(friend.accepted_at || friend.created_at)}`}
                  />
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'requests' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Friend Requests</h3>
            {friendRequests.length === 0 ? (
              <div className="text-center py-8">
                <UserPlus className="mx-auto h-12 w-12 text-dark-400 mb-4" />
                <h4 className="text-lg font-medium text-white mb-2">No pending requests</h4>
                <p className="text-dark-400">You'll see friend requests here when you receive them</p>
              </div>
            ) : (
              <div className="space-y-3">
                {friendRequests.map((request) => (
                  <div key={request.id} className="bg-dark-700 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {request.from_user.avatar && (
                          <img
                            src={request.from_user.avatar}
                            alt={request.from_user.username}
                            className="w-12 h-12 rounded-full"
                          />
                        )}
                        <div>
                          <h4 className="text-white font-medium">{request.from_user.full_name}</h4>
                          <p className="text-dark-400 text-sm">@{request.from_user.username}</p>
                          {request.message && (
                            <p className="text-dark-300 text-sm mt-1">"{request.message}"</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleRespondToRequest(request.id, 'accept')}
                          className="flex items-center gap-1 px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm"
                        >
                          <CheckCircle size={16} />
                          Accept
                        </button>
                        <button
                          onClick={() => handleRespondToRequest(request.id, 'reject')}
                          className="flex items-center gap-1 px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm"
                        >
                          <XCircle size={16} />
                          Decline
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'suggestions' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Friend Suggestions</h3>
            {suggestions.length === 0 ? (
              <div className="text-center py-8">
                <UserCheck className="mx-auto h-12 w-12 text-dark-400 mb-4" />
                <h4 className="text-lg font-medium text-white mb-2">No suggestions available</h4>
                <p className="text-dark-400">Check back later for new friend suggestions</p>
              </div>
            ) : (
              <div className="space-y-3">
                {suggestions.map((user) => (
                  <UserCard 
                    key={user.id} 
                    user={user} 
                    onSendRequest={() => handleSendFriendRequest(user.id)}
                    showActions={true}
                  />
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
            {activities.length === 0 ? (
              <div className="text-center py-8">
                <ActivityIcon className="mx-auto h-12 w-12 text-dark-400 mb-4" />
                <h4 className="text-lg font-medium text-white mb-2">No recent activity</h4>
                <p className="text-dark-400">Activity from you and your friends will appear here</p>
              </div>
            ) : (
              <div className="space-y-3">
                {activities.map((activity) => {
                  const Icon = getActivityIcon(activity.type);
                  const iconColor = getActivityColor(activity.type);
                  
                  return (
                    <div key={activity.id} className="bg-dark-700 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="flex items-center gap-2">
                          {activity.user.avatar && (
                            <img
                              src={activity.user.avatar}
                              alt={activity.user.username}
                              className="w-10 h-10 rounded-full"
                            />
                          )}
                          <Icon className={iconColor} size={20} />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-white font-medium">{activity.user.full_name}</span>
                            <span className="text-dark-400 text-sm">•</span>
                            <span className="text-dark-400 text-sm">{formatDate(activity.created_at)}</span>
                          </div>
                          <h4 className="text-white font-medium">{activity.title}</h4>
                          <p className="text-dark-300 text-sm">{activity.description}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// User Card Component
const UserCard: React.FC<{ 
  user: User; 
  onSendRequest?: () => void; 
  showActions?: boolean;
  additionalInfo?: string;
}> = ({ user, onSendRequest, showActions = false, additionalInfo }) => {
  return (
    <div className="bg-dark-700 rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {user.avatar && (
            <img
              src={user.avatar}
              alt={user.username}
              className="w-12 h-12 rounded-full"
            />
          )}
          <div>
            <h4 className="text-white font-medium">{user.full_name}</h4>
            <p className="text-dark-400 text-sm">@{user.username}</p>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-primary-400">Level {user.level}</span>
              <span className="text-dark-400">•</span>
              <span className="text-dark-400">{user.total_points} points</span>
            </div>
            {additionalInfo && (
              <p className="text-dark-400 text-xs mt-1">{additionalInfo}</p>
            )}
          </div>
        </div>
        
        {showActions && onSendRequest && (
          <button
            onClick={onSendRequest}
            className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors text-sm"
          >
            <UserPlus size={16} />
            Add Friend
          </button>
        )}
      </div>
    </div>
  );
};

export default SocialPage;
