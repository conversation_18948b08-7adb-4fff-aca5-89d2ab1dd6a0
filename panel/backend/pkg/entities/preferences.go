package entities

import (
    "github.com/google/uuid"
)

// UserPreferences stores per-user recommendation preferences
type UserPreferences struct {
    Base
    UserID          *uuid.UUID `json:"user_id" gorm:"uniqueIndex"`
    // Comma-separated interests/subjects for simplicity
    Interests       *string    `json:"interests"`
    // JSON-encoded weights (subject->weight); keep as text for MVP
    SubjectWeights  *string    `json:"subject_weights" gorm:"type:text"`
    DifficultyPref  *string    `json:"difficulty_pref"` // Easy/Medium/Hard/Any
}

func (UserPreferences) TableName() string { return "user_preferences" }

