package utils

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/kpss-plus-backend/pkg/config"
)

type JwtCustomClaim struct {
	UserID         string
	AdminID        string
	DeviceID       string
	PushNotifToken string
	PurchaseID     string
	Timezone       string
	PhoneLanguage  string
	jwt.RegisteredClaims
}

type JwtWrapper struct {
	SecretKey string
	Issuer    string
	Expire    int
}

type JwtClaim struct {
	ID       string
	UserName string
	UserId   string
	jwt.RegisteredClaims
}

func (j *JwtWrapper) ParseToken(tokenString string) (claims *JwtCustomClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtCustomClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtCustomClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ValidateToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtCustomClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtCustomClaim)
	// if !cache.UserSessionCheck(claims.UserID, tokenString) {
	// 	return false
	// }

	if claims.ExpiresAt.UTC().Unix() < time.Now().UTC().Unix() {
		return false
	}

	return token.Valid
}

func (j *JwtWrapper) GenerateJWT(userName, userId string, isAdmin bool) (string, error) {
	claims := &JwtCustomClaim{}
	if isAdmin {
		claims = &JwtCustomClaim{
			AdminID: userId,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().UTC().Add(time.Hour * time.Duration(config.ReadValue().App.JwtExpire))),
				IssuedAt:  jwt.NewNumericDate(time.Now().UTC()),
			},
		}
	} else {
		claims = &JwtCustomClaim{
			UserID: userId,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().UTC().Add(time.Hour * time.Duration(config.ReadValue().App.JwtExpire))),
				IssuedAt:  jwt.NewNumericDate(time.Now().UTC()),
			},
		}
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	signedToken, err := token.SignedString([]byte(j.SecretKey))
	if err != nil {
		return "", err
	}
	return signedToken, nil
}
