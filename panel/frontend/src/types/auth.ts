export interface NotificationSettings {
  push_notifications: boolean;
  email_notifications: boolean;
  quiz_reminders: boolean;
  friend_requests: boolean;
  achievement_notifications: boolean;
  study_reminders: boolean;
}

export interface PrivacySettings {
  profile_visibility: 'public' | 'friends' | 'private';
  show_study_progress: boolean;
  show_achievements: boolean;
  allow_friend_requests: boolean;
  show_online_status: boolean;
}

export interface StudyPreferences {
  daily_study_goal: number; // minutes
  preferred_study_time: string; // HH:MM format
  reminder_frequency: 'daily' | 'weekly' | 'never';
  difficulty_preference: 'easy' | 'medium' | 'hard' | 'mixed';
  subjects_of_interest: string[];
}

export interface DeviceInfo {
  device_type: 'web' | 'mobile' | 'tablet';
  device_name: string;
  os: string;
  browser?: string;
  app_version: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  bio?: string;
  location?: string;
  birth_date?: string;
  gender?: 'male' | 'female' | 'other';
  is_verified: boolean;
  is_active: boolean;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
  notification_settings?: NotificationSettings;
  privacy_settings?: PrivacySettings;
  study_preferences?: StudyPreferences;
}

export interface LoginRequest {
  username?: string;
  email?: string;
  phone?: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  name: string;
  phone?: string;
  terms_accepted: boolean;
}

export interface AuthResponse {
  token: string;
  expires: string;
  user: User;
  is_succeeded: boolean;
}

export interface GoogleLoginRequest {
  token: string;
  device_info?: DeviceInfo;
}

export interface AppleLoginRequest {
  token: string;
  device_info?: DeviceInfo;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  new_password: string;
}

export interface VerifyOTPRequest {
  phone: string;
  otp: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface UpdateProfileRequest {
  name?: string;
  bio?: string;
  location?: string;
  birth_date?: string;
  gender?: 'male' | 'female' | 'other';
  avatar?: File;
}

export interface UpdateNotificationSettingsRequest {
  notification_settings: NotificationSettings;
}

export interface UpdatePrivacySettingsRequest {
  privacy_settings: PrivacySettings;
}

export interface UpdateStudyPreferencesRequest {
  study_preferences: StudyPreferences;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  updateProfile: (data: UpdateProfileRequest) => Promise<void>;
  changePassword: (data: ChangePasswordRequest) => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
}