import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';

export interface Notification {
  id: string;
  type: 'quiz_completed' | 'achievement_unlocked' | 'friend_request' | 'system' | 'reminder';
  title: string;
  message: string;
  is_read: boolean;
  created_at: string;
  metadata?: any;
  action_url?: string;
}

export interface NotificationPreferences {
  email_notifications: boolean;
  push_notifications: boolean;
  quiz_reminders: boolean;
  friend_requests: boolean;
  achievements: boolean;
  study_streak: boolean;
  weekly_summary: boolean;
  system_updates: boolean;
}

export interface NotificationListResponse {
  notifications: Notification[];
  total: number;
  unread_count: number;
  page: number;
  limit: number;
  has_more: boolean;
}

export const notificationService = {
  // Get notifications
  async getNotifications(params?: { 
    page?: number; 
    limit?: number; 
    is_read?: boolean;
    type?: string;
  }): Promise<ApiResponse<NotificationListResponse>> {
    return http.get(endpoints.notifications.list, { params });
  },

  // Get unread count
  async getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    return http.get(endpoints.notifications.unread);
  },

  // Mark notification as read
  async markAsRead(id: string): Promise<ApiResponse<void>> {
    return http.put(endpoints.notifications.markAsRead(id));
  },

  // Mark all notifications as read
  async markAllAsRead(): Promise<ApiResponse<void>> {
    return http.put(endpoints.notifications.markAllAsRead);
  },

  // Delete notification
  async deleteNotification(id: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.notifications.delete(id));
  },

  // Get notification settings
  async getNotificationSettings(): Promise<ApiResponse<NotificationPreferences>> {
    return http.get(endpoints.notifications.settings);
  },

  // Update notification settings
  async updateNotificationSettings(settings: Partial<NotificationPreferences>): Promise<ApiResponse<NotificationPreferences>> {
    return http.put(endpoints.notifications.updateSettings, settings);
  }
};

export default notificationService;
