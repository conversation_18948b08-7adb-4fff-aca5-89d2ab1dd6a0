import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';

interface ProgressBarProps {
  progress: number;
  color?: string;
  height?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  color = colors.primary,
  height = 8,
}) => (
  <View style={[styles.progressBarContainer, {height}]}>
    <View
      style={[
        styles.progressBarFill,
        {
          width: `${Math.min(100, Math.max(0, progress))}%`,
          backgroundColor: color,
          height,
        },
      ]}
    />
  </View>
);

interface SubjectProgressProps {
  subject: string;
  progress: number;
  completedLessons: number;
  totalLessons: number;
  color: string;
}

const SubjectProgress: React.FC<SubjectProgressProps> = ({
  subject,
  progress,
  completedLessons,
  totalLessons,
  color,
}) => (
  <Card style={styles.subjectCard}>
    <View style={styles.subjectHeader}>
      <View style={styles.subjectInfo}>
        <Text style={styles.subjectName}>{subject}</Text>
        <Text style={styles.subjectStats}>
          {completedLessons}/{totalLessons} ders tamamlandı
        </Text>
      </View>
      <Text style={styles.subjectPercentage}>{Math.round(progress)}%</Text>
    </View>
    <ProgressBar progress={progress} color={color} />
  </Card>
);

export const ProgressScreen: React.FC = () => {
  const {isAuthenticated} = useAuth();

  // Mock data - in real app, this would come from API
  const subjects = [
    {
      name: 'Türkçe',
      progress: 75,
      completedLessons: 15,
      totalLessons: 20,
      color: colors.primary,
    },
    {
      name: 'Matematik',
      progress: 60,
      completedLessons: 12,
      totalLessons: 20,
      color: colors.success,
    },
    {
      name: 'Tarih',
      progress: 45,
      completedLessons: 9,
      totalLessons: 20,
      color: colors.warning,
    },
    {
      name: 'Coğrafya',
      progress: 30,
      completedLessons: 6,
      totalLessons: 20,
      color: colors.error,
    },
    {
      name: 'Vatandaşlık',
      progress: 85,
      completedLessons: 17,
      totalLessons: 20,
      color: colors.info,
    },
  ];

  const overallProgress =
    subjects.reduce((acc, subject) => acc + subject.progress, 0) /
    subjects.length;
  const totalCompleted = subjects.reduce(
    (acc, subject) => acc + subject.completedLessons,
    0,
  );
  const totalLessons = subjects.reduce(
    (acc, subject) => acc + subject.totalLessons,
    0,
  );

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <View style={styles.emptyState}>
          <Icon name="bar-chart-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.emptyTitle}>Giriş Yapın</Text>
          <Text style={styles.emptyDescription}>
            İlerleme raporunuzu görmek için giriş yapmanız gerekiyor
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {/* Overall Progress */}
        <Card style={styles.overallCard} variant="elevated">
          <View style={styles.overallHeader}>
            <Icon name="trending-up" size={32} color={colors.primary} />
            <View style={styles.overallInfo}>
              <Text style={styles.overallTitle}>Genel İlerleme</Text>
              <Text style={styles.overallSubtitle}>
                {totalCompleted}/{totalLessons} ders tamamlandı
              </Text>
            </View>
            <Text style={styles.overallPercentage}>
              {Math.round(overallProgress)}%
            </Text>
          </View>
          <ProgressBar progress={overallProgress} height={12} />
        </Card>

        {/* Subject Progress */}
        <View style={styles.subjectsSection}>
          <Text style={styles.sectionTitle}>Ders Bazında İlerleme</Text>
          {subjects.map((subject, index) => (
            <SubjectProgress
              key={index}
              subject={subject.name}
              progress={subject.progress}
              completedLessons={subject.completedLessons}
              totalLessons={subject.totalLessons}
              color={subject.color}
            />
          ))}
        </View>

        {/* Weekly Goal */}
        <Card style={styles.goalCard}>
          <View style={styles.goalHeader}>
            <Icon name="flag-outline" size={24} color={colors.warning} />
            <Text style={styles.goalTitle}>Haftalık Hedef</Text>
          </View>
          <View style={styles.goalContent}>
            <Text style={styles.goalDescription}>
              Bu hafta 5 ders tamamlama hedefin var
            </Text>
            <View style={styles.goalProgress}>
              <Text style={styles.goalStats}>3/5 ders</Text>
              <ProgressBar progress={60} color={colors.warning} />
            </View>
          </View>
        </Card>

        {/* Recent Activity */}
        <Card style={styles.activityCard}>
          <Text style={styles.sectionTitle}>Son Aktiviteler</Text>
          {[
            {
              icon: 'checkmark-circle',
              title: 'Türkçe - Cümle Bilgisi',
              subtitle: '2 saat önce tamamlandı',
              color: colors.success,
            },
            {
              icon: 'play-circle',
              title: 'Matematik - Geometri',
              subtitle: 'Devam ediyor',
              color: colors.primary,
            },
            {
              icon: 'trophy',
              title: 'Quiz tamamlandı',
              subtitle: 'Tarih - %85 başarı',
              color: colors.warning,
            },
          ].map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <Icon name={activity.icon} size={24} color={activity.color} />
              <View style={styles.activityText}>
                <Text style={styles.activityTitle}>{activity.title}</Text>
                <Text style={styles.activitySubtitle}>{activity.subtitle}</Text>
              </View>
            </View>
          ))}
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
  },
  emptyTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  progressBarContainer: {
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    borderRadius: 4,
  },
  overallCard: {
    marginBottom: spacing[6],
    padding: spacing[5],
  },
  overallHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  overallInfo: {
    flex: 1,
    marginLeft: spacing[3],
  },
  overallTitle: {
    ...typography.h4,
    marginBottom: spacing[1],
  },
  overallSubtitle: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  overallPercentage: {
    ...typography.h3,
    color: colors.primary,
    fontWeight: '700',
  },
  subjectsSection: {
    marginBottom: spacing[6],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  subjectCard: {
    marginBottom: spacing[3],
    padding: spacing[4],
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  subjectInfo: {
    flex: 1,
  },
  subjectName: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  subjectStats: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  subjectPercentage: {
    ...typography.h5,
    color: colors.text.primary,
    fontWeight: '600',
  },
  goalCard: {
    marginBottom: spacing[6],
    padding: spacing[4],
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  goalTitle: {
    ...typography.h5,
    marginLeft: spacing[2],
  },
  goalContent: {
    gap: spacing[3],
  },
  goalDescription: {
    ...typography.body1,
    color: colors.text.secondary,
  },
  goalProgress: {
    gap: spacing[2],
  },
  goalStats: {
    ...typography.body2,
    fontWeight: '600',
  },
  activityCard: {
    marginBottom: spacing[4],
    padding: spacing[4],
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  activityText: {
    flex: 1,
    marginLeft: spacing[3],
  },
  activityTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  activitySubtitle: {
    ...typography.caption,
    color: colors.text.secondary,
  },
});
