package dtos

import (
	"time"
	"github.com/google/uuid"
)

// Notification Management DTOs
type GetNotificationsRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Type     string `json:"type" validate:"omitempty,oneof=friend quiz progress achievement system"`
	IsRead   *bool  `json:"is_read"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=created_at updated_at"`
	SortDesc bool   `json:"sort_desc"`
}

type NotificationResponse struct {
	ID       uuid.UUID              `json:"id"`
	UserID   uuid.UUID              `json:"user_id"`
	Type     string                 `json:"type"`
	Title    string                 `json:"title"`
	Message  string                 `json:"message"`
	Data     map[string]interface{} `json:"data"`
	IsRead   bool                   `json:"is_read"`
	ReadAt   *time.Time             `json:"read_at"`
	
	// Related entities (if applicable)
	RelatedUserID   *uuid.UUID `json:"related_user_id"`
	RelatedUser     *UserInfo  `json:"related_user,omitempty"`
	RelatedContentID *uuid.UUID `json:"related_content_id"`
	RelatedQuizID   *uuid.UUID `json:"related_quiz_id"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type NotificationsResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	Total         int                    `json:"total"`
	Page          int                    `json:"page"`
	Limit         int                    `json:"limit"`
	TotalPages    int                    `json:"total_pages"`
	UnreadCount   int                    `json:"unread_count"`
}

type UnreadCountResponse struct {
	UnreadCount int `json:"unread_count"`
}

// Notification Creation DTOs
type CreateNotificationRequest struct {
	UserID    uuid.UUID              `json:"user_id" validate:"required"`
	Type      string                 `json:"type" validate:"required,oneof=friend quiz progress achievement system"`
	Title     string                 `json:"title" validate:"required,min=1,max=200"`
	Message   string                 `json:"message" validate:"required,min=1,max=1000"`
	Data      map[string]interface{} `json:"data"`
	SendPush  bool                   `json:"send_push"`
	SendEmail bool                   `json:"send_email"`
	
	// Related entities (optional)
	RelatedUserID    *uuid.UUID `json:"related_user_id"`
	RelatedContentID *uuid.UUID `json:"related_content_id"`
	RelatedQuizID    *uuid.UUID `json:"related_quiz_id"`
}

type CreateBulkNotificationsRequest struct {
	UserIDs   []uuid.UUID            `json:"user_ids" validate:"required,min=1"`
	Type      string                 `json:"type" validate:"required,oneof=friend quiz progress achievement system"`
	Title     string                 `json:"title" validate:"required,min=1,max=200"`
	Message   string                 `json:"message" validate:"required,min=1,max=1000"`
	Data      map[string]interface{} `json:"data"`
	SendPush  bool                   `json:"send_push"`
	SendEmail bool                   `json:"send_email"`
}

type BulkNotificationResponse struct {
	SuccessCount int                      `json:"success_count"`
	FailureCount int                      `json:"failure_count"`
	Notifications []NotificationResponse  `json:"notifications"`
	Errors       []string                `json:"errors,omitempty"`
}

// Push Notification DTOs
type RegisterPushTokenRequest struct {
	Token    string `json:"token" validate:"required"`
	Platform string `json:"platform" validate:"required,oneof=ios android web"`
	DeviceID string `json:"device_id" validate:"required"`
}

type UnregisterPushTokenRequest struct {
	Token    string `json:"token" validate:"required"`
	DeviceID string `json:"device_id" validate:"required"`
}

type SendPushNotificationRequest struct {
	Title   string                 `json:"title" validate:"required,min=1,max=200"`
	Message string                 `json:"message" validate:"required,min=1,max=1000"`
	Data    map[string]interface{} `json:"data"`
	Badge   *int                   `json:"badge" validate:"omitempty,min=0"`
	Sound   string                 `json:"sound" validate:"omitempty"`
}

type SendBulkPushNotificationsRequest struct {
	UserIDs []uuid.UUID            `json:"user_ids" validate:"required,min=1"`
	Title   string                 `json:"title" validate:"required,min=1,max=200"`
	Message string                 `json:"message" validate:"required,min=1,max=1000"`
	Data    map[string]interface{} `json:"data"`
	Badge   *int                   `json:"badge" validate:"omitempty,min=0"`
	Sound   string                 `json:"sound" validate:"omitempty"`
}

// Email Notification DTOs
type SendEmailNotificationRequest struct {
	Subject  string                 `json:"subject" validate:"required,min=1,max=200"`
	Body     string                 `json:"body" validate:"required,min=1"`
	Template *string                `json:"template"`
	Data     map[string]interface{} `json:"data"`
}

type SendBulkEmailNotificationsRequest struct {
	UserIDs  []uuid.UUID            `json:"user_ids" validate:"required,min=1"`
	Subject  string                 `json:"subject" validate:"required,min=1,max=200"`
	Body     string                 `json:"body" validate:"required,min=1"`
	Template *string                `json:"template"`
	Data     map[string]interface{} `json:"data"`
}

// Notification Preferences DTOs
type UpdateNotificationPreferencesRequest struct {
	PushEnabled           *bool `json:"push_enabled"`
	EmailEnabled          *bool `json:"email_enabled"`
	FriendRequestsEnabled *bool `json:"friend_requests_enabled"`
	QuizNotificationsEnabled *bool `json:"quiz_notifications_enabled"`
	ProgressNotificationsEnabled *bool `json:"progress_notifications_enabled"`
	AchievementNotificationsEnabled *bool `json:"achievement_notifications_enabled"`
	SystemNotificationsEnabled *bool `json:"system_notifications_enabled"`
	MarketingEmailsEnabled *bool `json:"marketing_emails_enabled"`
	WeeklyDigestEnabled    *bool `json:"weekly_digest_enabled"`
	DailyRemindersEnabled  *bool `json:"daily_reminders_enabled"`
}

type NotificationPreferencesResponse struct {
	UserID                          uuid.UUID `json:"user_id"`
	PushEnabled                     bool      `json:"push_enabled"`
	EmailEnabled                    bool      `json:"email_enabled"`
	FriendRequestsEnabled           bool      `json:"friend_requests_enabled"`
	QuizNotificationsEnabled        bool      `json:"quiz_notifications_enabled"`
	ProgressNotificationsEnabled    bool      `json:"progress_notifications_enabled"`
	AchievementNotificationsEnabled bool      `json:"achievement_notifications_enabled"`
	SystemNotificationsEnabled      bool      `json:"system_notifications_enabled"`
	MarketingEmailsEnabled          bool      `json:"marketing_emails_enabled"`
	WeeklyDigestEnabled             bool      `json:"weekly_digest_enabled"`
	DailyRemindersEnabled           bool      `json:"daily_reminders_enabled"`
	UpdatedAt                       time.Time `json:"updated_at"`
}

// Notification Template DTOs
type CreateNotificationTemplateRequest struct {
	Name        string                 `json:"name" validate:"required,min=1,max=100"`
	Type        string                 `json:"type" validate:"required,oneof=friend quiz progress achievement system"`
	Title       string                 `json:"title" validate:"required,min=1,max=200"`
	Message     string                 `json:"message" validate:"required,min=1,max=1000"`
	EmailSubject *string               `json:"email_subject" validate:"omitempty,min=1,max=200"`
	EmailBody   *string                `json:"email_body" validate:"omitempty,min=1"`
	Variables   []string               `json:"variables"`
	IsActive    bool                   `json:"is_active"`
}

type UpdateNotificationTemplateRequest struct {
	Name        *string  `json:"name" validate:"omitempty,min=1,max=100"`
	Title       *string  `json:"title" validate:"omitempty,min=1,max=200"`
	Message     *string  `json:"message" validate:"omitempty,min=1,max=1000"`
	EmailSubject *string `json:"email_subject" validate:"omitempty,min=1,max=200"`
	EmailBody   *string  `json:"email_body" validate:"omitempty,min=1"`
	Variables   []string `json:"variables"`
	IsActive    *bool    `json:"is_active"`
}

type NotificationTemplateResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Type        string    `json:"type"`
	Title       string    `json:"title"`
	Message     string    `json:"message"`
	EmailSubject *string  `json:"email_subject"`
	EmailBody   *string   `json:"email_body"`
	Variables   []string  `json:"variables"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type GetNotificationTemplatesRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Type     string `json:"type" validate:"omitempty,oneof=friend quiz progress achievement system"`
	IsActive *bool  `json:"is_active"`
}

type NotificationTemplatesResponse struct {
	Templates  []NotificationTemplateResponse `json:"templates"`
	Total      int                            `json:"total"`
	Page       int                            `json:"page"`
	Limit      int                            `json:"limit"`
	TotalPages int                            `json:"total_pages"`
}

// Notification Analytics DTOs
type GetNotificationStatsRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Type      string    `json:"type" validate:"omitempty,oneof=friend quiz progress achievement system"`
}

type NotificationStatsResponse struct {
	UserID    uuid.UUID `json:"user_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	
	// Notification counts
	TotalNotifications     int `json:"total_notifications"`
	ReadNotifications      int `json:"read_notifications"`
	UnreadNotifications    int `json:"unread_notifications"`
	PushNotificationsSent  int `json:"push_notifications_sent"`
	EmailNotificationsSent int `json:"email_notifications_sent"`
	
	// Type breakdown
	FriendNotifications      int `json:"friend_notifications"`
	QuizNotifications        int `json:"quiz_notifications"`
	ProgressNotifications    int `json:"progress_notifications"`
	AchievementNotifications int `json:"achievement_notifications"`
	SystemNotifications      int `json:"system_notifications"`
	
	// Engagement metrics
	ReadRate        float64 `json:"read_rate"`
	ClickThroughRate float64 `json:"click_through_rate"`
	AverageReadTime float64 `json:"average_read_time"` // in seconds
	
	// Daily breakdown
	DailyStats []DailyNotificationStat `json:"daily_stats"`
}

type DailyNotificationStat struct {
	Date                time.Time `json:"date"`
	TotalNotifications  int       `json:"total_notifications"`
	ReadNotifications   int       `json:"read_notifications"`
	UnreadNotifications int       `json:"unread_notifications"`
}

type GetNotificationAnalyticsRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Type      string    `json:"type" validate:"omitempty,oneof=friend quiz progress achievement system"`
}

type NotificationAnalyticsResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	
	// Overall metrics
	TotalNotifications     int     `json:"total_notifications"`
	TotalUsers             int     `json:"total_users"`
	AverageNotificationsPerUser float64 `json:"average_notifications_per_user"`
	OverallReadRate        float64 `json:"overall_read_rate"`
	
	// Platform breakdown
	PlatformStats map[string]PlatformNotificationStat `json:"platform_stats"`
	
	// Type breakdown
	TypeStats map[string]TypeNotificationStat `json:"type_stats"`
	
	// Hourly distribution
	HourlyDistribution []HourlyNotificationStat `json:"hourly_distribution"`
}

type PlatformNotificationStat struct {
	Platform           string  `json:"platform"`
	TotalNotifications int     `json:"total_notifications"`
	ReadRate           float64 `json:"read_rate"`
	ClickThroughRate   float64 `json:"click_through_rate"`
}

type TypeNotificationStat struct {
	Type               string  `json:"type"`
	TotalNotifications int     `json:"total_notifications"`
	ReadRate           float64 `json:"read_rate"`
	AverageReadTime    float64 `json:"average_read_time"`
}

type HourlyNotificationStat struct {
	Hour               int     `json:"hour"`
	TotalNotifications int     `json:"total_notifications"`
	ReadRate           float64 `json:"read_rate"`
}

// Notification Scheduling DTOs
type ScheduleNotificationRequest struct {
	UserID      uuid.UUID              `json:"user_id" validate:"required"`
	Type        string                 `json:"type" validate:"required,oneof=friend quiz progress achievement system"`
	Title       string                 `json:"title" validate:"required,min=1,max=200"`
	Message     string                 `json:"message" validate:"required,min=1,max=1000"`
	Data        map[string]interface{} `json:"data"`
	ScheduledAt time.Time              `json:"scheduled_at" validate:"required"`
	SendPush    bool                   `json:"send_push"`
	SendEmail   bool                   `json:"send_email"`
}

type ScheduledNotificationResponse struct {
	ID          uuid.UUID              `json:"id"`
	UserID      uuid.UUID              `json:"user_id"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Data        map[string]interface{} `json:"data"`
	ScheduledAt time.Time              `json:"scheduled_at"`
	SendPush    bool                   `json:"send_push"`
	SendEmail   bool                   `json:"send_email"`
	Status      string                 `json:"status"` // pending, sent, cancelled, failed
	SentAt      *time.Time             `json:"sent_at"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

type GetScheduledNotificationsRequest struct {
	Page     int       `json:"page" validate:"omitempty,min=1"`
	Limit    int       `json:"limit" validate:"omitempty,min=1,max=100"`
	Status   string    `json:"status" validate:"omitempty,oneof=pending sent cancelled failed"`
	UserID   *uuid.UUID `json:"user_id"`
	Type     string    `json:"type" validate:"omitempty,oneof=friend quiz progress achievement system"`
	SortBy   string    `json:"sort_by" validate:"omitempty,oneof=scheduled_at created_at"`
	SortDesc bool      `json:"sort_desc"`
}

type ScheduledNotificationsResponse struct {
	Notifications []ScheduledNotificationResponse `json:"notifications"`
	Total         int                             `json:"total"`
	Page          int                             `json:"page"`
	Limit         int                             `json:"limit"`
	TotalPages    int                             `json:"total_pages"`
}
