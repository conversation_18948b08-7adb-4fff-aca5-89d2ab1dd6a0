package admin

import "github.com/kpss-plus-backend/pkg/entities"

type Service interface {
	GetUsers(page, limit int) ([]entities.User, error)
	GetUser(id string) (entities.User, error)
	UpdateUser(userId string, user entities.User) error
	DeleteUser(userId string) error
	// Content management (for Content.jsx)
	AdminGetContent(page, limit int) ([]entities.Content, error)
	AdminCreateContent(content entities.Content) error
	AdminUpdateContent(contentId string, content entities.Content) error
	AdminDeleteContent(contentId string) error
	// Study content management (for StudyContent.jsx)
	AdminGetStudyContent(page, limit int) ([]entities.Content, error)
	AdminCreateStudyContent(content entities.Content) error
	AdminUpdateStudyContent(contentId string, content entities.Content) error
	AdminDeleteStudyContent(contentId string) error
	// Quiz management (for Quizzes.jsx)
	AdminGetQuizzes(page, limit int) ([]entities.Quiz, error)
	AdminCreateQuiz(quiz entities.Quiz) error
	AdminUpdateQuiz(quizId string, quiz entities.Quiz) error
	AdminDeleteQuiz(quizId string) error
	GetAnalytics() (int64, error)
	GetUserAnalytics() (int64, int64, error)
	GetContentAnalytics() (int64, int64, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) GetUsers(page, limit int) ([]entities.User, error) {
	return s.repo.GetUsers(page, limit)
}

func (s *service) GetUser(id string) (entities.User, error) {
	return s.repo.GetUser(id)
}

func (s *service) UpdateUser(userId string, user entities.User) error {
	return s.repo.UpdateUser(userId, user)
}

func (s *service) DeleteUser(userId string) error {
	return s.repo.DeleteUser(userId)
}

func (s *service) AdminGetContent(page, limit int) ([]entities.Content, error) {
	return s.repo.AdminGetContent(page, limit)
}

func (s *service) AdminCreateContent(content entities.Content) error {
	return s.repo.AdminCreateContent(content)
}

func (s *service) AdminUpdateContent(contentId string, content entities.Content) error {
	return s.repo.AdminUpdateContent(contentId, content)
}

func (s *service) AdminDeleteContent(contentId string) error {
	return s.repo.AdminDeleteContent(contentId)
}

func (s *service) GetAnalytics() (int64, error) {
	return s.repo.GetAnalytics()
}

func (s *service) GetUserAnalytics() (int64, int64, error) {
	return s.repo.GetUserAnalytics()
}

func (s *service) GetContentAnalytics() (int64, int64, error) {
	return s.repo.GetContentAnalytics()
}

func (s *service) AdminGetStudyContent(page, limit int) ([]entities.Content, error) {
	return s.repo.AdminGetStudyContent(page, limit)
}

func (s *service) AdminCreateStudyContent(content entities.Content) error {
	return s.repo.AdminCreateStudyContent(content)
}

func (s *service) AdminUpdateStudyContent(contentId string, content entities.Content) error {
	return s.repo.AdminUpdateStudyContent(contentId, content)
}

func (s *service) AdminDeleteStudyContent(contentId string) error {
	return s.repo.AdminDeleteStudyContent(contentId)
}

func (s *service) AdminGetQuizzes(page, limit int) ([]entities.Quiz, error) {
	return s.repo.AdminGetQuizzes(page, limit)
}

func (s *service) AdminCreateQuiz(quiz entities.Quiz) error {
	return s.repo.AdminCreateQuiz(quiz)
}

func (s *service) AdminUpdateQuiz(quizId string, quiz entities.Quiz) error {
	return s.repo.AdminUpdateQuiz(quizId, quiz)
}

func (s *service) AdminDeleteQuiz(quizId string) error {
	return s.repo.AdminDeleteQuiz(quizId)
}
