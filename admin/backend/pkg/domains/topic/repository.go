package topic

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Repository interface {
	GetAll(limit, offset int, search string) ([]entities.Topic, int64, error)
	GetBySubjectID(subjectID uuid.UUID) ([]entities.Topic, error)
	GetByParentID(parentID *uuid.UUID) ([]entities.Topic, error)
	GetByID(id uuid.UUID) (*entities.Topic, error)
	Create(topic *entities.Topic) error
	Update(topic *entities.Topic) error
	Delete(id uuid.UUID) error
	Publish(id uuid.UUID) error
	Unpublish(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetAll(limit, offset int, search string) ([]entities.Topic, int64, error) {
	var topics []entities.Topic
	var total int64

    query := r.db.Model(&entities.Topic{})

	if search != "" {
		query = query.Where("title ILIKE ?", "%"+search+"%")
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

    err = query.Order("title ASC, created_at DESC").Limit(limit).Offset(offset).Find(&topics).Error
	return topics, total, err
}

func (r *repository) GetBySubjectID(subjectID uuid.UUID) ([]entities.Topic, error) {
	var topics []entities.Topic
    err := r.db.
        Where("subject_id = ?", subjectID).
        Order("title ASC, created_at DESC").Find(&topics).Error
	return topics, err
}

func (r *repository) GetByParentID(parentID *uuid.UUID) ([]entities.Topic, error) {
	var topics []entities.Topic
    query := r.db
	
	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}
	
    err := query.Order("title ASC, created_at DESC").Find(&topics).Error
	return topics, err
}

func (r *repository) GetByID(id uuid.UUID) (*entities.Topic, error) {
	var topic entities.Topic
    err := r.db.
        First(&topic, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &topic, nil
}

func (r *repository) Create(topic *entities.Topic) error {
	return r.db.Create(topic).Error
}

func (r *repository) Update(topic *entities.Topic) error {
	return r.db.Save(topic).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Topic{}, "id = ?", id).Error
}

func (r *repository) Publish(id uuid.UUID) error {
	return r.db.Model(&entities.Topic{}).Where("id = ?", id).Update("is_published", true).Error
}

func (r *repository) Unpublish(id uuid.UUID) error {
	return r.db.Model(&entities.Topic{}).Where("id = ?", id).Update("is_published", false).Error
}
