import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Settings as SettingsIcon, 
  Bell, 
  Shield, 
  User, 
  Globe,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Save,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { preferencesService } from '../../services/preferencesService';

interface NotificationSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  quiz_reminders: boolean;
  friend_requests: boolean;
  achievements: boolean;
  study_streak: boolean;
  weekly_summary: boolean;
}

interface PrivacySettings {
  profile_visibility: 'public' | 'friends' | 'private';
  show_online_status: boolean;
  show_study_stats: boolean;
  allow_friend_requests: boolean;
  show_achievements: boolean;
}

interface AppSettings {
  language: string;
  theme: 'light' | 'dark' | 'auto';
  sound_effects: boolean;
  auto_save_progress: boolean;
  quiz_auto_submit: boolean;
  show_explanations: boolean;
}

const SettingsPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'notifications' | 'privacy' | 'account'>('general');

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    email_notifications: true,
    push_notifications: true,
    quiz_reminders: true,
    friend_requests: true,
    achievements: true,
    study_streak: true,
    weekly_summary: true
  });

  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    profile_visibility: 'public',
    show_online_status: true,
    show_study_stats: true,
    allow_friend_requests: true,
    show_achievements: true
  });

  const [appSettings, setAppSettings] = useState<AppSettings>({
    language: 'en',
    theme: 'dark',
    sound_effects: true,
    auto_save_progress: true,
    quiz_auto_submit: false,
    show_explanations: true
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock settings - in real app, call preferencesService.get()
      // Current preferences service only handles interests and subject weights
      // For full settings, we'll use mock data

      // Settings are already initialized with default values
      // In a real app, you would load actual user preferences here

    } catch (err) {
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const preferences = {
        notifications: notificationSettings,
        privacy: privacySettings,
        app: appSettings
      };

      // Mock save - in real app, call preferencesService.update with proper payload
      // const response = await preferencesService.update({ interests: [], subject_weights: {} });

      // Mock success response
      if (true) {
        setSuccess('Settings saved successfully');
        
        // Apply language change
        if (appSettings.language !== i18n.language) {
          i18n.changeLanguage(appSettings.language);
        }
        
        setTimeout(() => setSuccess(null), 3000);
      }
    } catch (err) {
      setError('An error occurred while saving settings');
    } finally {
      setSaving(false);
    }
  };

  const handleNotificationChange = (key: keyof NotificationSettings, value: boolean) => {
    setNotificationSettings(prev => ({ ...prev, [key]: value }));
  };

  const handlePrivacyChange = (key: keyof PrivacySettings, value: any) => {
    setPrivacySettings(prev => ({ ...prev, [key]: value }));
  };

  const handleAppChange = (key: keyof AppSettings, value: any) => {
    setAppSettings(prev => ({ ...prev, [key]: value }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Settings</h1>
          <p className="text-dark-300 mt-1">Manage your account preferences and privacy</p>
        </div>

        <button
          onClick={saveSettings}
          disabled={saving}
          className="flex items-center gap-2 px-6 py-2 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white rounded-lg transition-colors"
        >
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <Save size={16} />
          )}
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Alerts */}
      {error && (
        <div className="bg-red-600/20 border border-red-600/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="text-red-400" size={20} />
          <span className="text-red-400">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-600/20 border border-green-600/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="text-green-400" size={20} />
          <span className="text-green-400">{success}</span>
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
        {[
          { key: 'general', label: 'General', icon: SettingsIcon },
          { key: 'notifications', label: 'Notifications', icon: Bell },
          { key: 'privacy', label: 'Privacy', icon: Shield },
          { key: 'account', label: 'Account', icon: User }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as typeof activeTab)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === key
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:text-white hover:bg-dark-700'
            }`}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        {activeTab === 'general' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">General Settings</h3>
            
            {/* Language */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                <Globe className="inline mr-2" size={16} />
                Language
              </label>
              <select
                value={appSettings.language}
                onChange={(e) => handleAppChange('language', e.target.value)}
                className="w-full max-w-xs bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
              >
                <option value="en">English</option>
                <option value="tr">Türkçe</option>
              </select>
            </div>

            {/* Theme */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                <Moon className="inline mr-2" size={16} />
                Theme
              </label>
              <select
                value={appSettings.theme}
                onChange={(e) => handleAppChange('theme', e.target.value)}
                className="w-full max-w-xs bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
              </select>
            </div>

            {/* Sound Effects */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {appSettings.sound_effects ? <Volume2 size={16} /> : <VolumeX size={16} />}
                <span className="text-white">Sound Effects</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={appSettings.sound_effects}
                  onChange={(e) => handleAppChange('sound_effects', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Auto Save Progress */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Save size={16} />
                <span className="text-white">Auto Save Progress</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={appSettings.auto_save_progress}
                  onChange={(e) => handleAppChange('auto_save_progress', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Show Explanations */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Eye size={16} />
                <span className="text-white">Show Quiz Explanations</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={appSettings.show_explanations}
                  onChange={(e) => handleAppChange('show_explanations', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Notification Settings</h3>
            
            {/* Email Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Email Notifications</span>
                <p className="text-dark-400 text-sm">Receive notifications via email</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.email_notifications}
                  onChange={(e) => handleNotificationChange('email_notifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Push Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Push Notifications</span>
                <p className="text-dark-400 text-sm">Receive browser notifications</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.push_notifications}
                  onChange={(e) => handleNotificationChange('push_notifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Quiz Reminders */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Quiz Reminders</span>
                <p className="text-dark-400 text-sm">Get reminded to take quizzes</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.quiz_reminders}
                  onChange={(e) => handleNotificationChange('quiz_reminders', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Friend Requests */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Friend Requests</span>
                <p className="text-dark-400 text-sm">Notifications for friend requests</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.friend_requests}
                  onChange={(e) => handleNotificationChange('friend_requests', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Achievements */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Achievements</span>
                <p className="text-dark-400 text-sm">Notifications for new achievements</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.achievements}
                  onChange={(e) => handleNotificationChange('achievements', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Study Streak */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Study Streak</span>
                <p className="text-dark-400 text-sm">Reminders to maintain study streak</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.study_streak}
                  onChange={(e) => handleNotificationChange('study_streak', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Weekly Summary */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Weekly Summary</span>
                <p className="text-dark-400 text-sm">Weekly progress summary emails</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.weekly_summary}
                  onChange={(e) => handleNotificationChange('weekly_summary', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          </div>
        )}

        {activeTab === 'privacy' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Privacy Settings</h3>
            
            {/* Profile Visibility */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">Profile Visibility</label>
              <select
                value={privacySettings.profile_visibility}
                onChange={(e) => handlePrivacyChange('profile_visibility', e.target.value)}
                className="w-full max-w-xs bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
              >
                <option value="public">Public</option>
                <option value="friends">Friends Only</option>
                <option value="private">Private</option>
              </select>
              <p className="text-dark-400 text-sm mt-1">Who can see your profile information</p>
            </div>

            {/* Show Online Status */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Show Online Status</span>
                <p className="text-dark-400 text-sm">Let others see when you're online</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={privacySettings.show_online_status}
                  onChange={(e) => handlePrivacyChange('show_online_status', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Show Study Stats */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Show Study Statistics</span>
                <p className="text-dark-400 text-sm">Display your study progress to others</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={privacySettings.show_study_stats}
                  onChange={(e) => handlePrivacyChange('show_study_stats', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Allow Friend Requests */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Allow Friend Requests</span>
                <p className="text-dark-400 text-sm">Let others send you friend requests</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={privacySettings.allow_friend_requests}
                  onChange={(e) => handlePrivacyChange('allow_friend_requests', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            {/* Show Achievements */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-white">Show Achievements</span>
                <p className="text-dark-400 text-sm">Display your achievements publicly</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={privacySettings.show_achievements}
                  onChange={(e) => handlePrivacyChange('show_achievements', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          </div>
        )}

        {activeTab === 'account' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Account Information</h3>
            
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-4">
                {user?.avatar && (
                  <img src={user.avatar} alt="Profile" className="w-16 h-16 rounded-full" />
                )}
                <div>
                  <h4 className="text-white font-semibold">{user?.name}</h4>
                  <p className="text-dark-400">@{user?.username}</p>
                  <p className="text-dark-400 text-sm">{user?.email}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-dark-400">Member since</span>
                  <div className="text-white">{user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</div>
                </div>
                <div>
                  <span className="text-dark-400">Account type</span>
                  <div className="text-white">Free</div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <button className="w-full text-left px-4 py-3 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors">
                <div className="text-white font-medium">Change Password</div>
                <div className="text-dark-400 text-sm">Update your account password</div>
              </button>
              
              <button className="w-full text-left px-4 py-3 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors">
                <div className="text-white font-medium">Export Data</div>
                <div className="text-dark-400 text-sm">Download your account data</div>
              </button>
              
              <button className="w-full text-left px-4 py-3 bg-red-600/20 hover:bg-red-600/30 border border-red-600/30 rounded-lg transition-colors">
                <div className="text-red-400 font-medium">Delete Account</div>
                <div className="text-red-400/70 text-sm">Permanently delete your account and all data</div>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SettingsPage;
