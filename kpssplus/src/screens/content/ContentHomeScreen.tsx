import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import {StackScreenProps} from '@react-navigation/stack';
import {CompositeScreenProps} from '@react-navigation/native';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {
  ContentStackParamList,
  MainTabParamList,
  RootStackParamList,
} from '../../navigation/types';
import {Button, Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth, useGuestMode, useTokens} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';

type Props = CompositeScreenProps<
  StackScreenProps<ContentStackParamList, 'ContentHome'>,
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, 'Content'>,
    StackScreenProps<RootStackParamList>
  >
>;

export const ContentHomeScreen: React.FC<Props> = ({navigation}) => {
  const {isAuthenticated, isGuest, user} = useAuth();
  const {getRemainingContentViews, checkContentLimit, getDemoSubjects} =
    useGuestMode();
  const {balance: tokenBalance} = useTokens();

  const [subjects, setSubjects] = useState([
    {
      id: '1',
      name: 'Türkçe',
      icon: '📚',
      color: '#FF6B6B',
      topicCount: 25,
      questionCount: 500,
    },
    {
      id: '2',
      name: 'Tarih',
      icon: '🏛️',
      color: '#4ECDC4',
      topicCount: 20,
      questionCount: 400,
    },
    {
      id: '3',
      name: 'Coğrafya',
      icon: '🌍',
      color: '#45B7D1',
      topicCount: 18,
      questionCount: 350,
    },
    {
      id: '4',
      name: 'Vatandaşlık',
      icon: '⚖️',
      color: '#96CEB4',
      topicCount: 15,
      questionCount: 300,
    },
  ]);

  // Mock user stats
  const userStats = {
    totalQuizzes: user?.totalScore ? Math.floor(user.totalScore / 10) : 0,
    correctAnswers: user?.totalScore ? Math.floor(user.totalScore / 15) : 0,
    totalScore: user?.totalScore || 0,
  };

  // Load demo subjects for guest users
  const loadDemoSubjects = useCallback(async () => {
    try {
      const demoSubjects = await getDemoSubjects();
      setSubjects(demoSubjects);
    } catch (error) {
      console.error('Error loading demo subjects:', error);
    }
  }, [getDemoSubjects]);

  useEffect(() => {
    if (isGuest) {
      loadDemoSubjects();
    }
  }, [isGuest, loadDemoSubjects]);

  const handleSubjectPress = (_subjectId: string) => {
    console.log('Navigation disabled');
  };

  const handleQuizRobotPress = () => {
    if (isGuest) {
      console.log('Navigation disabled');
    } else {
      // Navigate to quiz robot screen
      navigation.getParent()?.navigate('Quiz', {screen: 'QuizHome'});
    }
  };

  const handleProfilePress = () => {
    if (isAuthenticated) {
      navigation.getParent()?.navigate('Menu', {screen: 'Profile'});
    } else {
      console.log('Navigation disabled');
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* User Profile Card */}
        <TouchableOpacity
          style={styles.profileCard}
          onPress={handleProfilePress}>
          <View style={styles.profileInfo}>
            <View style={styles.avatarContainer}>
              {user?.avatar ? (
                <Image source={{uri: user.avatar}} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Icon name="person" size={24} color={colors.white} />
                </View>
              )}
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>
                {isAuthenticated
                  ? user?.firstName || user?.username || 'Kullanıcı'
                  : 'Misafir'}
              </Text>
              <Text style={styles.userSubtitle}>
                {isAuthenticated ? 'Profilim' : 'Giriş yapmak için tıklayın'}
              </Text>
            </View>
          </View>
          {isAuthenticated && (
            <View style={styles.tokenInfo}>
              <Icon name="diamond" size={16} color={colors.primary} />
              <Text style={styles.tokenText}>{tokenBalance}</Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Statistics Cards */}
        {isAuthenticated && (
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{userStats.totalQuizzes}</Text>
              <Text style={styles.statLabel}>Giriş</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{userStats.correctAnswers}</Text>
              <Text style={styles.statLabel}>Açık</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{userStats.totalScore}</Text>
              <Text style={styles.statLabel}>Puan</Text>
            </View>
          </View>
        )}

        {/* Guest Mode Banner */}
        {isGuest && (
          <Card style={styles.guestBanner} variant="outlined">
            <Text style={styles.guestTitle}>Demo Modundasınız</Text>
            <Text style={styles.guestText}>
              {checkContentLimit()
                ? 'Demo içerik limitiniz doldu. Tüm içeriklere erişmek için giriş yapın.'
                : `Kalan demo içerik: ${getRemainingContentViews()}`}
            </Text>
            <Button
              title="Giriş Yap"
              onPress={() => {
                navigation.navigate('Auth', {
                  screen: 'Login',
                });
              }}
              size="small"
              style={styles.loginButton}
            />
          </Card>
        )}

        {/* Subject Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Kapışma</Text>
          <View style={styles.subjectsGrid}>
            {subjects.map(subject => (
              <TouchableOpacity
                key={subject.id}
                style={[styles.subjectCard, {backgroundColor: subject.color}]}
                onPress={() => handleSubjectPress(subject.id)}>
                <Text style={styles.subjectIcon}>{subject.icon}</Text>
                <Text style={styles.subjectName}>{subject.name}</Text>
                <Text style={styles.subjectStats}>
                  Konu {subject.topicCount}
                </Text>
                <View style={styles.subjectArrow}>
                  <Icon name="chevron-forward" size={20} color={colors.white} />
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Study Topics Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Konu Çalış</Text>
          <TouchableOpacity
            style={styles.studyCard}
            onPress={() => console.log('Navigation disabled')}>
            <View style={styles.studyContent}>
              <View style={styles.studyIcon}>
                <Icon name="book" size={32} color={colors.secondary} />
              </View>
              <View style={styles.studyInfo}>
                <Text style={styles.studyTitle}>Konuları Detaylı Çalış</Text>
                <Text style={styles.studySubtitle}>
                  {isGuest ? 'Demo konulara göz atın' : 'Tüm konulara erişin'}
                </Text>
              </View>
            </View>
            <View style={styles.studyArrow}>
              <Icon
                name="chevron-forward"
                size={24}
                color={colors.text.secondary}
              />
            </View>
          </TouchableOpacity>
        </View>

        {/* Quiz Robot Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Soru Robotu</Text>
          <TouchableOpacity
            style={styles.robotCard}
            onPress={handleQuizRobotPress}>
            <View style={styles.robotContent}>
              <View style={styles.robotIcon}>
                <Icon
                  name="chatbubble-ellipses"
                  size={32}
                  color={colors.primary}
                />
              </View>
              <View style={styles.robotInfo}>
                <Text style={styles.robotTitle}>Soru Robotu</Text>
                <Text style={styles.robotSubtitle}>
                  {isGuest
                    ? 'Giriş yaparak kullanın'
                    : 'Karışık sorularla çalış'}
                </Text>
              </View>
            </View>
            <View style={styles.robotArrow}>
              <Icon
                name="chevron-forward"
                size={24}
                color={colors.text.secondary}
              />
            </View>
          </TouchableOpacity>
        </View>

        {/* Battle Section - Only for authenticated users */}
        {!isGuest && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kapışma</Text>
            <View style={styles.battleGrid}>
              <TouchableOpacity
                style={styles.battleCard}
                onPress={() =>
                  navigation
                    .getParent()
                    ?.navigate('Battle', {screen: 'GroupBattle'})
                }>
                <View style={styles.battleIcon}>
                  <Icon name="people" size={24} color={colors.primary} />
                </View>
                <Text style={styles.battleTitle}>Grup Kapışması</Text>
                <Text style={styles.battleDescription}>
                  Arkadaşlarınızla grup halinde kapışın
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.battleCard}
                onPress={() =>
                  navigation
                    .getParent()
                    ?.navigate('Battle', {screen: 'FriendChallenge'})
                }>
                <View style={styles.battleIcon}>
                  <Icon name="flash" size={24} color={colors.secondary} />
                </View>
                <Text style={styles.battleTitle}>1v1 Kapışma</Text>
                <Text style={styles.battleDescription}>
                  Arkadaşlarınızla meydan okuma yapın
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing[4],
  },
  // Profile Card Styles
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    padding: spacing[4],
    borderRadius: 12,
    marginVertical: spacing[3],
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: spacing[3],
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    ...typography.h6,
    color: colors.text.primary,
    marginBottom: 2,
  },
  userSubtitle: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  tokenInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryMuted,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 8,
  },
  tokenText: {
    ...typography.caption,
    color: colors.primary,
    marginLeft: spacing[1],
    fontWeight: '600',
  },
  // Statistics Cards
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing[4],
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacing[3],
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: spacing[1],
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statNumber: {
    ...typography.h4,
    color: colors.primary,
    fontWeight: 'bold',
    marginBottom: spacing[1],
  },
  statLabel: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  // Guest Banner
  guestBanner: {
    marginVertical: spacing[4],
    backgroundColor: colors.warningMuted,
    borderColor: colors.warning,
  },
  guestTitle: {
    ...typography.h6,
    color: colors.warningDark,
    marginBottom: spacing[2],
  },
  guestText: {
    ...typography.body2,
    color: colors.warningDark,
    marginBottom: spacing[3],
  },
  loginButton: {
    alignSelf: 'flex-start',
  },
  // Section Styles
  section: {
    marginVertical: spacing[4],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[3],
    color: colors.text.primary,
  },
  // Subject Cards
  subjectsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  subjectCard: {
    width: '48%',
    padding: spacing[4],
    borderRadius: 12,
    marginBottom: spacing[3],
    position: 'relative',
  },
  subjectIcon: {
    fontSize: 32,
    marginBottom: spacing[2],
  },
  subjectName: {
    ...typography.h6,
    color: colors.white,
    fontWeight: 'bold',
    marginBottom: spacing[1],
  },
  subjectStats: {
    ...typography.caption,
    color: colors.white,
    opacity: 0.9,
  },
  subjectArrow: {
    position: 'absolute',
    top: spacing[2],
    right: spacing[2],
  },
  // Robot Card
  robotCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    padding: spacing[4],
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  robotContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  robotIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primaryMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  robotInfo: {
    flex: 1,
  },
  robotTitle: {
    ...typography.h6,
    color: colors.text.primary,
    marginBottom: spacing[1],
  },
  robotSubtitle: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  robotArrow: {
    marginLeft: spacing[2],
  },
  // Study Card
  studyCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    padding: spacing[4],
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  studyContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  studyIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.secondary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  studyInfo: {
    flex: 1,
  },
  studyTitle: {
    ...typography.body1,
    fontWeight: '600',
    marginBottom: spacing[1],
  },
  studySubtitle: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  studyArrow: {
    marginLeft: spacing[2],
  },
  // Battle Section
  battleGrid: {
    flexDirection: 'row',
    gap: spacing[3],
  },
  battleCard: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacing[4],
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  battleIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primaryMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing[3],
  },
  battleTitle: {
    ...typography.body1,
    fontWeight: '600',
    marginBottom: spacing[1],
    textAlign: 'center',
  },
  battleDescription: {
    ...typography.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});
