package social

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	// Friendship management
	SendFriendRequest(userID string, payload *dtos.SendFriendRequestRequest) (*dtos.FriendRequestResponse, error)
	RespondToFriendRequest(userID string, payload *dtos.RespondToFriendRequestRequest) (*dtos.FriendRequestResponse, error)
	GetFriendRequests(userID string, requestType string) (*dtos.FriendRequestsResponse, error)
	GetFriends(userID string, payload *dtos.GetFriendsRequest) (*dtos.FriendListResponse, error)
	RemoveFriend(userID string, friendID string) error
	GetFriendshipStatus(userID string, targetUserID string) (string, error)
	GetFriendRequest(friendshipID string) (*dtos.FriendRequestResponse, error)

	// Follow system
	FollowUser(userID string, payload *dtos.FollowUserRequest) (*dtos.FollowResponse, error)
	UnfollowUser(userID string, targetUserID string) error
	GetFollowers(userID string, payload *dtos.GetFollowersRequest) (*dtos.FollowersResponse, error)
	GetFollowing(userID string, payload *dtos.GetFollowingRequest) (*dtos.FollowingResponse, error)
	IsFollowing(userID string, targetUserID string) (bool, error)

	// User search and discovery
	SearchUsers(userID string, payload *dtos.SearchUsersRequest) (*dtos.SearchUsersResponse, error)
	GetFriendSuggestions(userID string, payload *dtos.GetFriendSuggestionsRequest) (*dtos.FriendSuggestionsResponse, error)
	GetMutualFriends(userID string, targetUserID string) (*dtos.MutualFriendsResponse, error)

	// User blocking
	BlockUser(userID string, payload *dtos.BlockUserRequest) error
	UnblockUser(userID string, payload *dtos.UnblockUserRequest) error
	GetBlockedUsers(userID string) (*dtos.BlockedUsersResponse, error)
	IsUserBlocked(userID string, targetUserID string) (bool, error)

	// Friend activity and timeline
	GetFriendActivity(userID string, payload *dtos.GetFriendActivityRequest) (*dtos.FriendActivityResponse, error)
	GetUserProfile(userID string, targetUserID string) (*dtos.PublicUserProfileResponse, error)

	// Social statistics
	GetSocialStats(userID string) (*dtos.SocialStatsResponse, error)
	GetFriendshipHistory(userID string, payload *dtos.GetFriendshipHistoryRequest) (*dtos.FriendshipHistoryResponse, error)

	// Privacy and settings
	UpdatePrivacySettings(userID string, payload *dtos.UpdatePrivacySettingsRequest) (*dtos.PrivacySettingsResponse, error)
	GetPrivacySettings(userID string) (*dtos.PrivacySettingsResponse, error)

	// Notification helpers
	CreateFriendAcceptedNotification(requesterID string, accepterID string) error
	CreateNewFollowerNotification(userID string, followerID string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Friendship management
func (r *repository) SendFriendRequest(userID string, payload *dtos.SendFriendRequestRequest) (*dtos.FriendRequestResponse, error) {
	friendship := entities.Friendship{
		RequesterID: uuid.MustParse(userID),
		AddresseeID: payload.AddresseeID,
		Status:      entities.FriendshipStatusPending,
	}

	err := r.db.Create(&friendship).Error
	if err != nil {
		return nil, errors.New("Failed to send friend request")
	}

	// Load users for response
	var requester, addressee entities.User
	r.db.Where("id = ?", userID).First(&requester)
	r.db.Where("id = ?", payload.AddresseeID).First(&addressee)

	return &dtos.FriendRequestResponse{
		ID:          friendship.ID,
		RequesterID: friendship.RequesterID,
		AddresseeID: friendship.AddresseeID,
		Status:      string(friendship.Status),
		CreatedAt:   friendship.CreatedAt,
		Requester: dtos.UserInfo{
			ID:       requester.ID,
			Username: requester.Username,
			Name:     requester.Name,
			// Add other fields as needed
		},
		Addressee: dtos.UserInfo{
			ID:       addressee.ID,
			Username: addressee.Username,
			Name:     addressee.Name,
			// Add other fields as needed
		},
	}, nil
}

func (r *repository) RespondToFriendRequest(userID string, payload *dtos.RespondToFriendRequestRequest) (*dtos.FriendRequestResponse, error) {
	var friendship entities.Friendship
	err := r.db.Where("id = ?", payload.FriendshipID).First(&friendship).Error
	if err != nil {
		return nil, errors.New("Friend request not found")
	}

	if payload.Action == "accept" {
		friendship.Status = entities.FriendshipStatusAccepted
	} else if payload.Action == "reject" {
		friendship.Status = entities.FriendshipStatusRejected
	}

	err = r.db.Save(&friendship).Error
	if err != nil {
		return nil, errors.New("Failed to respond to friend request")
	}

	// Load users for response
	var requester, addressee entities.User
	r.db.Where("id = ?", friendship.RequesterID).First(&requester)
	r.db.Where("id = ?", friendship.AddresseeID).First(&addressee)

	return &dtos.FriendRequestResponse{
		ID:          friendship.ID,
		RequesterID: friendship.RequesterID,
		AddresseeID: friendship.AddresseeID,
		Status:      string(friendship.Status),
		CreatedAt:   friendship.CreatedAt,
		Requester: dtos.UserInfo{
			ID:       requester.ID,
			Username: requester.Username,
			Name:     requester.Name,
		},
		Addressee: dtos.UserInfo{
			ID:       addressee.ID,
			Username: addressee.Username,
			Name:     addressee.Name,
		},
	}, nil
}

func (r *repository) GetFriendRequests(userID string, requestType string) (*dtos.FriendRequestsResponse, error) {
	var friendships []entities.Friendship
	query := r.db.Preload("Requester").Preload("Addressee").Where("status = ?", entities.FriendshipStatusPending)

	if requestType == "sent" {
		query = query.Where("requester_id = ?", userID)
	} else if requestType == "received" {
		query = query.Where("addressee_id = ?", userID)
	} else {
		query = query.Where("requester_id = ? OR addressee_id = ?", userID, userID)
	}

	err := query.Find(&friendships).Error
	if err != nil {
		return nil, errors.New("Failed to get friend requests")
	}

	requests := make([]dtos.FriendRequestResponse, len(friendships))
	for i, friendship := range friendships {
		// Get requester info
		var requester entities.User
		r.db.Where("id = ?", friendship.RequesterID).First(&requester)

		// Get addressee info
		var addressee entities.User
		r.db.Where("id = ?", friendship.AddresseeID).First(&addressee)

		requests[i] = dtos.FriendRequestResponse{
			ID:          friendship.ID,
			RequesterID: friendship.RequesterID,
			AddresseeID: friendship.AddresseeID,
			Status:      string(friendship.Status),
			CreatedAt:   friendship.CreatedAt,
			Requester: dtos.UserInfo{
				ID:         requester.ID,
				Username:   requester.Username,
				Name:       requester.Name,
				Email:      requester.Email,
				IsVerified: requester.IsVerified,
				CreatedAt:  requester.CreatedAt,
			},
			Addressee: dtos.UserInfo{
				ID:         addressee.ID,
				Username:   addressee.Username,
				Name:       addressee.Name,
				Email:      addressee.Email,
				IsVerified: addressee.IsVerified,
				CreatedAt:  addressee.CreatedAt,
			},
		}
	}

	return &dtos.FriendRequestsResponse{
		Requests: requests,
		Total:    len(requests),
	}, nil
}

func (r *repository) GetFriends(userID string, payload *dtos.GetFriendsRequest) (*dtos.FriendListResponse, error) {
	// This is a simplified implementation
	// In a real implementation, you would handle pagination, sorting, and search
	var friendships []entities.Friendship
	err := r.db.Preload("Requester").Preload("Addressee").
		Where("(requester_id = ? OR addressee_id = ?) AND status = ?", userID, userID, entities.FriendshipStatusAccepted).
		Find(&friendships).Error
	if err != nil {
		return nil, errors.New("Failed to get friends")
	}

	friends := make([]dtos.FriendInfo, 0)
	for _, friendship := range friendships {
		var friend entities.User
		var friendID uuid.UUID

		if friendship.RequesterID.String() == userID {
			friendID = friendship.AddresseeID
		} else {
			friendID = friendship.RequesterID
		}

		// Get friend info
		r.db.Where("id = ?", friendID).First(&friend)

		friendInfo := dtos.FriendInfo{
			ID:             friend.ID,
			Username:       friend.Username,
			Name:           friend.Name,
			FriendshipDate: friendship.CreatedAt,
		}
		friends = append(friends, friendInfo)
	}

	return &dtos.FriendListResponse{
		Friends: friends,
		Total:   len(friends),
	}, nil
}

func (r *repository) RemoveFriend(userID string, friendID string) error {
	err := r.db.Where("(requester_id = ? AND addressee_id = ?) OR (requester_id = ? AND addressee_id = ?)",
		userID, friendID, friendID, userID).Delete(&entities.Friendship{}).Error
	if err != nil {
		return errors.New("Failed to remove friend")
	}
	return nil
}

func (r *repository) GetFriendshipStatus(userID string, targetUserID string) (string, error) {
	var friendship entities.Friendship
	err := r.db.Where("(requester_id = ? AND addressee_id = ?) OR (requester_id = ? AND addressee_id = ?)",
		userID, targetUserID, targetUserID, userID).First(&friendship).Error
	if err != nil {
		return "", err
	}
	return string(friendship.Status), nil
}

func (r *repository) GetFriendRequest(friendshipID string) (*dtos.FriendRequestResponse, error) {
	var friendship entities.Friendship
	err := r.db.Preload("Requester").Preload("Addressee").Where("id = ?", friendshipID).First(&friendship).Error
	if err != nil {
		return nil, errors.New("Friend request not found")
	}

	// Get requester info
	var requester entities.User
	r.db.Where("id = ?", friendship.RequesterID).First(&requester)

	// Get addressee info
	var addressee entities.User
	r.db.Where("id = ?", friendship.AddresseeID).First(&addressee)

	return &dtos.FriendRequestResponse{
		ID:          friendship.ID,
		RequesterID: friendship.RequesterID,
		AddresseeID: friendship.AddresseeID,
		Status:      string(friendship.Status),
		CreatedAt:   friendship.CreatedAt,
		Requester: dtos.UserInfo{
			ID:         requester.ID,
			Username:   requester.Username,
			Name:       requester.Name,
			Email:      requester.Email,
			IsVerified: requester.IsVerified,
			CreatedAt:  requester.CreatedAt,
		},
		Addressee: dtos.UserInfo{
			ID:         addressee.ID,
			Username:   addressee.Username,
			Name:       addressee.Name,
			Email:      addressee.Email,
			IsVerified: addressee.IsVerified,
			CreatedAt:  addressee.CreatedAt,
		},
	}, nil
}

// Follow system (placeholder implementations)
func (r *repository) FollowUser(userID string, payload *dtos.FollowUserRequest) (*dtos.FollowResponse, error) {
	follow := entities.Follow{
		FollowerID:  uuid.MustParse(userID),
		FollowingID: payload.UserID,
	}

	err := r.db.Create(&follow).Error
	if err != nil {
		return nil, errors.New("Failed to follow user")
	}

	return &dtos.FollowResponse{
		ID:          follow.ID,
		FollowerID:  follow.FollowerID,
		FollowingID: follow.FollowingID,
		CreatedAt:   follow.CreatedAt,
	}, nil
}

func (r *repository) UnfollowUser(userID string, targetUserID string) error {
	err := r.db.Where("follower_id = ? AND following_id = ?", userID, targetUserID).Delete(&entities.Follow{}).Error
	if err != nil {
		return errors.New("Failed to unfollow user")
	}
	return nil
}

func (r *repository) GetFollowers(userID string, payload *dtos.GetFollowersRequest) (*dtos.FollowersResponse, error) {
	// TODO: Implement comprehensive followers retrieval with pagination
	return &dtos.FollowersResponse{
		Followers: []dtos.FollowerInfo{},
		Total:     0,
	}, nil
}

func (r *repository) GetFollowing(userID string, payload *dtos.GetFollowingRequest) (*dtos.FollowingResponse, error) {
	// TODO: Implement comprehensive following retrieval with pagination
	return &dtos.FollowingResponse{
		Following: []dtos.FollowerInfo{},
		Total:     0,
	}, nil
}

func (r *repository) IsFollowing(userID string, targetUserID string) (bool, error) {
	var count int64
	err := r.db.Model(&entities.Follow{}).Where("follower_id = ? AND following_id = ?", userID, targetUserID).Count(&count).Error
	return count > 0, err
}

// User search and discovery (placeholder implementations)
func (r *repository) SearchUsers(userID string, payload *dtos.SearchUsersRequest) (*dtos.SearchUsersResponse, error) {
	// TODO: Implement user search with relationship status
	return &dtos.SearchUsersResponse{
		Users: []dtos.SearchUserInfo{},
		Total: 0,
	}, nil
}

func (r *repository) GetFriendSuggestions(userID string, payload *dtos.GetFriendSuggestionsRequest) (*dtos.FriendSuggestionsResponse, error) {
	// TODO: Implement friend suggestion algorithm
	return &dtos.FriendSuggestionsResponse{
		Suggestions: []dtos.SuggestionInfo{},
		Total:       0,
	}, nil
}

func (r *repository) GetMutualFriends(userID string, targetUserID string) (*dtos.MutualFriendsResponse, error) {
	// TODO: Implement mutual friends calculation
	return &dtos.MutualFriendsResponse{
		MutualFriends: []dtos.UserInfo{},
		Total:         0,
	}, nil
}

// User blocking (placeholder implementations)
func (r *repository) BlockUser(userID string, payload *dtos.BlockUserRequest) error {
	blockerID := uuid.MustParse(userID)
	block := entities.UserBlock{
		BlockerID: &blockerID,
		BlockedID: &payload.UserID,
	}

	err := r.db.Create(&block).Error
	if err != nil {
		return errors.New("Failed to block user")
	}
	return nil
}

func (r *repository) UnblockUser(userID string, payload *dtos.UnblockUserRequest) error {
	err := r.db.Where("blocker_id = ? AND blocked_id = ?", userID, payload.UserID).Delete(&entities.UserBlock{}).Error
	if err != nil {
		return errors.New("Failed to unblock user")
	}
	return nil
}

func (r *repository) GetBlockedUsers(userID string) (*dtos.BlockedUsersResponse, error) {
	// TODO: Implement blocked users retrieval
	return &dtos.BlockedUsersResponse{
		BlockedUsers: []dtos.BlockedUserInfo{},
		Total:        0,
	}, nil
}

func (r *repository) IsUserBlocked(userID string, targetUserID string) (bool, error) {
	var count int64
	err := r.db.Model(&entities.UserBlock{}).Where("blocker_id = ? AND blocked_id = ?", userID, targetUserID).Count(&count).Error
	return count > 0, err
}

// Friend activity and timeline (placeholder implementations)
func (r *repository) GetFriendActivity(userID string, payload *dtos.GetFriendActivityRequest) (*dtos.FriendActivityResponse, error) {
	// TODO: Implement friend activity feed
	return &dtos.FriendActivityResponse{
		Activities: []dtos.FriendActivityInfo{},
		Total:      0,
	}, nil
}

func (r *repository) GetUserProfile(userID string, targetUserID string) (*dtos.PublicUserProfileResponse, error) {
	// TODO: Implement public user profile with relationship info
	var user entities.User
	err := r.db.Where("id = ?", targetUserID).First(&user).Error
	if err != nil {
		return nil, errors.New("User not found")
	}

	userInfo := dtos.UserInfo{
		ID:       user.ID,
		Username: user.Username,
		Name:     user.Name,
		// Add other public fields
	}

	return &dtos.PublicUserProfileResponse{
		User: userInfo,
		Stats: dtos.PublicUserStats{
			JoinedAt: user.CreatedAt,
		},
		Relationship:   dtos.UserRelationshipInfo{},
		RecentActivity: []dtos.FriendActivityInfo{},
	}, nil
}

// Social statistics (placeholder implementations)
func (r *repository) GetSocialStats(userID string) (*dtos.SocialStatsResponse, error) {
	// TODO: Implement comprehensive social statistics
	return &dtos.SocialStatsResponse{
		UserID:         uuid.MustParse(userID),
		FriendsCount:   0,
		FollowersCount: 0,
		FollowingCount: 0,
	}, nil
}

func (r *repository) GetFriendshipHistory(userID string, payload *dtos.GetFriendshipHistoryRequest) (*dtos.FriendshipHistoryResponse, error) {
	// TODO: Implement friendship history tracking
	return &dtos.FriendshipHistoryResponse{
		History:    []dtos.FriendshipHistoryEntry{},
		Total:      0,
		Page:       1,
		Limit:      20,
		TotalPages: 0,
	}, nil
}

// Privacy and settings (placeholder implementations)
func (r *repository) UpdatePrivacySettings(userID string, payload *dtos.UpdatePrivacySettingsRequest) (*dtos.PrivacySettingsResponse, error) {
	// TODO: Implement privacy settings update
	return &dtos.PrivacySettingsResponse{
		UserID:               uuid.MustParse(userID),
		ProfileVisibility:    "public",
		ActivityVisibility:   "friends",
		FriendListVisibility: "friends",
		AllowFriendRequests:  true,
		AllowFollows:         true,
		ShowOnlineStatus:     true,
		UpdatedAt:            time.Now(),
	}, nil
}

func (r *repository) GetPrivacySettings(userID string) (*dtos.PrivacySettingsResponse, error) {
	// TODO: Implement privacy settings retrieval
	return &dtos.PrivacySettingsResponse{
		UserID:               uuid.MustParse(userID),
		ProfileVisibility:    "public",
		ActivityVisibility:   "friends",
		FriendListVisibility: "friends",
		AllowFriendRequests:  true,
		AllowFollows:         true,
		ShowOnlineStatus:     true,
		UpdatedAt:            time.Now(),
	}, nil
}

// Notification helpers (placeholder implementations)
func (r *repository) CreateFriendAcceptedNotification(requesterID string, accepterID string) error {
	// TODO: Create notification for friend request acceptance
	return nil
}

func (r *repository) CreateNewFollowerNotification(userID string, followerID string) error {
	// TODO: Create notification for new follower
	return nil
}
