import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout';
import { apiGet, apiPost, apiPut, apiDel, apiPatch } from '../lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  DocumentTextIcon,
  PlayIcon,
  QuestionMarkCircleIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';

interface Topic {
  id: string;
  title: string;
}

interface Content {
  id: string;
  topic_id?: string;
  title: string;
  description?: string;
  content_type: string;
  difficulty_level: string;
  body?: string;
  video_url?: string;
  pdf_url?: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
  topic?: Topic;
}

const contentTypes = [
  { value: 'lesson', label: 'Konu Anlatımı', icon: DocumentTextIcon },
  { value: 'video', label: 'Video', icon: PlayIcon },
  { value: 'quiz', label: 'Mini Test', icon: QuestionMarkCircleIcon },
  { value: 'exam', label: '<PERSON><PERSON><PERSON>', icon: AcademicCapIcon },
  { value: 'pdf', label: 'PDF', icon: DocumentTextIcon }
];

const difficultyLevels = [
  { value: 'beginner', label: 'Başlangıç' },
  { value: 'intermediate', label: 'Orta' },
  { value: 'advanced', label: 'İleri' }
];

const Contents: React.FC = () => {
  const [contents, setContents] = useState<Content[]>([]);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingContent, setEditingContent] = useState<Content | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content_type: '',
    difficulty_level: '',
    body: '',
    video_url: '',
    pdf_url: '',
    topic_id: '',
    is_published: true
  });

  useEffect(() => {
    loadContents();
    loadTopics();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, selectedType, selectedDifficulty]);

  const loadContents = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchQuery) params.append('q', searchQuery);
      if (selectedType) params.append('content_type', selectedType);
      if (selectedDifficulty) params.append('difficulty_level', selectedDifficulty);
      params.append('limit', '50');

      const response = await apiGet<{ data: Content[]; total: number }>(`/contents?${params}`);
      setContents(response.data || []);
    } catch (error) {
      console.error('Error loading contents:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTopics = async () => {
    try {
      const response = await apiGet<{ data: Topic[] }>('/topics?limit=100');
      setTopics(response.data || []);
    } catch (error) {
      console.error('Error loading topics:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingContent) {
        await apiPut(`/contents/${editingContent.id}`, formData);
      } else {
        await apiPost('/contents', formData);
      }

      setShowCreateModal(false);
      setEditingContent(null);
      resetForm();
      loadContents();
    } catch (error) {
      console.error('Error saving content:', error);
    }
  };

  const handleEdit = (content: Content) => {
    setEditingContent(content);
    setFormData({
      title: content.title,
      description: content.description || '',
      content_type: content.content_type,
      difficulty_level: content.difficulty_level,
      body: content.body || '',
      video_url: content.video_url || '',
      pdf_url: content.pdf_url || '',
      topic_id: content.topic_id || '',
      is_published: content.is_published
    });
    setShowCreateModal(true);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Bu içeriği silmek istediğinizden emin misiniz?')) return;

    try {
      await apiDel(`/contents/${id}`);
      loadContents();
    } catch (error) {
      console.error('Error deleting content:', error);
    }
  };

  const handleTogglePublish = async (content: Content) => {
    try {
      if (content.is_published) {
        await apiPatch(`/contents/${content.id}/unpublish`);
      } else {
        await apiPatch(`/contents/${content.id}/publish`);
      }
      loadContents();
    } catch (error) {
      console.error('Error toggling publish status:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      content_type: '',
      difficulty_level: '',
      body: '',
      video_url: '',
      pdf_url: '',
      topic_id: '',
      is_published: true
    });
  };

  const handleCreateNew = () => {
    setEditingContent(null);
    resetForm();
    setShowCreateModal(true);
  };

  const getContentTypeIcon = (type: string) => {
    const contentType = contentTypes.find(ct => ct.value === type);
    return contentType?.icon || DocumentTextIcon;
  };

  const getContentTypeLabel = (type: string) => {
    const contentType = contentTypes.find(ct => ct.value === type);
    return contentType?.label || type;
  };

  const getDifficultyLabel = (level: string) => {
    const difficulty = difficultyLevels.find(dl => dl.value === level);
    return difficulty?.label || level;
  };

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              İçerikler
            </h1>
            <p className="mt-2 text-slate-300">
              KPSS içeriklerini yönetin (Konu Anlatımı, Video, Mini Test, Deneme Sınavı, PDF...)
            </p>
          </div>
          <button
            onClick={handleCreateNew}
            className="mt-4 sm:mt-0 btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            İçerik Ekle
          </button>
        </div>

        {/* Filters */}
        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Arama
              </label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                <input
                  type="text"
                  placeholder="İçerik ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="input-field pl-10"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                İçerik Türü
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="input-field"
              >
                <option value="">Tüm Türler</option>
                {contentTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Zorluk Seviyesi
              </label>
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="input-field"
              >
                <option value="">Tüm Seviyeler</option>
                {difficultyLevels.map((level) => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Contents List */}
        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-slate-400">Yükleniyor...</p>
            </div>
          ) : contents.length === 0 ? (
            <div className="p-8 text-center">
              <DocumentTextIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-400">Henüz içerik bulunmuyor.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-slate-900/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      İçerik
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Tür
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Zorluk
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Konu
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Durum
                    </th>
                    <th className="px-6 py-4 text-right text-xs font-medium text-slate-300 uppercase tracking-wider">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-700/50">
                  {contents.map((content) => {
                    const IconComponent = getContentTypeIcon(content.content_type);
                    return (
                      <tr key={content.id} className="hover:bg-slate-700/30 transition-colors duration-200">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <IconComponent className="h-5 w-5 text-blue-400 mr-3" />
                            <div>
                              <div className="text-sm font-medium text-slate-200">
                                {content.title}
                              </div>
                              {content.description && (
                                <div className="text-sm text-slate-400 mt-1">
                                  {content.description.length > 100
                                    ? `${content.description.substring(0, 100)}...`
                                    : content.description
                                  }
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-300">
                          {getContentTypeLabel(content.content_type)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-300">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            content.difficulty_level === 'beginner'
                              ? 'bg-green-500/20 text-green-400'
                              : content.difficulty_level === 'intermediate'
                              ? 'bg-yellow-500/20 text-yellow-400'
                              : 'bg-red-500/20 text-red-400'
                          }`}>
                            {getDifficultyLabel(content.difficulty_level)}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-300">
                          {content.topic?.title || '-'}
                        </td>
                        <td className="px-6 py-4">
                          <button
                            onClick={() => handleTogglePublish(content)}
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200 ${
                              content.is_published
                                ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                                : 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30'
                            }`}
                          >
                            {content.is_published ? (
                              <>
                                <EyeIcon className="h-3 w-3 mr-1" />
                                Yayında
                              </>
                            ) : (
                              <>
                                <EyeSlashIcon className="h-3 w-3 mr-1" />
                                Taslak
                              </>
                            )}
                          </button>
                        </td>
                        <td className="px-6 py-4 text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => handleEdit(content)}
                              className="text-blue-400 hover:text-blue-300 transition-colors duration-200"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(content.id)}
                              className="text-red-400 hover:text-red-300 transition-colors duration-200"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-slate-800 rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-white mb-6">
              {editingContent ? 'İçerik Düzenle' : 'Yeni İçerik Ekle'}
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Başlık *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="input-field"
                    placeholder="İçerik başlığı"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    İçerik Türü *
                  </label>
                  <select
                    required
                    value={formData.content_type}
                    onChange={(e) => setFormData({ ...formData, content_type: e.target.value })}
                    className="input-field"
                  >
                    <option value="">Tür Seçin</option>
                    {contentTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Zorluk Seviyesi *
                  </label>
                  <select
                    required
                    value={formData.difficulty_level}
                    onChange={(e) => setFormData({ ...formData, difficulty_level: e.target.value })}
                    className="input-field"
                  >
                    <option value="">Seviye Seçin</option>
                    {difficultyLevels.map((level) => (
                      <option key={level.value} value={level.value}>
                        {level.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Konu
                  </label>
                  <select
                    value={formData.topic_id}
                    onChange={(e) => setFormData({ ...formData, topic_id: e.target.value })}
                    className="input-field"
                  >
                    <option value="">Konu Seçin</option>
                    {topics.map((topic) => (
                      <option key={topic.id} value={topic.id}>
                        {topic.title}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Açıklama
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="input-field"
                  rows={3}
                  placeholder="İçerik açıklaması"
                />
              </div>

              {formData.content_type === 'video' && (
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Video URL
                  </label>
                  <input
                    type="url"
                    value={formData.video_url}
                    onChange={(e) => setFormData({ ...formData, video_url: e.target.value })}
                    className="input-field"
                    placeholder="https://..."
                  />
                </div>
              )}

              {formData.content_type === 'pdf' && (
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    PDF URL
                  </label>
                  <input
                    type="url"
                    value={formData.pdf_url}
                    onChange={(e) => setFormData({ ...formData, pdf_url: e.target.value })}
                    className="input-field"
                    placeholder="https://..."
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  İçerik
                </label>
                <textarea
                  value={formData.body}
                  onChange={(e) => setFormData({ ...formData, body: e.target.value })}
                  className="input-field"
                  rows={8}
                  placeholder="İçerik metni (HTML desteklenir)"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_published"
                  checked={formData.is_published}
                  onChange={(e) => setFormData({ ...formData, is_published: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_published" className="ml-2 block text-sm text-slate-300">
                  Yayında
                </label>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="btn-secondary"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  {editingContent ? 'Güncelle' : 'Oluştur'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default Contents;
