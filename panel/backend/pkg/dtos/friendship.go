package dtos

import (
	"time"

	"github.com/google/uuid"
)

// Friendship DTOs
type SendFriendRequestRequest struct {
	AddresseeID uuid.UUID `json:"addressee_id" validate:"required"`
}

type FriendRequestResponse struct {
	ID          uuid.UUID `json:"id"`
	RequesterID uuid.UUID `json:"requester_id"`
	AddresseeID uuid.UUID `json:"addressee_id"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	Requester   UserInfo  `json:"requester"`
	Addressee   UserInfo  `json:"addressee"`
}

type RespondToFriendRequestRequest struct {
	FriendshipID uuid.UUID `json:"friendship_id" validate:"required"`
	Action       string    `json:"action" validate:"required,oneof=accept reject"`
}

type FriendListResponse struct {
	Friends []FriendInfo `json:"friends"`
	Total   int          `json:"total"`
}

type FriendInfo struct {
	ID                uuid.UUID  `json:"id"`
	Username          string     `json:"username"`
	Name              string     `json:"name"`
	ProfileImageURL   *string    `json:"profile_image_url"`
	StudyArea         *string    `json:"study_area"`
	IsOnline          bool       `json:"is_online"`
	LastActiveAt      *time.Time `json:"last_active_at"`
	FriendshipDate    time.Time  `json:"friendship_date"`
	StudyStreak       int        `json:"study_streak"`
	TotalStudyTime    int        `json:"total_study_time"`
	QuestionsAnswered int        `json:"questions_answered"`
}

// Follow DTOs
type FollowUserRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

type FollowResponse struct {
	ID          uuid.UUID `json:"id"`
	FollowerID  uuid.UUID `json:"follower_id"`
	FollowingID uuid.UUID `json:"following_id"`
	CreatedAt   time.Time `json:"created_at"`
}

type FollowersResponse struct {
	Followers []FollowerInfo `json:"followers"`
	Total     int            `json:"total"`
}

type FollowingResponse struct {
	Following []FollowerInfo `json:"following"`
	Total     int            `json:"total"`
}

type FollowerInfo struct {
	ID              uuid.UUID  `json:"id"`
	Username        string     `json:"username"`
	Name            string     `json:"name"`
	ProfileImageURL *string    `json:"profile_image_url"`
	StudyArea       *string    `json:"study_area"`
	IsOnline        bool       `json:"is_online"`
	LastActiveAt    *time.Time `json:"last_active_at"`
	FollowDate      time.Time  `json:"follow_date"`
	StudyStreak     int        `json:"study_streak"`
	BadgesEarned    int        `json:"badges_earned"`
}

// User Search DTOs
type SearchUsersRequest struct {
	Query  string `json:"query" validate:"required,min=2"`
	Limit  int    `json:"limit" validate:"omitempty,min=1,max=50"`
	Offset int    `json:"offset" validate:"omitempty,min=0"`
}

type SearchUsersResponse struct {
	Users []SearchUserInfo `json:"users"`
	Total int              `json:"total"`
}

type SearchUserInfo struct {
	ID               uuid.UUID `json:"id"`
	Username         string    `json:"username"`
	Name             string    `json:"name"`
	ProfileImageURL  *string   `json:"profile_image_url"`
	StudyArea        *string   `json:"study_area"`
	StudyStreak      int       `json:"study_streak"`
	BadgesEarned     int       `json:"badges_earned"`
	IsFriend         bool      `json:"is_friend"`
	IsFollowing      bool      `json:"is_following"`
	IsFollower       bool      `json:"is_follower"`
	FriendshipStatus *string   `json:"friendship_status"` // pending, accepted, rejected, blocked
}

// Friend Suggestions DTOs
type FriendSuggestionsResponse struct {
	Suggestions []SuggestionInfo `json:"suggestions"`
	Total       int              `json:"total"`
}

type SuggestionInfo struct {
	ID              uuid.UUID `json:"id"`
	Username        string    `json:"username"`
	Name            string    `json:"name"`
	ProfileImageURL *string   `json:"profile_image_url"`
	StudyArea       *string   `json:"study_area"`
	StudyStreak     int       `json:"study_streak"`
	BadgesEarned    int       `json:"badges_earned"`
	MutualFriends   int       `json:"mutual_friends"`
	SimilarityScore float64   `json:"similarity_score"`
	Reason          string    `json:"reason"` // "mutual_friends", "same_study_area", "similar_progress"
}

// Block User DTOs
type BlockUserRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

type UnblockUserRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

type BlockedUsersResponse struct {
	BlockedUsers []BlockedUserInfo `json:"blocked_users"`
	Total        int               `json:"total"`
}

type BlockedUserInfo struct {
	ID              uuid.UUID `json:"id"`
	Username        string    `json:"username"`
	Name            string    `json:"name"`
	ProfileImageURL *string   `json:"profile_image_url"`
	BlockedAt       time.Time `json:"blocked_at"`
}

// Friend Activity DTOs
type FriendActivityResponse struct {
	Activities []FriendActivityInfo `json:"activities"`
	Total      int                  `json:"total"`
}

type FriendActivityInfo struct {
	ID          uuid.UUID      `json:"id"`
	UserID      uuid.UUID      `json:"user_id"`
	User        UserInfo       `json:"user"`
	Type        string         `json:"type"`
	Title       string         `json:"title"`
	Description *string        `json:"description"`
	Metadata    map[string]any `json:"metadata"`
	CreatedAt   time.Time      `json:"created_at"`
}

// Additional Social DTOs
type GetFriendsRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=name friendship_date last_active study_streak"`
	SortDesc bool   `json:"sort_desc"`
	Search   string `json:"search" validate:"omitempty,min=2"`
}

type GetFollowersRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=name follow_date last_active"`
	SortDesc bool   `json:"sort_desc"`
}

type GetFollowingRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=name follow_date last_active"`
	SortDesc bool   `json:"sort_desc"`
}

type GetFriendSuggestionsRequest struct {
	Limit int `json:"limit" validate:"omitempty,min=1,max=50"`
}

type FriendRequestsResponse struct {
	Requests []FriendRequestResponse `json:"requests"`
	Total    int                     `json:"total"`
}

type MutualFriendsResponse struct {
	MutualFriends []UserInfo `json:"mutual_friends"`
	Total         int        `json:"total"`
}

type GetFriendActivityRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Type     string `json:"type" validate:"omitempty,oneof=study quiz progress achievement"`
	SortDesc bool   `json:"sort_desc"`
}

type PublicUserProfileResponse struct {
	User           UserInfo             `json:"user"`
	Stats          PublicUserStats      `json:"stats"`
	Relationship   UserRelationshipInfo `json:"relationship"`
	RecentActivity []FriendActivityInfo `json:"recent_activity"`
}

type PublicUserStats struct {
	StudyStreak       int       `json:"study_streak"`
	TotalStudyTime    int       `json:"total_study_time"`
	QuestionsAnswered int       `json:"questions_answered"`
	QuizzesCompleted  int       `json:"quizzes_completed"`
	AverageQuizScore  float64   `json:"average_quiz_score"`
	BadgesEarned      int       `json:"badges_earned"`
	GlobalRank        *int      `json:"global_rank"`
	JoinedAt          time.Time `json:"joined_at"`
}

type UserRelationshipInfo struct {
	IsFriend         bool       `json:"is_friend"`
	IsFollowing      bool       `json:"is_following"`
	IsFollower       bool       `json:"is_follower"`
	IsBlocked        bool       `json:"is_blocked"`
	FriendshipStatus *string    `json:"friendship_status"` // pending, accepted, rejected
	FriendshipDate   *time.Time `json:"friendship_date"`
	FollowDate       *time.Time `json:"follow_date"`
}

type SocialStatsResponse struct {
	UserID uuid.UUID `json:"user_id"`

	// Friend statistics
	FriendsCount           int `json:"friends_count"`
	FriendRequestsSent     int `json:"friend_requests_sent"`
	FriendRequestsReceived int `json:"friend_requests_received"`

	// Follow statistics
	FollowersCount int `json:"followers_count"`
	FollowingCount int `json:"following_count"`

	// Activity statistics
	TimelineEntriesCount int `json:"timeline_entries_count"`
	LikesGiven           int `json:"likes_given"`
	LikesReceived        int `json:"likes_received"`
	CommentsGiven        int `json:"comments_given"`
	CommentsReceived     int `json:"comments_received"`

	// Recent activity
	RecentFriends   []UserInfo `json:"recent_friends"`
	RecentFollowers []UserInfo `json:"recent_followers"`
	ActiveFriends   []UserInfo `json:"active_friends"`
}

type GetFriendshipHistoryRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Action   string `json:"action" validate:"omitempty,oneof=sent received accepted rejected removed"`
	SortDesc bool   `json:"sort_desc"`
}

type FriendshipHistoryResponse struct {
	History    []FriendshipHistoryEntry `json:"history"`
	Total      int                      `json:"total"`
	Page       int                      `json:"page"`
	Limit      int                      `json:"limit"`
	TotalPages int                      `json:"total_pages"`
}

type FriendshipHistoryEntry struct {
	ID        uuid.UUID `json:"id"`
	Action    string    `json:"action"` // sent, received, accepted, rejected, removed
	User      UserInfo  `json:"user"`
	CreatedAt time.Time `json:"created_at"`
}

type UpdatePrivacySettingsRequest struct {
	ProfileVisibility    *string `json:"profile_visibility" validate:"omitempty,oneof=public friends private"`
	ActivityVisibility   *string `json:"activity_visibility" validate:"omitempty,oneof=public friends private"`
	FriendListVisibility *string `json:"friend_list_visibility" validate:"omitempty,oneof=public friends private"`
	AllowFriendRequests  *bool   `json:"allow_friend_requests"`
	AllowFollows         *bool   `json:"allow_follows"`
	ShowOnlineStatus     *bool   `json:"show_online_status"`
}

type PrivacySettingsResponse struct {
	UserID               uuid.UUID `json:"user_id"`
	ProfileVisibility    string    `json:"profile_visibility"`
	ActivityVisibility   string    `json:"activity_visibility"`
	FriendListVisibility string    `json:"friend_list_visibility"`
	AllowFriendRequests  bool      `json:"allow_friend_requests"`
	AllowFollows         bool      `json:"allow_follows"`
	ShowOnlineStatus     bool      `json:"show_online_status"`
	UpdatedAt            time.Time `json:"updated_at"`
}
