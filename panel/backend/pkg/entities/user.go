package entities

import (
	"time"

	"github.com/google/uuid"
)

type User struct {
	Base
	Username        string     `json:"username" gorm:"uniqueIndex;not null"`
	Email           *string    `json:"email" gorm:"uniqueIndex"`
	Phone           *string    `json:"phone" gorm:"uniqueIndex"`
	Password        string     `json:"-" gorm:"not null"`
	Name            string     `json:"name" gorm:"not null"`
	Bio             *string    `json:"bio"`
	ProfileImageURL *string    `json:"profile_image_url"`
	TargetKPSSYear  *int       `json:"target_kpss_year"`
	StudyArea       *string    `json:"study_area"` // Eğitim <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, ÖABT
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	IsVerified      bool       `json:"is_verified" gorm:"default:false"`
	IsAdmin         bool       `json:"is_admin" gorm:"default:false"`
	LastLoginAt     *time.Time `json:"last_login_at"`

	// Social login fields
	GoogleID *string `json:"-" gorm:"uniqueIndex"`
	AppleID  *string `json:"-" gorm:"uniqueIndex"`

	// Notification settings
	PushNotificationEnabled  bool `json:"push_notification_enabled" gorm:"default:true"`
	EmailNotificationEnabled bool `json:"email_notification_enabled" gorm:"default:true"`
}

// UserBlock represents a user blocking another user
type UserBlock struct {
	Base
	BlockerID *uuid.UUID `json:"blocker_id"` // Optional blocker user reference
	BlockedID *uuid.UUID `json:"blocked_id"` // Optional blocked user reference
}
