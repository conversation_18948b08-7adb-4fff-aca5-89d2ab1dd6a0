package subject

import (
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	GetAll() ([]entities.Subject, error)
	GetByAreaID(areaID uuid.UUID) ([]entities.Subject, error)
	GetByID(id uuid.UUID) (*entities.Subject, error)
	Create(subject *entities.Subject) error
	Update(subject *entities.Subject) error
	Delete(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetAll() ([]entities.Subject, error) {
	var subjects []entities.Subject
    err := r.db.Order("name ASC, created_at DESC").Find(&subjects).Error
	return subjects, err
}

func (r *repository) GetByAreaID(areaID uuid.UUID) ([]entities.Subject, error) {
	var subjects []entities.Subject
    err := r.db.Where("area_id = ?", areaID).Order("name ASC, created_at DESC").Find(&subjects).Error
	return subjects, err
}

func (r *repository) GetByID(id uuid.UUID) (*entities.Subject, error) {
	var subject entities.Subject
    err := r.db.First(&subject, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &subject, nil
}

func (r *repository) Create(subject *entities.Subject) error {
	return r.db.Create(subject).Error
}

func (r *repository) Update(subject *entities.Subject) error {
	return r.db.Save(subject).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Subject{}, "id = ?", id).Error
}
