package routes

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/database"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/question"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

func QuestionRoutes(r *gin.RouterGroup, questionService question.Service) {
	questions := r.Group("/questions")
	{
		questions.GET("", GetQuestions(questionService))
		questions.GET("/:id", GetQuestion(questionService))
		questions.POST("", CreateQuestionSafe())
		questions.PUT("/:id", UpdateQuestionSafe())
		questions.DELETE("/:id", DeleteQuestion(questionService))
	}
}

func GetQuestions(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		limitStr := c.<PERSON>fault<PERSON><PERSON><PERSON>("limit", "20")
		offsetStr := c.<PERSON><PERSON>ult<PERSON><PERSON><PERSON>("offset", "0")
		search := c.Query("q")
		quizIDStr := c.Query("quiz_id")

		limit, _ := strconv.Atoi(limitStr)
		offset, _ := strconv.Atoi(offsetStr)

		if quizIDStr != "" {
			quizID, err := uuid.Parse(quizIDStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz_id"})
				return
			}
			questions, err := s.GetByQuizID(quizID)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"data": questions, "total": len(questions)})
			return
		}

		questions, total, err := s.GetAll(limit, offset, search)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  questions,
			"total": total,
		})
	}
}

func GetQuestion(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		question, err := s.GetByID(id)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
			return
		}

		c.JSON(http.StatusOK, question)
	}
}

func CreateQuestionSafe() gin.HandlerFunc {
	return func(c *gin.Context) {
		var requestBody struct {
			Text          string  `json:"text"`
			QuestionType  string  `json:"question_type"`
			QuizID        *string `json:"quiz_id"`
			OptionA       string  `json:"option_a"`
			OptionB       string  `json:"option_b"`
			OptionC       string  `json:"option_c"`
			OptionD       string  `json:"option_d"`
			OptionE       string  `json:"option_e"`
			CorrectAnswer string  `json:"correct_answer"`
			Explanation   string  `json:"explanation"`
			Subject       string  `json:"subject"`
			TopicID       string  `json:"topic_id"`   // Ignore this field
			ContentID     string  `json:"content_id"` // Ignore this field
		}

		if err := c.ShouldBindBodyWithJSON(&requestBody); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if strings.TrimSpace(requestBody.Text) == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "text is required"})
			return
		}

		// Create question entity
		question := entities.Question{
			Base:          entities.Base{ID: uuid.New()},
			Text:          requestBody.Text,
			OptionA:       requestBody.OptionA,
			OptionB:       requestBody.OptionB,
			OptionC:       requestBody.OptionC,
			OptionD:       requestBody.OptionD,
			CorrectAnswer: requestBody.CorrectAnswer,
		}

		// Set optional quiz ID if provided
		if requestBody.QuizID != nil && *requestBody.QuizID != "" {
			quizID, err := uuid.Parse(*requestBody.QuizID)
			if err == nil {
				question.QuizID = &quizID
			}
		}

		// Set optional fields
		if requestBody.OptionE != "" {
			question.OptionE = &requestBody.OptionE
		}
		if requestBody.Explanation != "" {
			question.Explanation = &requestBody.Explanation
		}
		if requestBody.Subject != "" {
			question.Subject = &requestBody.Subject
		}

		// Validate based on question type
		qtype := strings.TrimSpace(requestBody.QuestionType)
		switch qtype {
		case "multiple_choice":
			// Require A-D and correct_answer
			if requestBody.OptionA == "" || strings.TrimSpace(requestBody.OptionA) == "" ||
				requestBody.OptionB == "" || strings.TrimSpace(requestBody.OptionB) == "" ||
				requestBody.OptionC == "" || strings.TrimSpace(requestBody.OptionC) == "" ||
				requestBody.OptionD == "" || strings.TrimSpace(requestBody.OptionD) == "" {
				c.JSON(http.StatusBadRequest, gin.H{"error": "option_a, option_b, option_c, option_d are required"})
				return
			}
			ca := strings.TrimSpace(requestBody.CorrectAnswer)
			if ca == "" || (ca != "A" && ca != "B" && ca != "C" && ca != "D" && ca != "E") {
				c.JSON(http.StatusBadRequest, gin.H{"error": "correct_answer must be A, B, C, D, or E"})
				return
			}

		case "true_false":
			// Accept true/false format from frontend
			ca := strings.TrimSpace(requestBody.CorrectAnswer)
			if ca != "true" && ca != "false" {
				c.JSON(http.StatusBadRequest, gin.H{"error": "correct_answer must be 'true' or 'false'"})
				return
			}
			// Convert true/false to A/B for database storage
			switch ca {
			case "true":
				question.CorrectAnswer = "A"
			case "false":
				question.CorrectAnswer = "B"
			}
			// Set standard true/false options
			question.OptionA = "Doğru"
			question.OptionB = "Yanlış"
			question.OptionC = ""
			question.OptionD = ""

		case "fill_blank", "essay", "":
			// No specific validation for these types
		default:
			// Unknown type - accept minimal validation
		}

		// Save to database
		if err := database.DB().Create(&question).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusCreated, question)
	}
}

func UpdateQuestionSafe() gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		var requestBody struct {
			Text          string  `json:"text"`
			QuestionType  string  `json:"question_type"`
			QuizID        *string `json:"quiz_id"`
			OptionA       string  `json:"option_a"`
			OptionB       string  `json:"option_b"`
			OptionC       string  `json:"option_c"`
			OptionD       string  `json:"option_d"`
			OptionE       string  `json:"option_e"`
			CorrectAnswer string  `json:"correct_answer"`
			Explanation   string  `json:"explanation"`
			Subject       string  `json:"subject"`
			TopicID       string  `json:"topic_id"`   // Ignore this field
			ContentID     string  `json:"content_id"` // Ignore this field
		}

		if err := c.ShouldBindBodyWithJSON(&requestBody); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Get existing question
		var question entities.Question
		if err := database.DB().Where("id = ?", id).First(&question).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
			return
		}

		// Update fields
		if strings.TrimSpace(requestBody.Text) != "" {
			question.Text = requestBody.Text
		}
		if requestBody.QuizID != nil && *requestBody.QuizID != "" {
			quizID, err := uuid.Parse(*requestBody.QuizID)
			if err == nil {
				question.QuizID = &quizID
			}
		}
		if requestBody.OptionA != "" {
			question.OptionA = requestBody.OptionA
		}
		if requestBody.OptionB != "" {
			question.OptionB = requestBody.OptionB
		}
		if requestBody.OptionC != "" {
			question.OptionC = requestBody.OptionC
		}
		if requestBody.OptionD != "" {
			question.OptionD = requestBody.OptionD
		}
		if requestBody.OptionE != "" {
			question.OptionE = &requestBody.OptionE
		}
		if requestBody.CorrectAnswer != "" {
			// Handle true/false questions specially
			if requestBody.QuestionType == "true_false" {
				ca := strings.TrimSpace(requestBody.CorrectAnswer)
				switch ca {
				case "true":
					question.CorrectAnswer = "A"
					question.OptionA = "Doğru"
					question.OptionB = "Yanlış"
					question.OptionC = ""
					question.OptionD = ""
				case "false":
					question.CorrectAnswer = "B"
					question.OptionA = "Doğru"
					question.OptionB = "Yanlış"
					question.OptionC = ""
					question.OptionD = ""
				default:
					question.CorrectAnswer = requestBody.CorrectAnswer
				}
			} else {
				question.CorrectAnswer = requestBody.CorrectAnswer
			}
		}
		if requestBody.Explanation != "" {
			question.Explanation = &requestBody.Explanation
		}
		if requestBody.Subject != "" {
			question.Subject = &requestBody.Subject
		}

		// Save to database
		if err := database.DB().Save(&question).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, question)
	}
}

func DeleteQuestion(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		if err := s.Delete(id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Question deleted successfully"})
	}
}
