import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  BookOpen, 
  Clock, 
  Users, 
  Star, 
  Play,
  Heart,
  Share2,
  Trophy,
  Target,
  TrendingUp,
  ArrowLeft,
  Award
} from 'lucide-react';
import { Quiz, QuizLeaderboard, QuizStatistics } from '../../types/quiz';
import { quizService } from '../../services/quizService';
import { quizSessionService } from '../../services/quizSessionService';
import { useToast } from '../common/Toast';

const QuizDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { showError, showSuccess } = useToast();

  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [leaderboard, setLeaderboard] = useState<QuizLeaderboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [startingQuiz, setStartingQuiz] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'leaderboard' | 'stats'>('overview');

  useEffect(() => {
    if (id) {
      loadQuizData();
    }
  }, [id]);

  const loadQuizData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use actual API calls instead of mock data
      const [quizResponse, leaderboardResponse] = await Promise.all([
        quizService.getQuiz(id!),
        quizService.getQuizLeaderboard(id!)
      ]);

      if (quizResponse.success && quizResponse.data) {
        setQuiz(quizResponse.data);
      }

      if (leaderboardResponse.success && leaderboardResponse.data) {
        setLeaderboard(leaderboardResponse.data);
      }

    } catch (err) {
      const errorMsg = 'Backend service unavailable';
      setError(errorMsg);
      showError('Service Error', errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const handleStartQuiz = async () => {
    if (!quiz) return;

    try {
      setStartingQuiz(true);

      // Start quiz session using actual API
      const response = await quizSessionService.startQuizSession(quiz.id);

      if (response.success && response.data) {
        navigate(`/quizzes/${quiz.id}/session/${response.data.id}`);
      } else {
        throw new Error('Failed to start quiz session');
      }

    } catch (err) {
      const errorMsg = 'Failed to start quiz session';
      setError(errorMsg);
      showError('Session Error', errorMsg);
    } finally {
      setStartingQuiz(false);
    }
  };

  const handleToggleFavorite = async () => {
    if (!quiz) return;

    try {
      if (quiz.is_favorite) {
        await quizService.removeFromFavorites(quiz.id);
      } else {
        await quizService.addToFavorites(quiz.id);
      }
      
      setQuiz(prev => prev ? { ...prev, is_favorite: !prev.is_favorite } : null);
    } catch (err) {
      console.error('Failed to toggle favorite:', err);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'hard': return 'text-red-400 bg-red-400/10 border-red-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} minutes`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error || !quiz) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-2">Error loading quiz</div>
        <p className="text-dark-400">{error}</p>
        <button
          onClick={loadQuizData}
          className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <button
        onClick={() => navigate('/quizzes')}
        className="flex items-center gap-2 text-dark-300 hover:text-white transition-colors"
      >
        <ArrowLeft size={20} />
        Back to Quizzes
      </button>

      {/* Quiz Header */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <h1 className="text-3xl font-bold text-white">{quiz.title}</h1>
              {quiz.is_featured && (
                <Star className="text-yellow-400" size={24} />
              )}
            </div>
            
            <p className="text-dark-300 text-lg mb-4">{quiz.description}</p>
            
            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getDifficultyColor(quiz.difficulty)}`}>
                {quiz.difficulty}
              </span>
              <span className="px-3 py-1 bg-dark-700 text-dark-300 rounded-full text-sm border border-dark-600">
                {quiz.subject}
              </span>
              {quiz.tags.map((tag, index) => (
                <span key={index} className="px-3 py-1 bg-dark-700 text-dark-300 rounded-full text-sm border border-dark-600">
                  {tag}
                </span>
              ))}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2 text-dark-300">
                <BookOpen size={20} className="text-primary-400" />
                <div>
                  <div className="text-white font-semibold">{quiz.question_count}</div>
                  <div className="text-sm">Questions</div>
                </div>
              </div>
              <div className="flex items-center gap-2 text-dark-300">
                <Clock size={20} className="text-primary-400" />
                <div>
                  <div className="text-white font-semibold">{formatDuration(quiz.time_limit)}</div>
                  <div className="text-sm">Duration</div>
                </div>
              </div>
              <div className="flex items-center gap-2 text-dark-300">
                <Users size={20} className="text-primary-400" />
                <div>
                  <div className="text-white font-semibold">{quiz.statistics.total_attempts}</div>
                  <div className="text-sm">Attempts</div>
                </div>
              </div>
              <div className="flex items-center gap-2 text-dark-300">
                <Target size={20} className="text-primary-400" />
                <div>
                  <div className="text-white font-semibold">{quiz.statistics.average_score}%</div>
                  <div className="text-sm">Avg Score</div>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-3 lg:w-48">
            <button
              onClick={handleStartQuiz}
              disabled={startingQuiz}
              className="flex items-center justify-center gap-2 px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white rounded-lg transition-colors font-medium"
            >
              {startingQuiz ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <>
                  <Play size={20} />
                  {t('quiz.startQuiz')}
                </>
              )}
            </button>
            
            <div className="flex gap-2">
              <button
                onClick={handleToggleFavorite}
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  quiz.is_favorite
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-dark-700 hover:bg-dark-600 text-dark-300 hover:text-white'
                }`}
              >
                <Heart size={16} fill={quiz.is_favorite ? 'currentColor' : 'none'} />
              </button>
              
              <button className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-dark-700 hover:bg-dark-600 text-dark-300 hover:text-white rounded-lg transition-colors">
                <Share2 size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
        {[
          { key: 'overview', label: 'Overview', icon: BookOpen },
          { key: 'leaderboard', label: 'Leaderboard', icon: Trophy },
          { key: 'stats', label: 'Statistics', icon: TrendingUp }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as typeof activeTab)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === key
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:text-white hover:bg-dark-700'
            }`}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">About this Quiz</h3>
              <p className="text-dark-300">{quiz.description}</p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Creator</h3>
              <div className="flex items-center gap-3">
                {quiz.creator_avatar && (
                  <img
                    src={quiz.creator_avatar}
                    alt={quiz.creator_name}
                    className="w-10 h-10 rounded-full"
                  />
                )}
                <div>
                  <div className="text-white font-medium">{quiz.creator_name}</div>
                  <div className="text-dark-400 text-sm">Created {new Date(quiz.created_at).toLocaleDateString()}</div>
                </div>
              </div>
            </div>

            {quiz.user_attempts && quiz.user_attempts > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Your Performance</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">{quiz.user_attempts}</div>
                    <div className="text-dark-400 text-sm">Attempts</div>
                  </div>
                  <div className="bg-dark-700 rounded-lg p-4">
                    <div className="text-2xl font-bold text-primary-400">{quiz.best_score}%</div>
                    <div className="text-dark-400 text-sm">Best Score</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'leaderboard' && leaderboard && (
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Top Performers</h3>
            <div className="space-y-3">
              {leaderboard.entries.map((entry, index) => (
                <div
                  key={entry.user_id}
                  className={`flex items-center justify-between p-4 rounded-lg ${
                    entry.is_current_user ? 'bg-primary-600/20 border border-primary-500/30' : 'bg-dark-700'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                      index === 0 ? 'bg-yellow-500 text-black' :
                      index === 1 ? 'bg-gray-400 text-black' :
                      index === 2 ? 'bg-orange-500 text-black' :
                      'bg-dark-600 text-white'
                    }`}>
                      {entry.rank}
                    </div>
                    {entry.avatar && (
                      <img src={entry.avatar} alt={entry.username} className="w-8 h-8 rounded-full" />
                    )}
                    <div>
                      <div className="text-white font-medium">{entry.username}</div>
                      <div className="text-dark-400 text-sm">
                        {new Date(entry.completed_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-bold">{entry.percentage}%</div>
                    <div className="text-dark-400 text-sm">{Math.floor(entry.time_taken / 60)}m</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'stats' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Users className="text-primary-400" size={20} />
                <span className="text-white font-medium">Total Attempts</span>
              </div>
              <div className="text-2xl font-bold text-white">{quiz.statistics.total_attempts}</div>
            </div>
            
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Target className="text-primary-400" size={20} />
                <span className="text-white font-medium">Average Score</span>
              </div>
              <div className="text-2xl font-bold text-white">{quiz.statistics.average_score}%</div>
            </div>
            
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="text-primary-400" size={20} />
                <span className="text-white font-medium">Avg Time</span>
              </div>
              <div className="text-2xl font-bold text-white">{quiz.statistics.average_time}m</div>
            </div>
            
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Award className="text-primary-400" size={20} />
                <span className="text-white font-medium">Completion Rate</span>
              </div>
              <div className="text-2xl font-bold text-white">{quiz.statistics.completion_rate}%</div>
            </div>
            
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Star className="text-primary-400" size={20} />
                <span className="text-white font-medium">Difficulty</span>
              </div>
              <div className="text-2xl font-bold text-white">{quiz.statistics.difficulty_rating}/5</div>
            </div>
            
            <div className="bg-dark-700 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="text-primary-400" size={20} />
                <span className="text-white font-medium">Popularity</span>
              </div>
              <div className="text-2xl font-bold text-white">{quiz.statistics.popularity_score}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuizDetail;
