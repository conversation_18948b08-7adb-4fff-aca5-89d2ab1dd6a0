// Import all theme modules
import {
  colors,
  getColorWithOpacity,
  getDifficultyColor,
  getBadgeColor,
} from './colors';
import {
  typography,
  fontWeights,
  fontSizes,
  lineHeights,
  createTextStyle,
} from './typography';
import {spacing, layout, breakpoints, safeArea} from './spacing';

// Export all theme modules
export {colors, getColorWithOpacity, getDifficultyColor, getBadgeColor};
export {typography, fontWeights, fontSizes, lineHeights, createTextStyle};
export {spacing, layout, breakpoints, safeArea};

// Theme object combining all theme modules
export const theme = {
  colors,
  typography,
  spacing,
  layout,
  breakpoints,
  safeArea,
} as const;

// Theme type for TypeScript
export type Theme = typeof theme;
