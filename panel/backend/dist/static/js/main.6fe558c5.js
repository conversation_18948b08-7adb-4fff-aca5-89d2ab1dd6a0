/*! For license information please see main.6fe558c5.js.LICENSE.txt */
(()=>{var e={43:(e,t,n)=>{"use strict";e.exports=n(202)},153:(e,t,n)=>{"use strict";var r=n(43),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!l.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:o,_owner:s.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,m(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},j={key:!0,ref:!0,__self:!0,__source:!0};function N(e,t,r){var a,o={},i=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(i=""+t.key),t)k.call(t,a)&&!j.hasOwnProperty(a)&&(o[a]=t[a]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(a in l=e.defaultProps)void 0===o[a]&&(o[a]=l[a]);return{$$typeof:n,type:e,key:i,ref:s,props:o,_owner:S.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var O=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function C(e,t,a,o,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return i=i(l=e),e=""===o?"."+P(l,0):o,w(i)?(a="",null!=e&&(a=e.replace(O,"$&/")+"/"),C(i,t,a,"",function(e){return e})):null!=i&&(E(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,a+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(O,"$&/")+"/")+e)),t.push(i)),1;if(l=0,o=""===o?".":o+":",w(e))for(var u=0;u<e.length;u++){var c=o+P(s=e[u],u);l+=C(s,t,a,c,i)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=C(s=s.value,t,a,c=o+P(s,u++),i);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function _(e,t,n){if(null==e)return e;var r=[],a=0;return C(e,r,"","",function(e){return t.call(n,e,a++)}),r}function T(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},A={transition:null},L={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:A,ReactCurrentOwner:S};function z(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:_,forEach:function(e,t,n){_(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return _(e,function(){t++}),t},toArray:function(e){return _(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.act=z,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),o=e.key,i=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,s=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)k.call(t,u)&&!j.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];a.children=l}return{$$typeof:n,type:e.type,key:o,ref:i,props:a,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=N,t.createFactory=function(e){var t=N.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=A.transition;A.transition={};try{e()}finally{A.transition=t}},t.unstable_act=z,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,n){return R.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,n){return R.current.useReducer(e,t,n)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return R.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>o(l,n))u<a&&0>o(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,A(k);else{var t=r(c);null!==t&&L(w,t.startTime-e)}}function k(e,n){m=!1,g&&(g=!1,y(E),E=-1),h=!0;var o=p;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!C());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var s=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&a(u),x(n)}else a(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&L(w,d.startTime-n),l=!1}return l}finally{f=null,p=o,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,j=!1,N=null,E=-1,O=5,P=-1;function C(){return!(t.unstable_now()-P<O)}function _(){if(null!==N){var e=t.unstable_now();P=e;var n=!0;try{n=N(!0,e)}finally{n?S():(j=!1,N=null)}}else j=!1}if("function"===typeof b)S=function(){b(_)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,R=T.port2;T.port1.onmessage=_,S=function(){R.postMessage(null)}}else S=function(){v(_,0)};function A(e){N=e,j||(j=!0,S())}function L(e,n){E=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,A(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>i?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(g?(y(E),E=-1):g=!0,L(w,o-i))):(e.sortIndex=s,n(u,e),m||h||(m=!0,A(k))),e},t.unstable_shouldYield=C,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{"use strict";var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{"use strict";e.exports=n(153)},730:(e,t,n)=>{"use strict";var r=n(43),a=n(853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,o,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),O=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),T=Symbol.for("react.memo"),R=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var A=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var L=Symbol.iterator;function z(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=L&&e[L]||e["@@iterator"])?e:null}var D,F=Object.assign;function I(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var U=!1;function M(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),o=r.stack.split("\n"),i=a.length-1,s=o.length-1;1<=i&&0<=s&&a[i]!==o[s];)s--;for(;1<=i&&0<=s;i--,s--)if(a[i]!==o[s]){if(1!==i||1!==s)do{if(i--,0>--s||a[i]!==o[s]){var l="\n"+a[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=i&&0<=s);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?I(e):""}function B(e){switch(e.tag){case 5:return I(e.type);case 16:return I("Lazy");case 13:return I("Suspense");case 19:return I("SuspenseList");case 0:case 2:case 15:return e=M(e.type,!1);case 11:return e=M(e.type.render,!1);case 1:return e=M(e.type,!0);default:return""}}function q(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case N:return"Profiler";case j:return"StrictMode";case C:return"Suspense";case _:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case O:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case T:return null!==(t=e.displayName||null)?t:q(e.type)||"Memo";case R:t=e._payload,e=e._init;try{return q(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return q(t);case 8:return t===j?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function W(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=W(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=W(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function $(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function J(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function X(e,t){Y(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&$(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function oe(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){he.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ve=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,je=null;function Ne(e){if(e=ba(e)){if("function"!==typeof ke)throw Error(o(280));var t=e.stateNode;t&&(t=wa(t),ke(e.stateNode,e.type,t))}}function Ee(e){Se?je?je.push(e):je=[e]:Se=e}function Oe(){if(Se){var e=Se,t=je;if(je=Se=null,Ne(e),t)for(e=0;e<t.length;e++)Ne(t[e])}}function Pe(e,t){return e(t)}function Ce(){}var _e=!1;function Te(e,t,n){if(_e)return e(t,n);_e=!0;try{return Pe(e,t,n)}finally{_e=!1,(null!==Se||null!==je)&&(Ce(),Oe())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Ae=!1;if(c)try{var Le={};Object.defineProperty(Le,"passive",{get:function(){Ae=!0}}),window.addEventListener("test",Le,Le),window.removeEventListener("test",Le,Le)}catch(ce){Ae=!1}function ze(e,t,n,r,a,o,i,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var De=!1,Fe=null,Ie=!1,Ue=null,Me={onError:function(e){De=!0,Fe=e}};function Be(e,t,n,r,a,o,i,s,l){De=!1,Fe=null,ze.apply(Me,arguments)}function qe(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(qe(e)!==e)throw Error(o(188))}function We(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=qe(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return Ve(a),e;if(i===r)return Ve(a),t;i=i.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=i;else{for(var s=!1,l=a.child;l;){if(l===n){s=!0,n=a,r=i;break}if(l===r){s=!0,r=a,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=a;break}if(l===r){s=!0,r=i,n=a;break}l=l.sibling}if(!s)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?Qe(e):null}function Qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Qe(e);if(null!==t)return t;e=e.sibling}return null}var Ke=a.unstable_scheduleCallback,$e=a.unstable_cancelCallback,Ge=a.unstable_shouldYield,Je=a.unstable_requestPaint,Ye=a.unstable_now,Xe=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,ot=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,i=268435455&n;if(0!==i){var s=i&~a;0!==s?r=dt(s):0!==(o&=i)&&(r=dt(o))}else 0!==(i=n&~a)?r=dt(i):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-it(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,kt,St,jt,Nt,Et=!1,Ot=[],Pt=null,Ct=null,_t=null,Tt=new Map,Rt=new Map,At=[],Lt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":Ct=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":Tt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Dt(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Ft(e){var t=ya(e.target);if(null!==t){var n=qe(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Nt(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function It(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Ut(e,t,n){It(e)&&n.delete(t)}function Mt(){Et=!1,null!==Pt&&It(Pt)&&(Pt=null),null!==Ct&&It(Ct)&&(Ct=null),null!==_t&&It(_t)&&(_t=null),Tt.forEach(Ut),Rt.forEach(Ut)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Et||(Et=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Mt)))}function qt(e){function t(t){return Bt(t,e)}if(0<Ot.length){Bt(Ot[0],e);for(var n=1;n<Ot.length;n++){var r=Ot[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&Bt(Pt,e),null!==Ct&&Bt(Ct,e),null!==_t&&Bt(_t,e),Tt.forEach(t),Rt.forEach(t),n=0;n<At.length;n++)(r=At[n]).blockedOn===e&&(r.blockedOn=null);for(;0<At.length&&null===(n=At[0]).blockedOn;)Ft(n),null===n.blockedOn&&At.shift()}var Ht=x.ReactCurrentBatchConfig,Vt=!0;function Wt(e,t,n,r){var a=bt,o=Ht.transition;Ht.transition=null;try{bt=1,Kt(e,t,n,r)}finally{bt=a,Ht.transition=o}}function Qt(e,t,n,r){var a=bt,o=Ht.transition;Ht.transition=null;try{bt=4,Kt(e,t,n,r)}finally{bt=a,Ht.transition=o}}function Kt(e,t,n,r){if(Vt){var a=Gt(e,t,n,r);if(null===a)Vr(e,t,r,$t,n),zt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Pt=Dt(Pt,e,t,n,r,a),!0;case"dragenter":return Ct=Dt(Ct,e,t,n,r,a),!0;case"mouseover":return _t=Dt(_t,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return Tt.set(o,Dt(Tt.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Rt.set(o,Dt(Rt.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(zt(e,r),4&t&&-1<Lt.indexOf(e)){for(;null!==a;){var o=ba(a);if(null!==o&&wt(o),null===(o=Gt(e,t,n,r))&&Vr(e,t,r,$t,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Vr(e,t,r,null,n)}}var $t=null;function Gt(e,t,n,r){if($t=null,null!==(e=ya(e=we(r))))if(null===(t=qe(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return $t=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xe()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Xt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Xt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=F({},un,{view:0,detail:0}),fn=an(dn),pn=F({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Nn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(on=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=on=0,ln=e),on)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),hn=an(pn),mn=an(F({},pn,{dataTransfer:0})),gn=an(F({},dn,{relatedTarget:0})),vn=an(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),xn=an(F({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function Nn(){return jn}var En=F({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Nn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),On=an(En),Pn=an(F({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Cn=an(F({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Nn})),_n=an(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Tn=F({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=an(Tn),An=[9,13,27,32],Ln=c&&"CompositionEvent"in window,zn=null;c&&"documentMode"in document&&(zn=document.documentMode);var Dn=c&&"TextEvent"in window&&!zn,Fn=c&&(!Ln||zn&&8<zn&&11>=zn),In=String.fromCharCode(32),Un=!1;function Mn(e,t){switch(e){case"keyup":return-1!==An.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var qn=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function Wn(e,t,n,r){Ee(r),0<(t=Qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,Kn=null;function $n(e){Ir(e,0)}function Gn(e){if(K(xa(e)))return e}function Jn(e,t){if("change"===e)return t}var Yn=!1;if(c){var Xn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Xn=Zn}else Xn=!1;Yn=Xn&&(!document.documentMode||9<document.documentMode)}function tr(){Qn&&(Qn.detachEvent("onpropertychange",nr),Kn=Qn=null)}function nr(e){if("value"===e.propertyName&&Gn(Kn)){var t=[];Wn(t,Kn,e,we(e)),Te($n,t)}}function rr(e,t,n){"focusin"===e?(tr(),Kn=n,(Qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(Kn)}function or(e,t){if("click"===e)return Gn(t)}function ir(e,t){if("input"===e||"change"===e)return Gn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!sr(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=$();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=$((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=cr(n,o);var i=cr(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==$(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&lr(yr,r)||(yr=r,0<(r=Qr(vr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Sr={},jr={};function Nr(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in jr)return Sr[e]=n[t];return e}c&&(jr=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Er=Nr("animationend"),Or=Nr("animationiteration"),Pr=Nr("animationstart"),Cr=Nr("transitionend"),_r=new Map,Tr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){_r.set(e,t),l(t,[e])}for(var Ar=0;Ar<Tr.length;Ar++){var Lr=Tr[Ar];Rr(Lr.toLowerCase(),"on"+(Lr[0].toUpperCase()+Lr.slice(1)))}Rr(Er,"onAnimationEnd"),Rr(Or,"onAnimationIteration"),Rr(Pr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Cr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,i,s,l,u){if(Be.apply(this,arguments),De){if(!De)throw Error(o(198));var c=Fe;De=!1,Fe=null,Ie||(Ie=!0,Ue=c)}}(r,t,void 0,e),e.currentTarget=null}function Ir(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&a.isPropagationStopped())break e;Fr(a,s,u),o=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,u=s.currentTarget,s=s.listener,l!==o&&a.isPropagationStopped())break e;Fr(a,s,u),o=l}}}if(Ie)throw e=Ue,Ie=!1,Ue=null,e}function Ur(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Mr(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function qr(e){if(!e[Br]){e[Br]=!0,i.forEach(function(t){"selectionchange"!==t&&(Dr.has(t)||Mr(t,!1,e),Mr(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Mr("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Jt(t)){case 1:var a=Wt;break;case 4:a=Qt;break;default:a=Kt}n=a.bind(null,t,n,e),a=void 0,!Ae||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Vr(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===a||8===s.nodeType&&s.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var l=i.tag;if((3===l||4===l)&&((l=i.stateNode.containerInfo)===a||8===l.nodeType&&l.parentNode===a))return;i=i.return}for(;null!==s;){if(null===(i=ya(s)))return;if(5===(l=i.tag)||6===l){r=o=i;continue e}s=s.parentNode}}r=r.return}Te(function(){var r=o,a=we(n),i=[];e:{var s=_r.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=On;break;case"focusin":u="focus",l=gn;break;case"focusout":u="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Cn;break;case Er:case Or:case Pr:l=vn;break;case Cr:l=_n;break;case"scroll":l=fn;break;case"wheel":l=Rn;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Pn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Re(h,f))&&c.push(Wr(h,m,p)))),d)break;h=h.return}0<c.length&&(s=new l(s,u,null,n,a),i.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!ya(u)&&!u[ha])&&(l||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?ya(u):null)&&(u!==(d=qe(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Pn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==l?s:xa(l),p=null==u?s:xa(u),(s=new c(m,h+"leave",l,n,a)).target=d,s.relatedTarget=p,m=null,ya(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,h=0,p=c=l;p;p=Kr(p))h++;for(p=0,m=f;m;m=Kr(m))p++;for(;0<h-p;)c=Kr(c),h--;for(;0<p-h;)f=Kr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Kr(c),f=Kr(f)}c=null}else c=null;null!==l&&$r(i,s,l,c,!1),null!==u&&null!==d&&$r(i,d,u,c,!0)}if("select"===(l=(s=r?xa(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Jn;else if(Vn(s))if(Yn)g=ir;else{g=ar;var v=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=or);switch(g&&(g=g(e,r))?Wn(i,g,n,a):(v&&v(e,s,r),"focusout"===e&&(v=s._wrapperState)&&v.controlled&&"number"===s.type&&ee(s,"number",s.value)),v=r?xa(r):window,e){case"focusin":(Vn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(i,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(i,n,a)}var y;if(Ln)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else qn?Mn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(qn||"onCompositionStart"!==b?"onCompositionEnd"===b&&qn&&(y=en()):(Xt="value"in(Yt=a)?Yt.value:Yt.textContent,qn=!0)),0<(v=Qr(r,b)).length&&(b=new xn(b,e,null,n,a),i.push({event:b,listeners:v}),y?b.data=y:null!==(y=Bn(n))&&(b.data=y))),(y=Dn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(Un=!0,In);case"textInput":return(e=t.data)===In&&Un?null:e;default:return null}}(e,n):function(e,t){if(qn)return"compositionend"===e||!Ln&&Mn(e,t)?(e=en(),Zt=Xt=Yt=null,qn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Qr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=y))}Ir(i,t)})}function Wr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Re(e,n))&&r.unshift(Wr(e,o,a)),null!=(o=Re(e,t))&&r.push(Wr(e,o,a))),e=e.return}return r}function Kr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function $r(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,a?null!=(l=Re(n,o))&&i.unshift(Wr(n,l,s)):a||null!=(l=Re(n,o))&&i.push(Wr(n,l,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Gr=/\r\n?/g,Jr=/\u0000|\uFFFD/g;function Yr(e){return("string"===typeof e?e:""+e).replace(Gr,"\n").replace(Jr,"")}function Xr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(o(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,oa="function"===typeof Promise?Promise:void 0,ia="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oa?function(e){return oa.resolve(null).then(e).catch(sa)}:ra;function sa(e){setTimeout(function(){throw e})}function la(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void qt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);qt(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function wa(e){return e[pa]||null}var ka=[],Sa=-1;function ja(e){return{current:e}}function Na(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function Ea(e,t){Sa++,ka[Sa]=e.current,e.current=t}var Oa={},Pa=ja(Oa),Ca=ja(!1),_a=Oa;function Ta(e,t){var n=e.type.contextTypes;if(!n)return Oa;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ra(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Aa(){Na(Ca),Na(Pa)}function La(e,t,n){if(Pa.current!==Oa)throw Error(o(168));Ea(Pa,t),Ea(Ca,n)}function za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,H(e)||"Unknown",a));return F({},n,r)}function Da(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Oa,_a=Pa.current,Ea(Pa,e),Ea(Ca,Ca.current),!0}function Fa(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=za(e,t,_a),r.__reactInternalMemoizedMergedChildContext=e,Na(Ca),Na(Pa),Ea(Pa,e)):Na(Ca),Ea(Ca,n)}var Ia=null,Ua=!1,Ma=!1;function Ba(e){null===Ia?Ia=[e]:Ia.push(e)}function qa(){if(!Ma&&null!==Ia){Ma=!0;var e=0,t=bt;try{var n=Ia;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ia=null,Ua=!1}catch(a){throw null!==Ia&&(Ia=Ia.slice(e+1)),Ke(Ze,qa),a}finally{bt=t,Ma=!1}}return null}var Ha=[],Va=0,Wa=null,Qa=0,Ka=[],$a=0,Ga=null,Ja=1,Ya="";function Xa(e,t){Ha[Va++]=Qa,Ha[Va++]=Wa,Wa=e,Qa=t}function Za(e,t,n){Ka[$a++]=Ja,Ka[$a++]=Ya,Ka[$a++]=Ga,Ga=e;var r=Ja;e=Ya;var a=32-it(r)-1;r&=~(1<<a),n+=1;var o=32-it(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Ja=1<<32-it(t)+a|n<<a|r,Ya=o+e}else Ja=1<<o|n<<a|r,Ya=e}function eo(e){null!==e.return&&(Xa(e,1),Za(e,1,0))}function to(e){for(;e===Wa;)Wa=Ha[--Va],Ha[Va]=null,Qa=Ha[--Va],Ha[Va]=null;for(;e===Ga;)Ga=Ka[--$a],Ka[$a]=null,Ya=Ka[--$a],Ka[$a]=null,Ja=Ka[--$a],Ka[$a]=null}var no=null,ro=null,ao=!1,oo=null;function io(e,t){var n=Tu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function so(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ga?{id:Ja,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Tu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function lo(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function uo(e){if(ao){var t=ro;if(t){var n=t;if(!so(e,t)){if(lo(e))throw Error(o(418));t=ua(n.nextSibling);var r=no;t&&so(e,t)?io(r,n):(e.flags=-4097&e.flags|2,ao=!1,no=e)}}else{if(lo(e))throw Error(o(418));e.flags=-4097&e.flags|2,ao=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!ao)return co(e),ao=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ro)){if(lo(e))throw po(),Error(o(418));for(;t;)io(e,t),t=ua(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ua(e.stateNode.nextSibling):null;return!0}function po(){for(var e=ro;e;)e=ua(e.nextSibling)}function ho(){ro=no=null,ao=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var go=x.ReactCurrentBatchConfig;function vo(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=a.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function yo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function xo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Au(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===R&&bo(o)===t.type)?((r=a(t,n.props)).ref=vo(e,t,n),r.return=e,r):((r=Lu(n.type,n.key,n.props,null,e.mode,r)).ref=vo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Iu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=zu(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Lu(t.type,t.key,t.props,null,e.mode,n)).ref=vo(e,null,t),n.return=e,n;case k:return(t=Iu(t,e.mode,n)).return=e,t;case R:return f(e,(0,t._init)(t._payload),n)}if(te(t)||z(t))return(t=zu(t,e.mode,n,null)).return=e,t;yo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case R:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||z(n))return null!==a?null:d(e,t,n,r,null);yo(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case R:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||z(r))return d(t,e=e.get(n)||null,r,a,null);yo(t,r)}return null}function m(a,o,s,l){for(var u=null,c=null,d=o,m=o=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=p(a,d,s[m],l);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),o=i(v,o,m),null===c?u=v:c.sibling=v,c=v,d=g}if(m===s.length)return n(a,d),ao&&Xa(a,m),u;if(null===d){for(;m<s.length;m++)null!==(d=f(a,s[m],l))&&(o=i(d,o,m),null===c?u=d:c.sibling=d,c=d);return ao&&Xa(a,m),u}for(d=r(a,d);m<s.length;m++)null!==(g=h(d,a,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),o=i(g,o,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),ao&&Xa(a,m),u}function g(a,s,l,u){var c=z(l);if("function"!==typeof c)throw Error(o(150));if(null==(l=c.call(l)))throw Error(o(151));for(var d=c=null,m=s,g=s=0,v=null,y=l.next();null!==m&&!y.done;g++,y=l.next()){m.index>g?(v=m,m=null):v=m.sibling;var b=p(a,m,y.value,u);if(null===b){null===m&&(m=v);break}e&&m&&null===b.alternate&&t(a,m),s=i(b,s,g),null===d?c=b:d.sibling=b,d=b,m=v}if(y.done)return n(a,m),ao&&Xa(a,g),c;if(null===m){for(;!y.done;g++,y=l.next())null!==(y=f(a,y.value,u))&&(s=i(y,s,g),null===d?c=y:d.sibling=y,d=y);return ao&&Xa(a,g),c}for(m=r(a,m);!y.done;g++,y=l.next())null!==(y=h(m,a,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),s=i(y,s,g),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach(function(e){return t(a,e)}),ao&&Xa(a,g),c}return function e(r,o,i,l){if("object"===typeof i&&null!==i&&i.type===S&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case w:e:{for(var u=i.key,c=o;null!==c;){if(c.key===u){if((u=i.type)===S){if(7===c.tag){n(r,c.sibling),(o=a(c,i.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===R&&bo(u)===c.type){n(r,c.sibling),(o=a(c,i.props)).ref=vo(r,c,i),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===S?((o=zu(i.props.children,r.mode,l,i.key)).return=r,r=o):((l=Lu(i.type,i.key,i.props,null,r.mode,l)).ref=vo(r,o,i),l.return=r,r=l)}return s(r);case k:e:{for(c=i.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===i.containerInfo&&o.stateNode.implementation===i.implementation){n(r,o.sibling),(o=a(o,i.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Iu(i,r.mode,l)).return=r,r=o}return s(r);case R:return e(r,o,(c=i._init)(i._payload),l)}if(te(i))return m(r,o,i,l);if(z(i))return g(r,o,i,l);yo(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,i)).return=r,r=o):(n(r,o),(o=Fu(i,r.mode,l)).return=r,r=o),s(r)):n(r,o)}}var wo=xo(!0),ko=xo(!1),So=ja(null),jo=null,No=null,Eo=null;function Oo(){Eo=No=jo=null}function Po(e){var t=So.current;Na(So),e._currentValue=t}function Co(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function _o(e,t){jo=e,Eo=No=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function To(e){var t=e._currentValue;if(Eo!==e)if(e={context:e,memoizedValue:t,next:null},null===No){if(null===jo)throw Error(o(308));No=e,jo.dependencies={lanes:0,firstContext:e}}else No=No.next=e;return t}var Ro=null;function Ao(e){null===Ro?Ro=[e]:Ro.push(e)}function Lo(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Ao(t)):(n.next=a.next,a.next=n),t.interleaved=n,zo(e,r)}function zo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Do=!1;function Fo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Io(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Uo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Pl)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,zo(e,n)}return null===(a=r.interleaved)?(t.next=t,Ao(r)):(t.next=a.next,a.next=t),r.interleaved=t,zo(e,n)}function Bo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function qo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ho(e,t,n,r){var a=e.updateQueue;Do=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,u=l.next;l.next=null,null===i?o=u:i.next=u,i=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=a.baseState;for(i=0,c=u=l=null,s=o;;){var f=s.lane,p=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var h=e,m=s;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=F({},d,f);break e;case 2:Do=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[s]:f.push(s))}else p={eventTime:p,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=p,l=d):c=c.next=p,i|=f;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(f=s).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(l=d),a.baseState=l,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Dl|=i,e.lanes=i,e.memoizedState=d}}function Vo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(o(191,a));a.call(r)}}}var Wo={},Qo=ja(Wo),Ko=ja(Wo),$o=ja(Wo);function Go(e){if(e===Wo)throw Error(o(174));return e}function Jo(e,t){switch(Ea($o,t),Ea(Ko,e),Ea(Qo,Wo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Na(Qo),Ea(Qo,t)}function Yo(){Na(Qo),Na(Ko),Na($o)}function Xo(e){Go($o.current);var t=Go(Qo.current),n=le(t,e.type);t!==n&&(Ea(Ko,e),Ea(Qo,n))}function Zo(e){Ko.current===e&&(Na(Qo),Na(Ko))}var ei=ja(0);function ti(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ni=[];function ri(){for(var e=0;e<ni.length;e++)ni[e]._workInProgressVersionPrimary=null;ni.length=0}var ai=x.ReactCurrentDispatcher,oi=x.ReactCurrentBatchConfig,ii=0,si=null,li=null,ui=null,ci=!1,di=!1,fi=0,pi=0;function hi(){throw Error(o(321))}function mi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function gi(e,t,n,r,a,i){if(ii=i,si=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ai.current=null===e||null===e.memoizedState?Zi:es,e=n(r,a),di){i=0;do{if(di=!1,fi=0,25<=i)throw Error(o(301));i+=1,ui=li=null,t.updateQueue=null,ai.current=ts,e=n(r,a)}while(di)}if(ai.current=Xi,t=null!==li&&null!==li.next,ii=0,ui=li=si=null,ci=!1,t)throw Error(o(300));return e}function vi(){var e=0!==fi;return fi=0,e}function yi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ui?si.memoizedState=ui=e:ui=ui.next=e,ui}function bi(){if(null===li){var e=si.alternate;e=null!==e?e.memoizedState:null}else e=li.next;var t=null===ui?si.memoizedState:ui.next;if(null!==t)ui=t,li=e;else{if(null===e)throw Error(o(310));e={memoizedState:(li=e).memoizedState,baseState:li.baseState,baseQueue:li.baseQueue,queue:li.queue,next:null},null===ui?si.memoizedState=ui=e:ui=ui.next=e}return ui}function xi(e,t){return"function"===typeof t?t(e):t}function wi(e){var t=bi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=li,a=r.baseQueue,i=n.pending;if(null!==i){if(null!==a){var s=a.next;a.next=i.next,i.next=s}r.baseQueue=a=i,n.pending=null}if(null!==a){i=a.next,r=r.baseState;var l=s=null,u=null,c=i;do{var d=c.lane;if((ii&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,si.lanes|=d,Dl|=d}c=c.next}while(null!==c&&c!==i);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(bs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{i=a.lane,si.lanes|=i,Dl|=i,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ki(e){var t=bi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{i=e(i,s.action),s=s.next}while(s!==a);sr(i,t.memoizedState)||(bs=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Si(){}function ji(e,t){var n=si,r=bi(),a=t(),i=!sr(r.memoizedState,a);if(i&&(r.memoizedState=a,bs=!0),r=r.queue,Di(Oi.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ui&&1&ui.memoizedState.tag){if(n.flags|=2048,Ti(9,Ei.bind(null,n,r,a,t),void 0,null),null===Cl)throw Error(o(349));0!==(30&ii)||Ni(n,t,a)}return a}function Ni(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ei(e,t,n,r){t.value=n,t.getSnapshot=r,Pi(t)&&Ci(e)}function Oi(e,t,n){return n(function(){Pi(t)&&Ci(e)})}function Pi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Ci(e){var t=zo(e,1);null!==t&&nu(t,e,1,-1)}function _i(e){var t=yi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xi,lastRenderedState:e},t.queue=e,e=e.dispatch=$i.bind(null,si,e),[t.memoizedState,e]}function Ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ri(){return bi().memoizedState}function Ai(e,t,n,r){var a=yi();si.flags|=e,a.memoizedState=Ti(1|t,n,void 0,void 0===r?null:r)}function Li(e,t,n,r){var a=bi();r=void 0===r?null:r;var o=void 0;if(null!==li){var i=li.memoizedState;if(o=i.destroy,null!==r&&mi(r,i.deps))return void(a.memoizedState=Ti(t,n,o,r))}si.flags|=e,a.memoizedState=Ti(1|t,n,o,r)}function zi(e,t){return Ai(8390656,8,e,t)}function Di(e,t){return Li(2048,8,e,t)}function Fi(e,t){return Li(4,2,e,t)}function Ii(e,t){return Li(4,4,e,t)}function Ui(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Mi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Li(4,4,Ui.bind(null,t,e),n)}function Bi(){}function qi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Hi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vi(e,t,n){return 0===(21&ii)?(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),si.lanes|=n,Dl|=n,e.baseState=!0),t)}function Wi(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=oi.transition;oi.transition={};try{e(!1),t()}finally{bt=n,oi.transition=r}}function Qi(){return bi().memoizedState}function Ki(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gi(e))Ji(t,n);else if(null!==(n=Lo(e,t,n,r))){nu(n,e,r,eu()),Yi(n,t,r)}}function $i(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gi(e))Ji(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=o(i,n);if(a.hasEagerState=!0,a.eagerState=s,sr(s,i)){var l=t.interleaved;return null===l?(a.next=a,Ao(t)):(a.next=l.next,l.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Lo(e,t,a,r))&&(nu(n,e,r,a=eu()),Yi(n,t,r))}}function Gi(e){var t=e.alternate;return e===si||null!==t&&t===si}function Ji(e,t){di=ci=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yi(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Xi={readContext:To,useCallback:hi,useContext:hi,useEffect:hi,useImperativeHandle:hi,useInsertionEffect:hi,useLayoutEffect:hi,useMemo:hi,useReducer:hi,useRef:hi,useState:hi,useDebugValue:hi,useDeferredValue:hi,useTransition:hi,useMutableSource:hi,useSyncExternalStore:hi,useId:hi,unstable_isNewReconciler:!1},Zi={readContext:To,useCallback:function(e,t){return yi().memoizedState=[e,void 0===t?null:t],e},useContext:To,useEffect:zi,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ai(4194308,4,Ui.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ai(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ai(4,2,e,t)},useMemo:function(e,t){var n=yi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ki.bind(null,si,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yi().memoizedState=e},useState:_i,useDebugValue:Bi,useDeferredValue:function(e){return yi().memoizedState=e},useTransition:function(){var e=_i(!1),t=e[0];return e=Wi.bind(null,e[1]),yi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=si,a=yi();if(ao){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Cl)throw Error(o(349));0!==(30&ii)||Ni(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,zi(Oi.bind(null,r,i,e),[e]),r.flags|=2048,Ti(9,Ei.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=yi(),t=Cl.identifierPrefix;if(ao){var n=Ya;t=":"+t+"R"+(n=(Ja&~(1<<32-it(Ja)-1)).toString(32)+n),0<(n=fi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:To,useCallback:qi,useContext:To,useEffect:Di,useImperativeHandle:Mi,useInsertionEffect:Fi,useLayoutEffect:Ii,useMemo:Hi,useReducer:wi,useRef:Ri,useState:function(){return wi(xi)},useDebugValue:Bi,useDeferredValue:function(e){return Vi(bi(),li.memoizedState,e)},useTransition:function(){return[wi(xi)[0],bi().memoizedState]},useMutableSource:Si,useSyncExternalStore:ji,useId:Qi,unstable_isNewReconciler:!1},ts={readContext:To,useCallback:qi,useContext:To,useEffect:Di,useImperativeHandle:Mi,useInsertionEffect:Fi,useLayoutEffect:Ii,useMemo:Hi,useReducer:ki,useRef:Ri,useState:function(){return ki(xi)},useDebugValue:Bi,useDeferredValue:function(e){var t=bi();return null===li?t.memoizedState=e:Vi(t,li.memoizedState,e)},useTransition:function(){return[ki(xi)[0],bi().memoizedState]},useMutableSource:Si,useSyncExternalStore:ji,useId:Qi,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return!!(e=e._reactInternals)&&qe(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=Uo(r,a);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Mo(e,o,a))&&(nu(t,e,a,r),Bo(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=Uo(r,a);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Mo(e,o,a))&&(nu(t,e,a,r),Bo(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Uo(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Mo(e,a,r))&&(nu(t,e,r,n),Bo(t,e,r))}};function os(e,t,n,r,a,o,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(a,o))}function is(e,t,n){var r=!1,a=Oa,o=t.contextType;return"object"===typeof o&&null!==o?o=To(o):(a=Ra(t)?_a:Pa.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ta(e,a):Oa),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fo(e);var o=t.contextType;"object"===typeof o&&null!==o?a.context=To(o):(o=Ra(t)?_a:Pa.current,a.context=Ta(e,o)),a.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(rs(e,t,o,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&as.enqueueReplaceState(a,a.state,null),Ho(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(o){a="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:a,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fs="function"===typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=Uo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vl||(Vl=!0,Wl=r),ds(0,t)},n}function hs(e,t,n){(n=Uo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ds(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===Ql?Ql=new Set([this]):Ql.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Nu.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vs(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Uo(-1,1)).tag=2,Mo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var ys=x.ReactCurrentOwner,bs=!1;function xs(e,t,n,r){t.child=null===e?ko(t,null,n,r):wo(t,e.child,n,r)}function ws(e,t,n,r,a){n=n.render;var o=t.ref;return _o(t,a),r=gi(e,t,n,r,o,a),n=vi(),null===e||bs?(ao&&n&&eo(t),t.flags|=1,xs(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vs(e,t,a))}function ks(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Ru(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Lu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Ss(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(i,r)&&e.ref===t.ref)return Vs(e,t,a)}return t.flags|=1,(e=Au(o,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,Vs(e,t,a);0!==(131072&e.flags)&&(bs=!0)}}return Es(e,t,n,r,a)}function js(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ea(Al,Rl),Rl|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ea(Al,Rl),Rl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ea(Al,Rl),Rl|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ea(Al,Rl),Rl|=r;return xs(e,t,a,n),t.child}function Ns(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Es(e,t,n,r,a){var o=Ra(n)?_a:Pa.current;return o=Ta(t,o),_o(t,a),n=gi(e,t,n,r,o,a),r=vi(),null===e||bs?(ao&&r&&eo(t),t.flags|=1,xs(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vs(e,t,a))}function Os(e,t,n,r,a){if(Ra(n)){var o=!0;Da(t)}else o=!1;if(_o(t,a),null===t.stateNode)Hs(e,t),is(t,n,r),ls(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,s=t.memoizedProps;i.props=s;var l=i.context,u=n.contextType;"object"===typeof u&&null!==u?u=To(u):u=Ta(t,u=Ra(n)?_a:Pa.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==r||l!==u)&&ss(t,i,r,u),Do=!1;var f=t.memoizedState;i.state=f,Ho(t,r,i,a),l=t.memoizedState,s!==r||f!==l||Ca.current||Do?("function"===typeof c&&(rs(t,n,c,r),l=t.memoizedState),(s=Do||os(t,n,s,r,f,l,u))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=u,r=s):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Io(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ns(t.type,s),i.props=u,d=t.pendingProps,f=i.context,"object"===typeof(l=n.contextType)&&null!==l?l=To(l):l=Ta(t,l=Ra(n)?_a:Pa.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,i,r,l),Do=!1,f=t.memoizedState,i.state=f,Ho(t,r,i,a);var h=t.memoizedState;s!==d||f!==h||Ca.current||Do?("function"===typeof p&&(rs(t,n,p,r),h=t.memoizedState),(u=Do||os(t,n,u,r,f,h,l)||!1)?(c||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,l),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,l)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=l,r=u):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ps(e,t,n,r,o,a)}function Ps(e,t,n,r,a,o){Ns(e,t);var i=0!==(128&t.flags);if(!r&&!i)return a&&Fa(t,n,!1),Vs(e,t,o);r=t.stateNode,ys.current=t;var s=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=wo(t,e.child,null,o),t.child=wo(t,null,s,o)):xs(e,t,s,o),t.memoizedState=r.state,a&&Fa(t,n,!0),t.child}function Cs(e){var t=e.stateNode;t.pendingContext?La(0,t.pendingContext,t.pendingContext!==t.context):t.context&&La(0,t.context,!1),Jo(e,t.containerInfo)}function _s(e,t,n,r,a){return ho(),mo(a),t.flags|=256,xs(e,t,n,r),t.child}var Ts,Rs,As,Ls,zs={dehydrated:null,treeContext:null,retryLane:0};function Ds(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fs(e,t,n){var r,a=t.pendingProps,i=ei.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Ea(ei,1&i),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=a.children,e=a.fallback,s?(a=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&a)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Du(l,a,0,null),e=zu(e,a,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ds(n),t.memoizedState=zs,e):Is(t,l));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,a,i,s){if(n)return 256&t.flags?(t.flags&=-257,Us(e,t,s,r=cs(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,a=t.mode,r=Du({mode:"visible",children:r.children},a,0,null),(i=zu(i,a,s,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&wo(t,e.child,null,s),t.child.memoizedState=Ds(s),t.memoizedState=zs,i);if(0===(1&t.mode))return Us(e,t,s,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var l=r.dgst;return r=l,Us(e,t,s,r=cs(i=Error(o(419)),r,void 0))}if(l=0!==(s&e.childLanes),bs||l){if(null!==(r=Cl)){switch(s&-s){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|s))?0:a)&&a!==i.retryLane&&(i.retryLane=a,zo(e,a),nu(r,e,a,-1))}return mu(),Us(e,t,s,r=cs(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Ou.bind(null,e),a._reactRetry=t,null):(e=i.treeContext,ro=ua(a.nextSibling),no=t,ao=!0,oo=null,null!==e&&(Ka[$a++]=Ja,Ka[$a++]=Ya,Ka[$a++]=Ga,Ja=e.id,Ya=e.overflow,Ga=t),t=Is(t,r.children),t.flags|=4096,t)}(e,t,l,a,r,i,n);if(s){s=a.fallback,l=t.mode,r=(i=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&l)&&t.child!==i?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Au(i,u)).subtreeFlags=14680064&i.subtreeFlags,null!==r?s=Au(r,s):(s=zu(s,l,n,null)).flags|=2,s.return=t,a.return=t,a.sibling=s,t.child=a,a=s,s=t.child,l=null===(l=e.child.memoizedState)?Ds(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=zs,a}return e=(s=e.child).sibling,a=Au(s,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Is(e,t){return(t=Du({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Us(e,t,n,r){return null!==r&&mo(r),wo(t,e.child,null,n),(e=Is(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ms(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Co(e.return,t,n)}function Bs(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function qs(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(xs(e,t,r.children,n),0!==(2&(r=ei.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ms(e,n,t);else if(19===e.tag)Ms(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ea(ei,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ti(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bs(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ti(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bs(t,!0,n,null,o);break;case"together":Bs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hs(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Dl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Au(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Au(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ws(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ks(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qs(t),null;case 1:case 17:return Ra(t.type)&&Aa(),Qs(t),null;case 3:return r=t.stateNode,Yo(),Na(Ca),Na(Pa),ri(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(iu(oo),oo=null))),Rs(e,t),Qs(t),null;case 5:Zo(t);var a=Go($o.current);if(n=t.type,null!==e&&null!=t.stateNode)As(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Qs(t),null}if(e=Go(Qo.current),fo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[fa]=t,r[pa]=i,e=0!==(1&t.mode),n){case"dialog":Ur("cancel",r),Ur("close",r);break;case"iframe":case"object":case"embed":Ur("load",r);break;case"video":case"audio":for(a=0;a<zr.length;a++)Ur(zr[a],r);break;case"source":Ur("error",r);break;case"img":case"image":case"link":Ur("error",r),Ur("load",r);break;case"details":Ur("toggle",r);break;case"input":J(r,i),Ur("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ur("invalid",r);break;case"textarea":ae(r,i),Ur("invalid",r)}for(var l in ye(n,i),a=null,i)if(i.hasOwnProperty(l)){var u=i[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==i.suppressHydrationWarning&&Xr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==i.suppressHydrationWarning&&Xr(r.textContent,u,e),a=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&Ur("scroll",r)}switch(n){case"input":Q(r),Z(r,i,!0);break;case"textarea":Q(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fa]=t,e[pa]=r,Ts(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Ur("cancel",e),Ur("close",e),a=r;break;case"iframe":case"object":case"embed":Ur("load",e),a=r;break;case"video":case"audio":for(a=0;a<zr.length;a++)Ur(zr[a],e);a=r;break;case"source":Ur("error",e),a=r;break;case"img":case"image":case"link":Ur("error",e),Ur("load",e),a=r;break;case"details":Ur("toggle",e),a=r;break;case"input":J(e,r),a=G(e,r),Ur("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=F({},r,{value:void 0}),Ur("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ur("invalid",e)}for(i in ye(n,a),u=a)if(u.hasOwnProperty(i)){var c=u[i];"style"===i?ge(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===i?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(s.hasOwnProperty(i)?null!=c&&"onScroll"===i&&Ur("scroll",e):null!=c&&b(e,i,c,l))}switch(n){case"input":Q(e),Z(e,r,!1);break;case"textarea":Q(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Qs(t),null;case 6:if(e&&null!=t.stateNode)Ls(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Go($o.current),Go(Qo.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(i=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Xr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Xr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Qs(t),null;case 13:if(Na(ei),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ao&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))po(),ho(),t.flags|=98560,i=!1;else if(i=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(o(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(o(317));i[fa]=t}else ho(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Qs(t),i=!1}else null!==oo&&(iu(oo),oo=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ei.current)?0===Ll&&(Ll=3):mu())),null!==t.updateQueue&&(t.flags|=4),Qs(t),null);case 4:return Yo(),Rs(e,t),null===e&&qr(t.stateNode.containerInfo),Qs(t),null;case 10:return Po(t.type._context),Qs(t),null;case 19:if(Na(ei),null===(i=t.memoizedState))return Qs(t),null;if(r=0!==(128&t.flags),null===(l=i.rendering))if(r)Ws(i,!1);else{if(0!==Ll||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ti(e))){for(t.flags|=128,Ws(i,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(l=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ea(ei,1&ei.current|2),t.child}e=e.sibling}null!==i.tail&&Ye()>ql&&(t.flags|=128,r=!0,Ws(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ti(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ws(i,!0),null===i.tail&&"hidden"===i.tailMode&&!l.alternate&&!ao)return Qs(t),null}else 2*Ye()-i.renderingStartTime>ql&&1073741824!==n&&(t.flags|=128,r=!0,Ws(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=i.last)?n.sibling=l:t.child=l,i.last=l)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ye(),t.sibling=null,n=ei.current,Ea(ei,r?1&n|2:1&n),t):(Qs(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rl)&&(Qs(t),6&t.subtreeFlags&&(t.flags|=8192)):Qs(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function $s(e,t){switch(to(t),t.tag){case 1:return Ra(t.type)&&Aa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Yo(),Na(Ca),Na(Pa),ri(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zo(t),null;case 13:if(Na(ei),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));ho()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Na(ei),null;case 4:return Yo(),null;case 10:return Po(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ts=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Rs=function(){},As=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Go(Qo.current);var o,i=null;switch(n){case"input":a=G(e,a),r=G(e,r),i=[];break;case"select":a=F({},a,{value:void 0}),r=F({},r,{value:void 0}),i=[];break;case"textarea":a=re(e,a),r=re(e,r),i=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ye(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var l=a[c];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(i=i||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(i=i||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ur("scroll",e),i||l===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Ls=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gs=!1,Js=!1,Ys="function"===typeof WeakSet?WeakSet:Set,Xs=null;function Zs(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){ju(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){ju(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&el(t,n,o)}a=a.next}while(a!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function al(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ol(e){var t=e.alternate;null!==t&&(e.alternate=null,ol(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function il(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||il(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)pl(e,t,n),n=n.sibling}function pl(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(at,n)}catch(s){}switch(n.tag){case 5:Js||Zs(n,t);case 6:var r=cl,a=dl;cl=null,fl(e,t,n),dl=a,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?la(e.parentNode,n):1===e.nodeType&&la(e,n),qt(e)):la(cl,n.stateNode));break;case 4:r=cl,a=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=a;break;case 0:case 11:case 14:case 15:if(!Js&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,i=o.destroy;o=o.tag,void 0!==i&&(0!==(2&o)||0!==(4&o))&&el(n,t,i),a=a.next}while(a!==r)}fl(e,t,n);break;case 1:if(!Js&&(Zs(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){ju(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Js=(r=Js)||null!==n.memoizedState,fl(e,t,n),Js=r):fl(e,t,n);break;default:fl(e,t,n)}}function hl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Ys),t.forEach(function(t){var r=Pu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(o(160));pl(i,s,a),cl=null,dl=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){ju(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),vl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(g){ju(e,e.return,g)}try{nl(5,e,e.return)}catch(g){ju(e,e.return,g)}}break;case 1:ml(t,e),vl(e),512&r&&null!==n&&Zs(n,n.return);break;case 5:if(ml(t,e),vl(e),512&r&&null!==n&&Zs(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){ju(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var i=e.memoizedProps,s=null!==n?n.memoizedProps:i,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===i.type&&null!=i.name&&Y(a,i),be(l,s);var c=be(l,i);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(l){case"input":X(a,i);break;case"textarea":oe(a,i);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(a,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(a,!!i.multiple,i.defaultValue,!0):ne(a,!!i.multiple,i.multiple?[]:"",!1))}a[pa]=i}catch(g){ju(e,e.return,g)}}break;case 6:if(ml(t,e),vl(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,i=e.memoizedProps;try{a.nodeValue=i}catch(g){ju(e,e.return,g)}}break;case 3:if(ml(t,e),vl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{qt(t.containerInfo)}catch(g){ju(e,e.return,g)}break;case 4:default:ml(t,e),vl(e);break;case 13:ml(t,e),vl(e),8192&(a=e.child).flags&&(i=null!==a.memoizedState,a.stateNode.isHidden=i,!i||null!==a.alternate&&null!==a.alternate.memoizedState||(Bl=Ye())),4&r&&hl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Js=(c=Js)||d,ml(t,e),Js=c):ml(t,e),vl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Xs=e,d=e.child;null!==d;){for(f=Xs=d;null!==Xs;){switch(h=(p=Xs).child,p.tag){case 0:case 11:case 14:case 15:nl(4,p,p.return);break;case 1:Zs(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){ju(r,n,g)}}break;case 5:Zs(p,p.return);break;case 22:if(null!==p.memoizedState){wl(f);continue}}null!==h?(h.return=p,Xs=h):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(i=a.style).setProperty?i.setProperty("display","none","important"):i.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",s))}catch(g){ju(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){ju(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),vl(e),4&r&&hl(e);case 21:}}function vl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(il(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),ul(e,sl(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;ll(e,sl(e),i);break;default:throw Error(o(161))}}catch(s){ju(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yl(e,t,n){Xs=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Xs;){var a=Xs,o=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Gs;if(!i){var s=a.alternate,l=null!==s&&null!==s.memoizedState||Js;s=Gs;var u=Js;if(Gs=i,(Js=l)&&!u)for(Xs=a;null!==Xs;)l=(i=Xs).child,22===i.tag&&null!==i.memoizedState?kl(a):null!==l?(l.return=i,Xs=l):kl(a);for(;null!==o;)Xs=o,bl(o,t,n),o=o.sibling;Xs=a,Gs=s,Js=u}xl(e)}else 0!==(8772&a.subtreeFlags)&&null!==o?(o.return=a,Xs=o):xl(e)}}function xl(e){for(;null!==Xs;){var t=Xs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Js||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Js)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Vo(t,i,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Vo(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&qt(f)}}}break;default:throw Error(o(163))}Js||512&t.flags&&al(t)}catch(p){ju(t,t.return,p)}}if(t===e){Xs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Xs=n;break}Xs=t.return}}function wl(e){for(;null!==Xs;){var t=Xs;if(t===e){Xs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Xs=n;break}Xs=t.return}}function kl(e){for(;null!==Xs;){var t=Xs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){ju(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(l){ju(t,a,l)}}var o=t.return;try{al(t)}catch(l){ju(t,o,l)}break;case 5:var i=t.return;try{al(t)}catch(l){ju(t,i,l)}}}catch(l){ju(t,t.return,l)}if(t===e){Xs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Xs=s;break}Xs=t.return}}var Sl,jl=Math.ceil,Nl=x.ReactCurrentDispatcher,El=x.ReactCurrentOwner,Ol=x.ReactCurrentBatchConfig,Pl=0,Cl=null,_l=null,Tl=0,Rl=0,Al=ja(0),Ll=0,zl=null,Dl=0,Fl=0,Il=0,Ul=null,Ml=null,Bl=0,ql=1/0,Hl=null,Vl=!1,Wl=null,Ql=null,Kl=!1,$l=null,Gl=0,Jl=0,Yl=null,Xl=-1,Zl=0;function eu(){return 0!==(6&Pl)?Ye():-1!==Xl?Xl:Xl=Ye()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Pl)&&0!==Tl?Tl&-Tl:null!==go.transition?(0===Zl&&(Zl=mt()),Zl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function nu(e,t,n,r){if(50<Jl)throw Jl=0,Yl=null,Error(o(185));vt(e,n,r),0!==(2&Pl)&&e===Cl||(e===Cl&&(0===(2&Pl)&&(Fl|=n),4===Ll&&su(e,Tl)),ru(e,r),1===n&&0===Pl&&0===(1&t.mode)&&(ql=Ye()+500,Ua&&qa()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-it(o),s=1<<i,l=a[i];-1===l?0!==(s&n)&&0===(s&r)||(a[i]=pt(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}(e,t);var r=ft(e,e===Cl?Tl:0);if(0===r)null!==n&&$e(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&$e(n),1===t)0===e.tag?function(e){Ua=!0,Ba(e)}(lu.bind(null,e)):Ba(lu.bind(null,e)),ia(function(){0===(6&Pl)&&qa()}),n=null;else{switch(xt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Cu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Xl=-1,Zl=0,0!==(6&Pl))throw Error(o(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===Cl?Tl:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=Pl;Pl|=2;var i=hu();for(Cl===e&&Tl===t||(Hl=null,ql=Ye()+500,fu(e,t));;)try{yu();break}catch(l){pu(e,l)}Oo(),Nl.current=i,Pl=a,null!==_l?t=0:(Cl=null,Tl=0,t=Ll)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=ou(e,a))),1===t)throw n=zl,fu(e,0),su(e,r),ru(e,Ye()),n;if(6===t)su(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!sr(o(),a))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gu(e,r))&&(0!==(i=ht(e))&&(r=i,t=ou(e,i))),1===t))throw n=zl,fu(e,0),su(e,r),ru(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:wu(e,Ml,Hl);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Bl+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,Ml,Hl),t);break}wu(e,Ml,Hl);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var s=31-it(r);i=1<<s,(s=t[s])>a&&(a=s),r&=~i}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*jl(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,Ml,Hl),r);break}wu(e,Ml,Hl);break;default:throw Error(o(329))}}}return ru(e,Ye()),e.callbackNode===n?au.bind(null,e):null}function ou(e,t){var n=Ul;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Ml,Ml=n,null!==t&&iu(t)),e}function iu(e){null===Ml?Ml=e:Ml.push.apply(Ml,e)}function su(e,t){for(t&=~Il,t&=~Fl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&Pl))throw Error(o(327));ku();var t=ft(e,0);if(0===(1&t))return ru(e,Ye()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=ou(e,r))}if(1===n)throw n=zl,fu(e,0),su(e,t),ru(e,Ye()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Ml,Hl),ru(e,Ye()),null}function uu(e,t){var n=Pl;Pl|=1;try{return e(t)}finally{0===(Pl=n)&&(ql=Ye()+500,Ua&&qa())}}function cu(e){null!==$l&&0===$l.tag&&0===(6&Pl)&&ku();var t=Pl;Pl|=1;var n=Ol.transition,r=bt;try{if(Ol.transition=null,bt=1,e)return e()}finally{bt=r,Ol.transition=n,0===(6&(Pl=t))&&qa()}}function du(){Rl=Al.current,Na(Al)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==_l)for(n=_l.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Aa();break;case 3:Yo(),Na(Ca),Na(Pa),ri();break;case 5:Zo(r);break;case 4:Yo();break;case 13:case 19:Na(ei);break;case 10:Po(r.type._context);break;case 22:case 23:du()}n=n.return}if(Cl=e,_l=e=Au(e.current,null),Tl=Rl=t,Ll=0,zl=null,Il=Fl=Dl=0,Ml=Ul=null,null!==Ro){for(t=0;t<Ro.length;t++)if(null!==(r=(n=Ro[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var i=o.next;o.next=a,r.next=i}n.pending=r}Ro=null}return e}function pu(e,t){for(;;){var n=_l;try{if(Oo(),ai.current=Xi,ci){for(var r=si.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}ci=!1}if(ii=0,ui=li=si=null,di=!1,fi=0,El.current=null,null===n||null===n.return){Ll=1,zl=t,_l=null;break}e:{var i=e,s=n.return,l=n,u=t;if(t=Tl,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gs(s);if(null!==h){h.flags&=-257,vs(h,s,l,0,t),1&h.mode&&ms(i,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){ms(i,c,t),mu();break e}u=Error(o(426))}else if(ao&&1&l.mode){var v=gs(s);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vs(v,s,l,0,t),mo(us(u,l));break e}}i=u=us(u,l),4!==Ll&&(Ll=2),null===Ul?Ul=[i]:Ul.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,qo(i,ps(0,u,t));break e;case 1:l=u;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Ql||!Ql.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,qo(i,hs(i,l,t));break e}}i=i.return}while(null!==i)}xu(n)}catch(x){t=x,_l===n&&null!==n&&(_l=n=n.return);continue}break}}function hu(){var e=Nl.current;return Nl.current=Xi,null===e?Xi:e}function mu(){0!==Ll&&3!==Ll&&2!==Ll||(Ll=4),null===Cl||0===(268435455&Dl)&&0===(268435455&Fl)||su(Cl,Tl)}function gu(e,t){var n=Pl;Pl|=2;var r=hu();for(Cl===e&&Tl===t||(Hl=null,fu(e,t));;)try{vu();break}catch(a){pu(e,a)}if(Oo(),Pl=n,Nl.current=r,null!==_l)throw Error(o(261));return Cl=null,Tl=0,Ll}function vu(){for(;null!==_l;)bu(_l)}function yu(){for(;null!==_l&&!Ge();)bu(_l)}function bu(e){var t=Sl(e.alternate,e,Rl);e.memoizedProps=e.pendingProps,null===t?xu(e):_l=t,El.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Ks(n,t,Rl)))return void(_l=n)}else{if(null!==(n=$s(n,t)))return n.flags&=32767,void(_l=n);if(null===e)return Ll=6,void(_l=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(_l=t);_l=t=e}while(null!==t);0===Ll&&(Ll=5)}function wu(e,t,n){var r=bt,a=Ol.transition;try{Ol.transition=null,bt=1,function(e,t,n,r){do{ku()}while(null!==$l);if(0!==(6&Pl))throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-it(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,i),e===Cl&&(_l=Cl=null,Tl=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Kl||(Kl=!0,Cu(tt,function(){return ku(),null})),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Ol.transition,Ol.transition=null;var s=bt;bt=1;var l=Pl;Pl|=4,El.current=null,function(e,t){if(ea=Vt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(w){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(l=s+a),f!==i||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(l=s),p===i&&++d===r&&(u=s),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Vt=!1,Xs=t;null!==Xs;)if(e=(t=Xs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Xs=e;else for(;null!==Xs;){t=Xs;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:ns(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(o(163))}}catch(w){ju(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Xs=e;break}Xs=t.return}m=tl,tl=!1}(e,n),gl(n,e),hr(ta),Vt=!!ea,ta=ea=null,e.current=n,yl(n,e,a),Je(),Pl=l,bt=s,Ol.transition=i}else e.current=n;if(Kl&&(Kl=!1,$l=e,Gl=a),i=e.pendingLanes,0===i&&(Ql=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Vl)throw Vl=!1,e=Wl,Wl=null,e;0!==(1&Gl)&&0!==e.tag&&ku(),i=e.pendingLanes,0!==(1&i)?e===Yl?Jl++:(Jl=0,Yl=e):Jl=0,qa()}(e,t,n,r)}finally{Ol.transition=a,bt=r}return null}function ku(){if(null!==$l){var e=xt(Gl),t=Ol.transition,n=bt;try{if(Ol.transition=null,bt=16>e?16:e,null===$l)var r=!1;else{if(e=$l,$l=null,Gl=0,0!==(6&Pl))throw Error(o(331));var a=Pl;for(Pl|=4,Xs=e.current;null!==Xs;){var i=Xs,s=i.child;if(0!==(16&Xs.flags)){var l=i.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Xs=c;null!==Xs;){var d=Xs;switch(d.tag){case 0:case 11:case 15:nl(8,d,i)}var f=d.child;if(null!==f)f.return=d,Xs=f;else for(;null!==Xs;){var p=(d=Xs).sibling,h=d.return;if(ol(d),d===c){Xs=null;break}if(null!==p){p.return=h,Xs=p;break}Xs=h}}}var m=i.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Xs=i}}if(0!==(2064&i.subtreeFlags)&&null!==s)s.return=i,Xs=s;else e:for(;null!==Xs;){if(0!==(2048&(i=Xs).flags))switch(i.tag){case 0:case 11:case 15:nl(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Xs=y;break e}Xs=i.return}}var b=e.current;for(Xs=b;null!==Xs;){var x=(s=Xs).child;if(0!==(2064&s.subtreeFlags)&&null!==x)x.return=s,Xs=x;else e:for(s=b;null!==Xs;){if(0!==(2048&(l=Xs).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(k){ju(l,l.return,k)}if(l===s){Xs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Xs=w;break e}Xs=l.return}}if(Pl=a,qa(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(at,e)}catch(k){}r=!0}return r}finally{bt=n,Ol.transition=t}}return!1}function Su(e,t,n){e=Mo(e,t=ps(0,t=us(n,t),1),1),t=eu(),null!==e&&(vt(e,1,t),ru(e,t))}function ju(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Ql||!Ql.has(r))){t=Mo(t,e=hs(t,e=us(n,e),1),1),e=eu(),null!==t&&(vt(t,1,e),ru(t,e));break}}t=t.return}}function Nu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Cl===e&&(Tl&n)===n&&(4===Ll||3===Ll&&(130023424&Tl)===Tl&&500>Ye()-Bl?fu(e,0):Il|=n),ru(e,t)}function Eu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=zo(e,t))&&(vt(e,t,n),ru(e,n))}function Ou(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Eu(e,n)}function Pu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Eu(e,n)}function Cu(e,t){return Ke(e,t)}function _u(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tu(e,t,n,r){return new _u(e,t,n,r)}function Ru(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Au(e,t){var n=e.alternate;return null===n?((n=Tu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Lu(e,t,n,r,a,i){var s=2;if(r=e,"function"===typeof e)Ru(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case S:return zu(n.children,a,i,t);case j:s=8,a|=8;break;case N:return(e=Tu(12,n,t,2|a)).elementType=N,e.lanes=i,e;case C:return(e=Tu(13,n,t,a)).elementType=C,e.lanes=i,e;case _:return(e=Tu(19,n,t,a)).elementType=_,e.lanes=i,e;case A:return Du(n,a,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:s=10;break e;case O:s=9;break e;case P:s=11;break e;case T:s=14;break e;case R:s=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Tu(s,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function zu(e,t,n,r){return(e=Tu(7,e,r,t)).lanes=n,e}function Du(e,t,n,r){return(e=Tu(22,e,r,t)).elementType=A,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Tu(6,e,null,t)).lanes=n,e}function Iu(e,t,n){return(t=Tu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Mu(e,t,n,r,a,o,i,s,l){return e=new Uu(e,t,n,s,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Tu(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fo(o),e}function Bu(e){if(!e)return Oa;e:{if(qe(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ra(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Ra(n))return za(e,n,t)}return t}function qu(e,t,n,r,a,o,i,s,l){return(e=Mu(n,r,!0,e,0,o,0,s,l)).context=Bu(null),n=e.current,(o=Uo(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Mo(n,o,a),e.current.lanes=a,vt(e,a,r),ru(e,r),e}function Hu(e,t,n,r){var a=t.current,o=eu(),i=tu(a);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Uo(o,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Mo(a,t,i))&&(nu(e,a,i,o),Bo(e,a,i)),i}function Vu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Wu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Qu(e,t){Wu(e,t),(e=e.alternate)&&Wu(e,t)}Sl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ca.current)bs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:Cs(t),ho();break;case 5:Xo(t);break;case 1:Ra(t.type)&&Da(t);break;case 4:Jo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ea(So,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ea(ei,1&ei.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fs(e,t,n):(Ea(ei,1&ei.current),null!==(e=Vs(e,t,n))?e.sibling:null);Ea(ei,1&ei.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return qs(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ea(ei,ei.current),r)break;return null;case 22:case 23:return t.lanes=0,js(e,t,n)}return Vs(e,t,n)}(e,t,n);bs=0!==(131072&e.flags)}else bs=!1,ao&&0!==(1048576&t.flags)&&Za(t,Qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Hs(e,t),e=t.pendingProps;var a=Ta(t,Pa.current);_o(t,n),a=gi(null,t,r,e,a,n);var i=vi();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ra(r)?(i=!0,Da(t)):i=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fo(t),a.updater=as,t.stateNode=a,a._reactInternals=t,ls(t,r,e,n),t=Ps(null,t,r,!0,i,n)):(t.tag=0,ao&&i&&eo(t),xs(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Hs(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Ru(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===T)return 14}return 2}(r),e=ns(r,e),a){case 0:t=Es(null,t,r,e,n);break e;case 1:t=Os(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=ks(null,t,r,ns(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Es(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 1:return r=t.type,a=t.pendingProps,Os(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 3:e:{if(Cs(t),null===e)throw Error(o(387));r=t.pendingProps,a=(i=t.memoizedState).element,Io(e,t),Ho(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=_s(e,t,r,n,a=us(Error(o(423)),t));break e}if(r!==a){t=_s(e,t,r,n,a=us(Error(o(424)),t));break e}for(ro=ua(t.stateNode.containerInfo.firstChild),no=t,ao=!0,oo=null,n=ko(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ho(),r===a){t=Vs(e,t,n);break e}xs(e,t,r,n)}t=t.child}return t;case 5:return Xo(t),null===e&&uo(t),r=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,s=a.children,na(r,a)?s=null:null!==i&&na(r,i)&&(t.flags|=32),Ns(e,t),xs(e,t,s,n),t.child;case 6:return null===e&&uo(t),null;case 13:return Fs(e,t,n);case 4:return Jo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wo(t,null,r,n):xs(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,ws(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 7:return xs(e,t,t.pendingProps,n),t.child;case 8:case 12:return xs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,s=a.value,Ea(So,r._currentValue),r._currentValue=s,null!==i)if(sr(i.value,s)){if(i.children===a.children&&!Ca.current){t=Vs(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var l=i.dependencies;if(null!==l){s=i.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===i.tag){(u=Uo(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),Co(i.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===i.tag)s=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(s=i.return))throw Error(o(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Co(s,n,t),s=i.sibling}else s=i.child;if(null!==s)s.return=i;else for(s=i;null!==s;){if(s===t){s=null;break}if(null!==(i=s.sibling)){i.return=s.return,s=i;break}s=s.return}i=s}xs(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,_o(t,n),r=r(a=To(a)),t.flags|=1,xs(e,t,r,n),t.child;case 14:return a=ns(r=t.type,t.pendingProps),ks(e,t,r,a=ns(r.type,a),n);case 15:return Ss(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ns(r,a),Hs(e,t),t.tag=1,Ra(r)?(e=!0,Da(t)):e=!1,_o(t,n),is(t,r,a),ls(t,r,a,n),Ps(null,t,r,!0,e,n);case 19:return qs(e,t,n);case 22:return js(e,t,n)}throw Error(o(156,t.tag))};var Ku="function"===typeof reportError?reportError:function(e){console.error(e)};function $u(e){this._internalRoot=e}function Gu(e){this._internalRoot=e}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Xu(){}function Zu(e,t,n,r,a){var o=n._reactRootContainer;if(o){var i=o;if("function"===typeof a){var s=a;a=function(){var e=Vu(i);s.call(e)}}Hu(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"===typeof r){var o=r;r=function(){var e=Vu(i);o.call(e)}}var i=qu(t,r,e,0,null,!1,0,"",Xu);return e._reactRootContainer=i,e[ha]=i.current,qr(8===e.nodeType?e.parentNode:e),cu(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var s=r;r=function(){var e=Vu(l);s.call(e)}}var l=Mu(e,0,!1,null,0,!1,0,"",Xu);return e._reactRootContainer=l,e[ha]=l.current,qr(8===e.nodeType?e.parentNode:e),cu(function(){Hu(t,l,n,r)}),l}(n,t,e,a,r);return Vu(i)}Gu.prototype.render=$u.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Hu(e,t,null,null)},Gu.prototype.unmount=$u.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Hu(null,e,null,null)}),t[ha]=null}},Gu.prototype.unstable_scheduleHydration=function(e){if(e){var t=jt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<At.length&&0!==t&&t<At[n].priority;n++);At.splice(n,0,e),0===n&&Ft(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ru(t,Ye()),0===(6&Pl)&&(ql=Ye()+500,qa()))}break;case 13:cu(function(){var t=zo(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Qu(e,1)}},kt=function(e){if(13===e.tag){var t=zo(e,134217728);if(null!==t)nu(t,e,134217728,eu());Qu(e,134217728)}},St=function(e){if(13===e.tag){var t=tu(e),n=zo(e,t);if(null!==n)nu(n,e,t,eu());Qu(e,t)}},jt=function(){return bt},Nt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},ke=function(e,t,n){switch(t){case"input":if(X(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(o(90));K(r),X(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=uu,Ce=cu;var ec={usingClientEntryPoint:!1,Events:[ba,xa,wa,Ee,Oe,uu]},tc={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=We(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),ot=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ju(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Ju(e))throw Error(o(299));var n=!1,r="",a=Ku;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Mu(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,qr(8===e.nodeType?e.parentNode:e),new $u(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=We(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Yu(t))throw Error(o(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Ju(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,i="",s=Ku;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=qu(t,null,e,1,null!=n?n:null,a,0,i,s),e[ha]=t.current,qr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Gu(t)},t.render=function(e,t,n){if(!Yu(t))throw Error(o(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yu(e))throw Error(o(40));return!!e._reactRootContainer&&(cu(function(){Zu(null,null,e,!1,function(){e._reactRootContainer=null,e[ha]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},844:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},853:(e,t,n)=>{"use strict";e.exports=n(234)},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var o=Object.create(null);n.r(o);var i={};e=e||[null,t({}),t([]),t(t)];for(var s=2&a&&r;("object"==typeof s||"function"==typeof s)&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>i[e]=()=>r[e]);return i.default=()=>r,n.d(o,i),o}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".670e15c7.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="frontend:";n.l=(r,a,o,i)=>{if(e[r])e[r].push(a);else{var s,l;if(void 0!==o)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+o),s.src=r),e[r]=[a];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach(e=>e(n)),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var o=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=o);var i=n.p+n.u(t),s=new Error;n.l(i,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+i+")",s.name="ChunkLoadError",s.type=o,s.request=i,a[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,o,i=r[0],s=r[1],l=r[2],u=0;if(i.some(t=>0!==e[t])){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)l(n)}for(t&&t(r);u<i.length;u++)o=i[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkfrontend=self.webpackChunkfrontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>Ht,hasStandardBrowserEnv:()=>Wt,hasStandardBrowserWebWorkerEnv:()=>Qt,navigator:()=>Vt,origin:()=>Kt});var t,r=n(43),a=n.t(r,2),o=n(391),i=n(950),s=n.t(i,2);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(t||(t={}));const u="popstate";function c(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function d(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function f(e,t){return{usr:e.state,key:e.key,idx:t}}function p(e,t,n,r){return void 0===n&&(n=null),l({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?m(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function h(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function m(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function g(e,n,r,a){void 0===a&&(a={});let{window:o=document.defaultView,v5Compat:i=!1}=a,s=o.history,d=t.Pop,m=null,g=v();function v(){return(s.state||{idx:null}).idx}function y(){d=t.Pop;let e=v(),n=null==e?null:e-g;g=e,m&&m({action:d,location:x.location,delta:n})}function b(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,n="string"===typeof e?e:h(e);return n=n.replace(/ $/,"%20"),c(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==g&&(g=0,s.replaceState(l({},s.state,{idx:g}),""));let x={get action(){return d},get location(){return e(o,s)},listen(e){if(m)throw new Error("A history only accepts one active listener");return o.addEventListener(u,y),m=e,()=>{o.removeEventListener(u,y),m=null}},createHref:e=>n(o,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,n){d=t.Push;let a=p(x.location,e,n);r&&r(a,e),g=v()+1;let l=f(a,g),u=x.createHref(a);try{s.pushState(l,"",u)}catch(c){if(c instanceof DOMException&&"DataCloneError"===c.name)throw c;o.location.assign(u)}i&&m&&m({action:d,location:x.location,delta:1})},replace:function(e,n){d=t.Replace;let a=p(x.location,e,n);r&&r(a,e),g=v();let o=f(a,g),l=x.createHref(a);s.replaceState(o,"",l),i&&m&&m({action:d,location:x.location,delta:0})},go:e=>s.go(e)};return x}var v;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(v||(v={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function y(e,t,n){return void 0===n&&(n="/"),b(e,t,n,!1)}function b(e,t,n,r){let a=A(("string"===typeof t?m(t):t).pathname||"/",n);if(null==a)return null;let o=x(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(o);let i=null;for(let s=0;null==i&&s<o.length;++s){let e=R(a);i=_(o[s],e,r)}return i}function x(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(c(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let s=I([r,i.relativePath]),l=n.concat(i);e.children&&e.children.length>0&&(c(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),x(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:C(s,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of w(e.path))a(e,t,r);else a(e,t)}),t}function w(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=w(r.join("/")),s=[];return s.push(...i.map(e=>""===e?o:[o,e].join("/"))),a&&s.push(...i),s.map(t=>e.startsWith("/")&&""===t?"/":t)}const k=/^:[\w-]+$/,S=3,j=2,N=1,E=10,O=-2,P=e=>"*"===e;function C(e,t){let n=e.split("/"),r=n.length;return n.some(P)&&(r+=O),t&&(r+=j),n.filter(e=>!P(e)).reduce((e,t)=>e+(k.test(t)?S:""===t?N:E),r)}function _(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},o="/",i=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=T({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=T({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:I([o,c.pathname]),pathnameBase:U(I([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=I([o,c.pathnameBase]))}return i}function T(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);d("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function R(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return d(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function A(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function L(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function z(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function D(e,t){let n=z(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function F(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=m(e):(a=l({},e),c(!a.pathname||!a.pathname.includes("?"),L("?","pathname","search",a)),c(!a.pathname||!a.pathname.includes("#"),L("#","pathname","hash",a)),c(!a.search||!a.search.includes("#"),L("#","search","hash",a)));let o,i=""===e||""===a.pathname,s=i?"/":a.pathname;if(null==s)o=n;else{let e=t.length-1;if(!r&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let u=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?m(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:M(r),hash:B(a)}}(a,o),d=s&&"/"!==s&&s.endsWith("/"),f=(i||"."===s)&&n.endsWith("/");return u.pathname.endsWith("/")||!d&&!f||(u.pathname+="/"),u}const I=e=>e.join("/").replace(/\/\/+/g,"/"),U=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),M=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",B=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function q(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const H=["post","put","patch","delete"],V=(new Set(H),["get",...H]);new Set(V),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)}const Q=r.createContext(null);const K=r.createContext(null);const $=r.createContext(null);const G=r.createContext(null);const J=r.createContext({outlet:null,matches:[],isDataRoute:!1});const Y=r.createContext(null);function X(){return null!=r.useContext(G)}function Z(){return X()||c(!1),r.useContext(G).location}function ee(e){r.useContext($).static||r.useLayoutEffect(e)}function te(){let{isDataRoute:e}=r.useContext(J);return e?function(){let{router:e}=fe(ce.UseNavigateStable),t=he(de.UseNavigateStable),n=r.useRef(!1);return ee(()=>{n.current=!0}),r.useCallback(function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,W({fromRouteId:t},a)))},[e,t])}():function(){X()||c(!1);let e=r.useContext(Q),{basename:t,future:n,navigator:a}=r.useContext($),{matches:o}=r.useContext(J),{pathname:i}=Z(),s=JSON.stringify(D(o,n.v7_relativeSplatPath)),l=r.useRef(!1);ee(()=>{l.current=!0});let u=r.useCallback(function(n,r){if(void 0===r&&(r={}),!l.current)return;if("number"===typeof n)return void a.go(n);let o=F(n,JSON.parse(s),i,"path"===r.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:I([t,o.pathname])),(r.replace?a.replace:a.push)(o,r.state,r)},[t,a,s,i,e]);return u}()}const ne=r.createContext(null);function re(e,t){let{relative:n}=void 0===t?{}:t,{future:a}=r.useContext($),{matches:o}=r.useContext(J),{pathname:i}=Z(),s=JSON.stringify(D(o,a.v7_relativeSplatPath));return r.useMemo(()=>F(e,JSON.parse(s),i,"path"===n),[e,s,i,n])}function ae(e,n,a,o){X()||c(!1);let{navigator:i}=r.useContext($),{matches:s}=r.useContext(J),l=s[s.length-1],u=l?l.params:{},d=(l&&l.pathname,l?l.pathnameBase:"/");l&&l.route;let f,p=Z();if(n){var h;let e="string"===typeof n?m(n):n;"/"===d||(null==(h=e.pathname)?void 0:h.startsWith(d))||c(!1),f=e}else f=p;let g=f.pathname||"/",v=g;if("/"!==d){let e=d.replace(/^\//,"").split("/");v="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=y(e,{pathname:v});let x=ue(b&&b.map(e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:I([d,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:I([d,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),s,a,o);return n&&x?r.createElement(G.Provider,{value:{location:W({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:t.Pop}},x):x}function oe(){let e=function(){var e;let t=r.useContext(Y),n=pe(de.UseRouteError),a=he(de.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[a]}(),t=q(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:a};return r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),n?r.createElement("pre",{style:o},n):null,null)}const ie=r.createElement(oe,null);class se extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(J.Provider,{value:this.props.routeContext},r.createElement(Y.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function le(e){let{routeContext:t,match:n,children:a}=e,o=r.useContext(Q);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),r.createElement(J.Provider,{value:t},a)}function ue(e,t,n,a){var o;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===a&&(a=null),null==e){var i;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(i=a)&&i.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,l=null==(o=n)?void 0:o.errors;if(null!=l){let e=s.findIndex(e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id]));e>=0||c(!1),s=s.slice(0,Math.min(s.length,e+1))}let u=!1,d=-1;if(n&&a&&a.v7_partialHydration)for(let r=0;r<s.length;r++){let e=s[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(d=r),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,s=d>=0?s.slice(0,d+1):[s[0]];break}}}return s.reduceRight((e,a,o)=>{let i,c=!1,f=null,p=null;var h;n&&(i=l&&a.route.id?l[a.route.id]:void 0,f=a.route.errorElement||ie,u&&(d<0&&0===o?(h="route-fallback",!1||me[h]||(me[h]=!0),c=!0,p=null):d===o&&(c=!0,p=a.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,o+1)),g=()=>{let t;return t=i?f:c?p:a.route.Component?r.createElement(a.route.Component,null):a.route.element?a.route.element:e,r.createElement(le,{match:a,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(a.route.ErrorBoundary||a.route.errorElement||0===o)?r.createElement(se,{location:n.location,revalidation:n.revalidation,component:f,error:i,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()},null)}var ce=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ce||{}),de=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(de||{});function fe(e){let t=r.useContext(Q);return t||c(!1),t}function pe(e){let t=r.useContext(K);return t||c(!1),t}function he(e){let t=function(){let e=r.useContext(J);return e||c(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||c(!1),n.route.id}const me={};function ge(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}a.startTransition;function ve(e){let{to:t,replace:n,state:a,relative:o}=e;X()||c(!1);let{future:i,static:s}=r.useContext($),{matches:l}=r.useContext(J),{pathname:u}=Z(),d=te(),f=F(t,D(l,i.v7_relativeSplatPath),u,"path"===o),p=JSON.stringify(f);return r.useEffect(()=>d(JSON.parse(p),{replace:n,state:a,relative:o}),[d,p,o,n,a]),null}function ye(e){return function(e){let t=r.useContext(J).outlet;return t?r.createElement(ne.Provider,{value:e},t):t}(e.context)}function be(e){c(!1)}function xe(e){let{basename:n="/",children:a=null,location:o,navigationType:i=t.Pop,navigator:s,static:l=!1,future:u}=e;X()&&c(!1);let d=n.replace(/^\/*/,"/"),f=r.useMemo(()=>({basename:d,navigator:s,static:l,future:W({v7_relativeSplatPath:!1},u)}),[d,u,s,l]);"string"===typeof o&&(o=m(o));let{pathname:p="/",search:h="",hash:g="",state:v=null,key:y="default"}=o,b=r.useMemo(()=>{let e=A(p,d);return null==e?null:{location:{pathname:e,search:h,hash:g,state:v,key:y},navigationType:i}},[d,p,h,g,v,y,i]);return null==b?null:r.createElement($.Provider,{value:f},r.createElement(G.Provider,{children:a,value:b}))}function we(e){let{children:t,location:n}=e;return ae(ke(t),n)}new Promise(()=>{});r.Component;function ke(e,t){void 0===t&&(t=[]);let n=[];return r.Children.forEach(e,(e,a)=>{if(!r.isValidElement(e))return;let o=[...t,a];if(e.type===r.Fragment)return void n.push.apply(n,ke(e.props.children,o));e.type!==be&&c(!1),e.props.index&&e.props.children&&c(!1);let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=ke(e.props.children,o)),n.push(i)}),n}function Se(){return Se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Se.apply(this,arguments)}function je(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ne=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(Wi){}new Map;const Ee=a.startTransition;s.flushSync,a.useId;function Oe(e){let{basename:t,children:n,future:a,window:o}=e,i=r.useRef();var s;null==i.current&&(i.current=(void 0===(s={window:o,v5Compat:!0})&&(s={}),g(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return p("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:h(t)},null,s)));let l=i.current,[u,c]=r.useState({action:l.action,location:l.location}),{v7_startTransition:d}=a||{},f=r.useCallback(e=>{d&&Ee?Ee(()=>c(e)):c(e)},[c,d]);return r.useLayoutEffect(()=>l.listen(f),[l,f]),r.useEffect(()=>ge(a),[a]),r.createElement(xe,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:l,future:a})}const Pe="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Ce=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,_e=r.forwardRef(function(e,t){let n,{onClick:a,relative:o,reloadDocument:i,replace:s,state:l,target:u,to:d,preventScrollReset:f,viewTransition:p}=e,m=je(e,Ne),{basename:g}=r.useContext($),v=!1;if("string"===typeof d&&Ce.test(d)&&(n=d,Pe))try{let e=new URL(window.location.href),t=d.startsWith("//")?new URL(e.protocol+d):new URL(d),n=A(t.pathname,g);t.origin===e.origin&&null!=n?d=n+t.search+t.hash:v=!0}catch(Wi){}let y=function(e,t){let{relative:n}=void 0===t?{}:t;X()||c(!1);let{basename:a,navigator:o}=r.useContext($),{hash:i,pathname:s,search:l}=re(e,{relative:n}),u=s;return"/"!==a&&(u="/"===s?a:I([a,s])),o.createHref({pathname:u,search:l,hash:i})}(d,{relative:o}),b=function(e,t){let{target:n,replace:a,state:o,preventScrollReset:i,relative:s,viewTransition:l}=void 0===t?{}:t,u=te(),c=Z(),d=re(e,{relative:s});return r.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==a?a:h(c)===h(d);u(e,{replace:n,state:o,preventScrollReset:i,relative:s,viewTransition:l})}},[c,u,d,a,o,n,e,i,s,l])}(d,{replace:s,state:l,target:u,preventScrollReset:f,relative:o,viewTransition:p});return r.createElement("a",Se({},m,{href:n||y,onClick:v||i?a:function(e){a&&a(e),e.defaultPrevented||b(e)},ref:t,target:u}))});var Te,Re;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Te||(Te={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Re||(Re={}));function Ae(e){return Ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ae(e)}function Le(e){var t=function(e,t){if("object"!=Ae(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ae(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ae(t)?t:t+""}function ze(e,t,n){return(t=Le(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function De(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?De(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):De(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ie(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ue}=Object.prototype,{getPrototypeOf:Me}=Object,{iterator:Be,toStringTag:qe}=Symbol,He=(Ve=Object.create(null),e=>{const t=Ue.call(e);return Ve[t]||(Ve[t]=t.slice(8,-1).toLowerCase())});var Ve;const We=e=>(e=e.toLowerCase(),t=>He(t)===e),Qe=e=>t=>typeof t===e,{isArray:Ke}=Array,$e=Qe("undefined");function Ge(e){return null!==e&&!$e(e)&&null!==e.constructor&&!$e(e.constructor)&&Xe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Je=We("ArrayBuffer");const Ye=Qe("string"),Xe=Qe("function"),Ze=Qe("number"),et=e=>null!==e&&"object"===typeof e,tt=e=>{if("object"!==He(e))return!1;const t=Me(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(qe in e)&&!(Be in e)},nt=We("Date"),rt=We("File"),at=We("Blob"),ot=We("FileList"),it=We("URLSearchParams"),[st,lt,ut,ct]=["ReadableStream","Request","Response","Headers"].map(We);function dt(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Ke(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{if(Ge(e))return;const r=a?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let i;for(n=0;n<o;n++)i=r[n],t.call(null,e[i],i,e)}}function ft(e,t){if(Ge(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const pt="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,ht=e=>!$e(e)&&e!==pt;const mt=(gt="undefined"!==typeof Uint8Array&&Me(Uint8Array),e=>gt&&e instanceof gt);var gt;const vt=We("HTMLFormElement"),yt=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),bt=We("RegExp"),xt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};dt(n,(n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)}),Object.defineProperties(e,r)};const wt=We("AsyncFunction"),kt=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],pt.addEventListener("message",e=>{let{source:t,data:a}=e;t===pt&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),pt.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,Xe(pt.postMessage)),St="undefined"!==typeof queueMicrotask?queueMicrotask.bind(pt):"undefined"!==typeof process&&process.nextTick||kt,jt={isArray:Ke,isArrayBuffer:Je,isBuffer:Ge,isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Xe(e.append)&&("formdata"===(t=He(e))||"object"===t&&Xe(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Je(e.buffer),t},isString:Ye,isNumber:Ze,isBoolean:e=>!0===e||!1===e,isObject:et,isPlainObject:tt,isEmptyObject:e=>{if(!et(e)||Ge(e))return!1;try{return 0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype}catch(Wi){return!1}},isReadableStream:st,isRequest:lt,isResponse:ut,isHeaders:ct,isUndefined:$e,isDate:nt,isFile:rt,isBlob:at,isRegExp:bt,isFunction:Xe,isStream:e=>et(e)&&Xe(e.pipe),isURLSearchParams:it,isTypedArray:mt,isFileList:ot,forEach:dt,merge:function e(){const{caseless:t}=ht(this)&&this||{},n={},r=(r,a)=>{const o=t&&ft(n,a)||a;tt(n[o])&&tt(r)?n[o]=e(n[o],r):tt(r)?n[o]=e({},r):Ke(r)?n[o]=r.slice():n[o]=r};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&dt(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return dt(t,(t,r)=>{n&&Xe(t)?e[r]=Ie(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,i;const s={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],r&&!r(i,e,t)||s[i]||(t[i]=e[i],s[i]=!0);e=!1!==n&&Me(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:He,kindOfTest:We,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Ke(e))return e;let t=e.length;if(!Ze(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Be]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:vt,hasOwnProperty:yt,hasOwnProp:yt,reduceDescriptors:xt,freezeMethods:e=>{xt(e,(t,n)=>{if(Xe(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Xe(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return Ke(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:ft,global:pt,isContextDefined:ht,isSpecCompliantForm:function(e){return!!(e&&Xe(e.append)&&"FormData"===e[qe]&&e[Be])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(et(e)){if(t.indexOf(e)>=0)return;if(Ge(e))return e;if(!("toJSON"in e)){t[r]=e;const a=Ke(e)?[]:{};return dt(e,(e,t)=>{const o=n(e,r+1);!$e(o)&&(a[t]=o)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:wt,isThenable:e=>e&&(et(e)||Xe(e))&&Xe(e.then)&&Xe(e.catch),setImmediate:kt,asap:St,isIterable:e=>null!=e&&Xe(e[Be])};function Nt(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}jt.inherits(Nt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:jt.toJSONObject(this.config),code:this.code,status:this.status}}});const Et=Nt.prototype,Ot={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ot[e]={value:e}}),Object.defineProperties(Nt,Ot),Object.defineProperty(Et,"isAxiosError",{value:!0}),Nt.from=(e,t,n,r,a,o)=>{const i=Object.create(Et);return jt.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Nt.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Pt=Nt;function Ct(e){return jt.isPlainObject(e)||jt.isArray(e)}function _t(e){return jt.endsWith(e,"[]")?e.slice(0,-2):e}function Tt(e,t,n){return e?e.concat(t).map(function(e,t){return e=_t(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Rt=jt.toFlatObject(jt,{},null,function(e){return/^is[A-Z]/.test(e)});const At=function(e,t,n){if(!jt.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=jt.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!jt.isUndefined(t[e])})).metaTokens,a=n.visitor||u,o=n.dots,i=n.indexes,s=(n.Blob||"undefined"!==typeof Blob&&Blob)&&jt.isSpecCompliantForm(t);if(!jt.isFunction(a))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(jt.isDate(e))return e.toISOString();if(jt.isBoolean(e))return e.toString();if(!s&&jt.isBlob(e))throw new Pt("Blob is not supported. Use a Buffer instead.");return jt.isArrayBuffer(e)||jt.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let s=e;if(e&&!a&&"object"===typeof e)if(jt.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(jt.isArray(e)&&function(e){return jt.isArray(e)&&!e.some(Ct)}(e)||(jt.isFileList(e)||jt.endsWith(n,"[]"))&&(s=jt.toArray(e)))return n=_t(n),s.forEach(function(e,r){!jt.isUndefined(e)&&null!==e&&t.append(!0===i?Tt([n],r,o):null===i?n:n+"[]",l(e))}),!1;return!!Ct(e)||(t.append(Tt(a,n,o),l(e)),!1)}const c=[],d=Object.assign(Rt,{defaultVisitor:u,convertValue:l,isVisitable:Ct});if(!jt.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!jt.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),jt.forEach(n,function(n,o){!0===(!(jt.isUndefined(n)||null===n)&&a.call(t,n,jt.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])}),c.pop()}}(e),t};function Lt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function zt(e,t){this._pairs=[],e&&At(e,this,t)}const Dt=zt.prototype;Dt.append=function(e,t){this._pairs.push([e,t])},Dt.toString=function(e){const t=e?function(t){return e.call(this,t,Lt)}:Lt;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const Ft=zt;function It(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ut(e,t,n){if(!t)return e;const r=n&&n.encode||It;jt.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):jt.isURLSearchParams(t)?t.toString():new Ft(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const Mt=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){jt.forEach(this.handlers,function(t){null!==t&&e(t)})}},Bt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},qt={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Ft,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Ht="undefined"!==typeof window&&"undefined"!==typeof document,Vt="object"===typeof navigator&&navigator||void 0,Wt=Ht&&(!Vt||["ReactNative","NativeScript","NS"].indexOf(Vt.product)<0),Qt="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Kt=Ht&&window.location.href||"http://localhost",$t=Fe(Fe({},e),qt);const Gt=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const i=Number.isFinite(+o),s=a>=e.length;if(o=!o&&jt.isArray(r)?r.length:o,s)return jt.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!i;r[o]&&jt.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],a)&&jt.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!i}if(jt.isFormData(e)&&jt.isFunction(e.entries)){const n={};return jt.forEachEntry(e,(e,r)=>{t(function(e){return jt.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const Jt={transitional:Bt,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=jt.isObject(e);a&&jt.isHTMLForm(e)&&(e=new FormData(e));if(jt.isFormData(e))return r?JSON.stringify(Gt(e)):e;if(jt.isArrayBuffer(e)||jt.isBuffer(e)||jt.isStream(e)||jt.isFile(e)||jt.isBlob(e)||jt.isReadableStream(e))return e;if(jt.isArrayBufferView(e))return e.buffer;if(jt.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return At(e,new $t.classes.URLSearchParams,Fe({visitor:function(e,t,n,r){return $t.isNode&&jt.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=jt.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return At(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(jt.isString(e))try{return(t||JSON.parse)(e),jt.trim(e)}catch(Wi){if("SyntaxError"!==Wi.name)throw Wi}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Jt.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(jt.isResponse(e)||jt.isReadableStream(e))return e;if(e&&jt.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(Wi){if(n){if("SyntaxError"===Wi.name)throw Pt.from(Wi,Pt.ERR_BAD_RESPONSE,this,null,this.response);throw Wi}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:$t.classes.FormData,Blob:$t.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};jt.forEach(["delete","get","head","post","put","patch"],e=>{Jt.headers[e]={}});const Yt=Jt,Xt=jt.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Zt=Symbol("internals");function en(e){return e&&String(e).trim().toLowerCase()}function tn(e){return!1===e||null==e?e:jt.isArray(e)?e.map(tn):String(e)}function nn(e,t,n,r,a){return jt.isFunction(r)?r.call(this,t,n):(a&&(t=n),jt.isString(t)?jt.isString(r)?-1!==t.indexOf(r):jt.isRegExp(r)?r.test(t):void 0:void 0)}class rn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=en(t);if(!a)throw new Error("header name must be a non-empty string");const o=jt.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=tn(e))}const o=(e,t)=>jt.forEach(e,(e,n)=>a(e,n,t));if(jt.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(jt.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Xt[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(jt.isObject(e)&&jt.isIterable(e)){let n,r,a={};for(const t of e){if(!jt.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?jt.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=en(e)){const n=jt.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(jt.isFunction(t))return t.call(this,e,n);if(jt.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=en(e)){const n=jt.findKey(this,e);return!(!n||void 0===this[n]||t&&!nn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=en(e)){const a=jt.findKey(n,e);!a||t&&!nn(0,n[a],a,t)||(delete n[a],r=!0)}}return jt.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!nn(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return jt.forEach(this,(r,a)=>{const o=jt.findKey(n,a);if(o)return t[o]=tn(r),void delete t[a];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();i!==a&&delete t[a],t[i]=tn(r),n[i]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return jt.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&jt.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[Zt]=this[Zt]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=en(e);t[r]||(!function(e,t){const n=jt.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return jt.isArray(e)?e.forEach(r):r(e),this}}rn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),jt.reduceDescriptors(rn.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),jt.freezeMethods(rn);const an=rn;function on(e,t){const n=this||Yt,r=t||n,a=an.from(r.headers);let o=r.data;return jt.forEach(e,function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function sn(e){return!(!e||!e.__CANCEL__)}function ln(e,t,n){Pt.call(this,null==e?"canceled":e,Pt.ERR_CANCELED,t,n),this.name="CanceledError"}jt.inherits(ln,Pt,{__CANCEL__:!0});const un=ln;function cn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Pt("Request failed with status code "+n.status,[Pt.ERR_BAD_REQUEST,Pt.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const dn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,i=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),u=r[i];a||(a=l),n[o]=s,r[o]=l;let c=i,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===i&&(i=(i+1)%e),l-a<t)return;const f=u&&l-u;return f?Math.round(1e3*d/f):void 0}};const fn=function(e,t){let n,r,a=0,o=1e3/t;const i=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=o,n=null,r&&(clearTimeout(r),r=null),e(...t)};return[function(){const e=Date.now(),t=e-a;for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];t>=o?i(l,e):(n=l,r||(r=setTimeout(()=>{r=null,i(n)},o-t)))},()=>n&&i(n)]},pn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=dn(50,250);return fn(n=>{const o=n.loaded,i=n.lengthComputable?n.total:void 0,s=o-r,l=a(s);r=o;e({loaded:o,total:i,progress:i?o/i:void 0,bytes:s,rate:l||void 0,estimated:l&&i&&o<=i?(i-o)/l:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},hn=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},mn=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return jt.asap(()=>e(...n))},gn=$t.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,$t.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL($t.origin),$t.navigator&&/(msie|trident)/i.test($t.navigator.userAgent)):()=>!0,vn=$t.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const i=[e+"="+encodeURIComponent(t)];jt.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),jt.isString(r)&&i.push("path="+r),jt.isString(a)&&i.push("domain="+a),!0===o&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function yn(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const bn=e=>e instanceof an?Fe({},e):e;function xn(e,t){t=t||{};const n={};function r(e,t,n,r){return jt.isPlainObject(e)&&jt.isPlainObject(t)?jt.merge.call({caseless:r},e,t):jt.isPlainObject(t)?jt.merge({},t):jt.isArray(t)?t.slice():t}function a(e,t,n,a){return jt.isUndefined(t)?jt.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!jt.isUndefined(t))return r(void 0,t)}function i(e,t){return jt.isUndefined(t)?jt.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:s,headers:(e,t,n)=>a(bn(e),bn(t),0,!0)};return jt.forEach(Object.keys(Fe(Fe({},e),t)),function(r){const o=l[r]||a,i=o(e[r],t[r],r);jt.isUndefined(i)&&o!==s||(n[r]=i)}),n}const wn=e=>{const t=xn({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:i,headers:s,auth:l}=t;if(t.headers=s=an.from(s),t.url=Ut(yn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),jt.isFormData(r))if($t.hasStandardBrowserEnv||$t.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...t].join("; "))}if($t.hasStandardBrowserEnv&&(a&&jt.isFunction(a)&&(a=a(t)),a||!1!==a&&gn(t.url))){const e=o&&i&&vn.read(i);e&&s.set(o,e)}return t},kn="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=wn(e);let a=r.data;const o=an.from(r.headers).normalize();let i,s,l,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=an.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());cn(function(e){t(e),h()},function(e){n(e),h()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Pt("Request aborted",Pt.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Pt("Network Error",Pt.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Bt;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Pt(t,a.clarifyTimeoutError?Pt.ETIMEDOUT:Pt.ECONNABORTED,e,m)),m=null},void 0===a&&o.setContentType(null),"setRequestHeader"in m&&jt.forEach(o.toJSON(),function(e,t){m.setRequestHeader(t,e)}),jt.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([l,c]=pn(p,!0),m.addEventListener("progress",l)),f&&m.upload&&([s,u]=pn(f),m.upload.addEventListener("progress",s),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new un(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const v=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);v&&-1===$t.protocols.indexOf(v)?n(new Pt("Unsupported protocol "+v+":",Pt.ERR_BAD_REQUEST,e)):m.send(a||null)})},Sn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Pt?t:new un(t instanceof Error?t.message:t))}};let o=t&&setTimeout(()=>{o=null,a(new Pt("timeout ".concat(t," of ms exceeded"),Pt.ETIMEDOUT))},t);const i=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:s}=r;return s.unsubscribe=()=>jt.asap(i),s}};function jn(e,t){this.v=e,this.k=t}function Nn(e){return function(){return new En(e.apply(this,arguments))}}function En(e){var t,n;function r(t,n){try{var o=e[t](n),i=o.value,s=i instanceof jn;Promise.resolve(s?i.v:i).then(function(n){if(s){var l="return"===t?"return":"next";if(!i.k||n.done)return r(l,n);n=e[l](n).value}a(o.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(o,i){var s={key:e,arg:a,resolve:o,reject:i,next:null};n?n=n.next=s:(t=n=s,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function On(e){return new jn(e,0)}function Pn(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new jn(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Cn(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new _n(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function _n(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return _n=function(e){this.s=e,this.n=e.next},_n.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new _n(e)}En.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},En.prototype.next=function(e){return this._invoke("next",e)},En.prototype.throw=function(e){return this._invoke("throw",e)},En.prototype.return=function(e){return this._invoke("return",e)};const Tn=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Rn=function(){var e=Nn(function*(e,t){var n,r=!1,a=!1;try{for(var o,i=Cn(An(e));r=!(o=yield On(i.next())).done;r=!1){const e=o.value;yield*Pn(Cn(Tn(e,t)))}}catch(s){a=!0,n=s}finally{try{r&&null!=i.return&&(yield On(i.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),An=function(){var e=Nn(function*(e){if(e[Symbol.asyncIterator])return void(yield*Pn(Cn(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield On(t.read());if(e)break;yield n}}finally{yield On(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),Ln=(e,t,n,r)=>{const a=Rn(e,t);let o,i=0,s=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return s(),void e.close();let o=r.byteLength;if(n){let e=i+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw s(t),t}},cancel:e=>(s(e),a.return())},{highWaterMark:2})},zn="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Dn=zn&&"function"===typeof ReadableStream,Fn=zn&&("function"===typeof TextEncoder?(In=new TextEncoder,e=>In.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var In;const Un=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(Wi){return!1}},Mn=Dn&&Un(()=>{let e=!1;const t=new Request($t.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Bn=Dn&&Un(()=>jt.isReadableStream(new Response("").body)),qn={stream:Bn&&(e=>e.body)};var Hn;zn&&(Hn=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!qn[e]&&(qn[e]=jt.isFunction(Hn[e])?t=>t[e]():(t,n)=>{throw new Pt("Response type '".concat(e,"' is not supported"),Pt.ERR_NOT_SUPPORT,n)})}));const Vn=async(e,t)=>{const n=jt.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(jt.isBlob(e))return e.size;if(jt.isSpecCompliantForm(e)){const t=new Request($t.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return jt.isArrayBufferView(e)||jt.isArrayBuffer(e)?e.byteLength:(jt.isURLSearchParams(e)&&(e+=""),jt.isString(e)?(await Fn(e)).byteLength:void 0)})(t):n},Wn=zn&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:i,onDownloadProgress:s,onUploadProgress:l,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=wn(e);u=u?(u+"").toLowerCase():"text";let p,h=Sn([a,o&&o.toAbortSignal()],i);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(l&&Mn&&"get"!==n&&"head"!==n&&0!==(g=await Vn(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(jt.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=hn(g,pn(mn(l)));r=Ln(n.body,65536,e,t)}}jt.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,Fe(Fe({},f),{},{signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let o=await fetch(p,f);const i=Bn&&("stream"===u||"response"===u);if(Bn&&(s||i&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=o[t]});const t=jt.toFiniteNumber(o.headers.get("content-length")),[n,r]=s&&hn(t,pn(mn(s),!0))||[];o=new Response(Ln(o.body,65536,n,()=>{r&&r(),m&&m()}),e)}u=u||"text";let v=await qn[jt.findKey(qn,u)||"text"](o,e);return!i&&m&&m(),await new Promise((t,n)=>{cn(t,n,{data:v,headers:an.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})})}catch(v){if(m&&m(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new Pt("Network Error",Pt.ERR_NETWORK,e,p),{cause:v.cause||v});throw Pt.from(v,v&&v.code,e,p)}}),Qn={http:null,xhr:kn,fetch:Wn};jt.forEach(Qn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(Wi){}Object.defineProperty(e,"adapterName",{value:t})}});const Kn=e=>"- ".concat(e),$n=e=>jt.isFunction(e)||null===e||!1===e,Gn=e=>{e=jt.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!$n(n)&&(r=Qn[(t=String(n)).toLowerCase()],void 0===r))throw new Pt("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(Kn).join("\n"):" "+Kn(e[0]):"as no adapter specified";throw new Pt("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Jn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new un(null,e)}function Yn(e){Jn(e),e.headers=an.from(e.headers),e.data=on.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Gn(e.adapter||Yt.adapter)(e).then(function(t){return Jn(e),t.data=on.call(e,e.transformResponse,t),t.headers=an.from(t.headers),t},function(t){return sn(t)||(Jn(e),t&&t.response&&(t.response.data=on.call(e,e.transformResponse,t.response),t.response.headers=an.from(t.response.headers))),Promise.reject(t)})}const Xn="1.11.0",Zn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Zn[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const er={};Zn.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Xn+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new Pt(r(a," has been removed"+(t?" in "+t:"")),Pt.ERR_DEPRECATED);return t&&!er[a]&&(er[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},Zn.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const tr={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Pt("options must be an object",Pt.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],i=t[o];if(i){const t=e[o],n=void 0===t||i(t,o,e);if(!0!==n)throw new Pt("option "+o+" must be "+n,Pt.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new Pt("Unknown option "+o,Pt.ERR_BAD_OPTION)}},validators:Zn},nr=tr.validators;class rr{constructor(e){this.defaults=e||{},this.interceptors={request:new Mt,response:new Mt}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(Wi){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=xn(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&tr.assertOptions(n,{silentJSONParsing:nr.transitional(nr.boolean),forcedJSONParsing:nr.transitional(nr.boolean),clarifyTimeoutError:nr.transitional(nr.boolean)},!1),null!=r&&(jt.isFunction(r)?t.paramsSerializer={serialize:r}:tr.assertOptions(r,{encode:nr.function,serialize:nr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tr.assertOptions(t,{baseUrl:nr.spelling("baseURL"),withXsrfToken:nr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&jt.merge(a.common,a[t.method]);a&&jt.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=an.concat(o,a);const i=[];let s=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const l=[];let u;this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let c,d=0;if(!s){const e=[Yn.bind(this),void 0];for(e.unshift(...i),e.push(...l),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=i.length;let f=t;for(d=0;d<c;){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=Yn.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=l.length;d<c;)u=u.then(l[d++],l[d++]);return u}getUri(e){return Ut(yn((e=xn(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}jt.forEach(["delete","get","head","options"],function(e){rr.prototype[e]=function(t,n){return this.request(xn(n||{},{method:e,url:t,data:(n||{}).data}))}}),jt.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(xn(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}rr.prototype[e]=t(),rr.prototype[e+"Form"]=t(!0)});const ar=rr;class or{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new un(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new or(function(t){e=t});return{token:t,cancel:e}}}const ir=or;const sr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(sr).forEach(e=>{let[t,n]=e;sr[n]=t});const lr=sr;const ur=function e(t){const n=new ar(t),r=Ie(ar.prototype.request,n);return jt.extend(r,ar.prototype,n,{allOwnKeys:!0}),jt.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(xn(t,n))},r}(Yt);ur.Axios=ar,ur.CanceledError=un,ur.CancelToken=ir,ur.isCancel=sn,ur.VERSION=Xn,ur.toFormData=At,ur.AxiosError=Pt,ur.Cancel=ur.CanceledError,ur.all=function(e){return Promise.all(e)},ur.spread=function(e){return function(t){return e.apply(null,t)}},ur.isAxiosError=function(e){return jt.isObject(e)&&!0===e.isAxiosError},ur.mergeConfig=xn,ur.AxiosHeaders=an,ur.formToJSON=e=>Gt(jt.isHTMLForm(e)?new FormData(e):e),ur.getAdapter=Gn,ur.HttpStatusCode=lr,ur.default=ur;const cr=ur;const dr=new class{constructor(){this.instance=void 0,this.baseURL=void 0,this.baseURL="/api/v1",this.instance=cr.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.instance.interceptors.request.use(e=>{var t;const n=localStorage.getItem("auth_token");return n&&(e.headers.Authorization="Bearer ".concat(n)),e.metadata={startTime:new Date},console.log("\ud83d\ude80 ".concat(null===(t=e.method)||void 0===t?void 0:t.toUpperCase()," ").concat(e.url),{data:e.data,params:e.params}),e},e=>(console.error("\u274c Request Error:",e),Promise.reject(e))),this.instance.interceptors.response.use(e=>{var t,n,r;const a=(new Date).getTime()-(null===(t=e.config.metadata)||void 0===t||null===(n=t.startTime)||void 0===n?void 0:n.getTime());return console.log("\u2705 ".concat(null===(r=e.config.method)||void 0===r?void 0:r.toUpperCase()," ").concat(e.config.url," (").concat(a,"ms)"),{status:e.status,data:e.data}),e},e=>{var t,n,r,a,o,i,s,l;const u=null!==(t=e.config)&&void 0!==t&&null!==(n=t.metadata)&&void 0!==n&&n.startTime?(new Date).getTime()-e.config.metadata.startTime.getTime():0;return console.error("\u274c ".concat(null===(r=e.config)||void 0===r||null===(a=r.method)||void 0===a?void 0:a.toUpperCase()," ").concat(null===(o=e.config)||void 0===o?void 0:o.url," (").concat(u,"ms)"),{status:null===(i=e.response)||void 0===i?void 0:i.status,data:null===(s=e.response)||void 0===s?void 0:s.data,message:e.message}),401===(null===(l=e.response)||void 0===l?void 0:l.status)&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(this.handleError(e))})}handleError(e){var t,n;return e.response?{error:(null===(t=e.response.data)||void 0===t?void 0:t.error)||"Server Error",status:e.response.status,message:(null===(n=e.response.data)||void 0===n?void 0:n.message)||e.message,details:e.response.data}:e.request?{error:"Network Error",status:0,message:"No response from server. Please check your internet connection.",details:e.request}:{error:"Request Error",status:0,message:e.message||"An unexpected error occurred",details:e}}transformResponse(e){return{data:e.data.data||e.data,status:e.status,message:e.data.message,success:e.status>=200&&e.status<300}}async get(e,t){const n=await this.instance.get(e,{params:null===t||void 0===t?void 0:t.params,headers:null===t||void 0===t?void 0:t.headers,timeout:null===t||void 0===t?void 0:t.timeout});return this.transformResponse(n)}async post(e,t,n){const r=await this.instance.post(e,t,{headers:null===n||void 0===n?void 0:n.headers,timeout:null===n||void 0===n?void 0:n.timeout});return this.transformResponse(r)}async put(e,t,n){const r=await this.instance.put(e,t,{headers:null===n||void 0===n?void 0:n.headers,timeout:null===n||void 0===n?void 0:n.timeout});return this.transformResponse(r)}async delete(e,t){const n=await this.instance.delete(e,{headers:null===t||void 0===t?void 0:t.headers,timeout:null===t||void 0===t?void 0:t.timeout});return this.transformResponse(n)}async patch(e,t,n){const r=await this.instance.patch(e,t,{headers:null===n||void 0===n?void 0:n.headers,timeout:null===n||void 0===n?void 0:n.timeout});return this.transformResponse(r)}async upload(e,t,n){const r=new FormData;r.append("file",t);const a=await this.instance.post(e,r,{headers:Fe({"Content-Type":"multipart/form-data"},null===n||void 0===n?void 0:n.headers),timeout:(null===n||void 0===n?void 0:n.timeout)||6e4});return this.transformResponse(a)}setAuthToken(e){this.instance.defaults.headers.common.Authorization="Bearer ".concat(e),localStorage.setItem("auth_token",e)}clearAuthToken(){delete this.instance.defaults.headers.common.Authorization,localStorage.removeItem("auth_token")}getBaseURL(){return this.baseURL}updateBaseURL(e){this.baseURL=e,this.instance.defaults.baseURL=e}},fr=dr,pr={auth:{login:"/login",register:"/register",logout:"/logout",refresh:"/refresh",forgotPassword:"/forgot-password",resetPassword:"/reset-password",verifyOTP:"/verify-otp",profile:"/user/profile",updateProfile:"/user/profile",changePassword:"/user/password",updateNotifications:"/user/notifications",updatePrivacy:"/social/privacy"},user:{profile:"/user/profile",updateProfile:"/user/profile",changePassword:"/user/password",deactivate:"/user/deactivate",delete:"/user/delete",verifyEmail:"/user/verify-email",resendVerification:"/user/resend-verification"},quiz:{list:"/quiz",detail:e=>"/quiz/".concat(e),create:"/quiz",update:e=>"/quiz/".concat(e),delete:e=>"/quiz/".concat(e),search:"/quiz/search",bySubject:e=>"/quiz/subject/".concat(e),popular:"/quiz/popular",featured:"/quiz/featured",questions:e=>"/quiz/".concat(e,"/questions"),statistics:e=>"/quiz/".concat(e,"/statistics"),leaderboard:e=>"/quiz/".concat(e,"/leaderboard"),start:e=>"/quiz/".concat(e,"/start"),submitAnswer:e=>"/quiz/sessions/".concat(e,"/answer"),finish:e=>"/quiz/sessions/".concat(e,"/finish"),results:e=>"/quiz/results/".concat(e),userResults:"/quiz/results",share:e=>"/quiz/".concat(e,"/share"),invite:e=>"/quiz/".concat(e,"/invite"),shared:"/quiz/shared",favorites:"/quiz/favorites",addToFavorites:e=>"/quiz/".concat(e,"/favorite"),removeFromFavorites:e=>"/quiz/".concat(e,"/favorite")},progress:{update:"/progress",content:e=>"/progress/content/".concat(e),all:"/progress",stats:"/progress/stats",history:"/progress/history",streak:"/progress/streak",sessions:"/progress/sessions",startSession:"/progress/sessions",endSession:e=>"/progress/sessions/".concat(e,"/end"),goals:"/progress/goals",createGoal:"/progress/goals",updateGoal:e=>"/progress/goals/".concat(e),deleteGoal:e=>"/progress/goals/".concat(e),achievements:"/progress/achievements"},social:{friends:{request:"/social/friends/request",respond:"/social/friends/request/respond",requests:e=>"/social/friends/requests/".concat(e),list:"/social/friends",remove:e=>"/social/friends/".concat(e)},follow:{follow:"/social/follow",unfollow:e=>"/social/follow/".concat(e),followers:"/social/followers",following:"/social/following"},search:{users:"/social/search/users",suggestions:"/social/suggestions/friends",mutualFriends:e=>"/social/mutual-friends/".concat(e)},block:{block:"/social/block",unblock:e=>"/social/block/".concat(e),list:"/social/blocked"},activity:"/social/activity",profile:e=>"/social/profile/".concat(e),stats:"/social/stats",privacy:"/social/privacy"},timeline:{me:"/timeline",feed:"/timeline/feed",public:"/timeline/public",create:"/timeline",update:e=>"/timeline/".concat(e),delete:e=>"/timeline/".concat(e),like:e=>"/timeline/".concat(e,"/like"),comments:e=>"/timeline/".concat(e,"/comments"),comment:e=>"/timeline/comments/".concat(e),stats:"/timeline/stats",summary:"/timeline/summary",privacy:"/timeline/privacy",engagement:e=>"/timeline/".concat(e,"/engagement")},content:{list:"/content",detail:e=>"/content/".concat(e),create:"/content",update:e=>"/content/".concat(e),delete:e=>"/content/".concat(e),search:"/content/search",bySubject:e=>"/content/subject/".concat(e),featured:"/content/featured",library:"/content/library",addToLibrary:e=>"/content/".concat(e,"/library"),removeFromLibrary:e=>"/content/".concat(e,"/library"),progress:e=>"/content/".concat(e,"/progress"),updateProgress:"/content/progress",recommended:"/content/recommended"},notifications:{list:"/notifications",unread:"/notifications/unread",markAsRead:e=>"/notifications/".concat(e,"/read"),markAllAsRead:"/notifications/read-all",delete:e=>"/notifications/".concat(e),settings:"/notifications/settings",updateSettings:"/notifications/settings"},upload:{avatar:"/upload/avatar",file:"/upload/file",image:"/upload/image"}};const hr=new class{async login(e){let t=pr.auth.login;e.email?t="/login/email":e.phone?t="/login/phone":e.username&&(t="/login/username");const n=await fr.post(t,e);return n.success&&n.data.token&&this.setAuthData(n.data),n.data}async loginWithUsername(e){const t=await fr.post("/login/username",e);return t.success&&t.data.token&&this.setAuthData(t.data),t.data}async loginWithEmail(e){const t=await fr.post("/login/email",e);return t.success&&t.data.token&&this.setAuthData(t.data),t.data}async loginWithPhone(e){const t=await fr.post("/login/phone",e);return t.success&&t.data.token&&this.setAuthData(t.data),t.data}async loginWithGoogle(e){const t=await fr.post("/login/google",e);return t.success&&t.data.token&&this.setAuthData(t.data),t.data}async loginWithApple(e){const t=await fr.post("/login/apple",e);return t.success&&t.data.token&&this.setAuthData(t.data),t.data}async register(e){return(await fr.post(pr.auth.register,e)).data}async logout(){this.clearAuthData()}async forgotPassword(e){return(await fr.post(pr.auth.forgotPassword,e)).data}async resetPassword(e){return(await fr.post(pr.auth.resetPassword,e)).data}async verifyOTP(e){return(await fr.post(pr.auth.verifyOTP,e)).data}async getProfile(){var e;const t=await fr.get(pr.user.profile);return null!==(e=t.data.user)&&void 0!==e?e:t.data}async updateProfile(e){const t={};void 0!==e.name&&(t.name=e.name),void 0!==e.bio&&(t.bio=e.bio);const n=await fr.put(pr.user.updateProfile,t),r=this.getCurrentUser();if(r){const e=Fe(Fe({},r),n.data);localStorage.setItem("user",JSON.stringify(e))}return n.data}async changePassword(e){return(await fr.put(pr.user.changePassword,{current_password:e.current_password,new_password:e.new_password})).data}async updateNotificationSettings(e){const t=e.notification_settings?{push_notification_enabled:e.notification_settings.push_notifications,email_notification_enabled:e.notification_settings.email_notifications}:e,n=await fr.put(pr.auth.updateNotifications,t),r=this.getCurrentUser();if(r){const e=Fe(Fe({},r),n.data);localStorage.setItem("user",JSON.stringify(e))}return n.data}async updatePrivacySettings(e){const t=e.privacy_settings,n=t?{profile_visibility:t.profile_visibility,allow_friend_requests:t.allow_friend_requests,show_online_status:t.show_online_status}:e,r=await fr.put(pr.auth.updatePrivacy,n),a=this.getCurrentUser();if(a){const e=Fe(Fe({},a),r.data);localStorage.setItem("user",JSON.stringify(e))}return r.data}setAuthData(e){localStorage.setItem("auth_token",e.token),localStorage.setItem("user",JSON.stringify(e.user)),localStorage.setItem("token_expires",e.expires),fr.setAuthToken(e.token)}clearAuthData(){localStorage.removeItem("auth_token"),localStorage.removeItem("user"),localStorage.removeItem("token_expires"),fr.clearAuthToken()}getCurrentUser(){const e=localStorage.getItem("user");if(e)try{return JSON.parse(e)}catch(t){return console.error("Error parsing user data:",t),this.clearAuthData(),null}return null}getCurrentToken(){return localStorage.getItem("auth_token")}isAuthenticated(){const e=this.getCurrentToken(),t=this.getCurrentUser(),n=localStorage.getItem("token_expires");if(!e||!t||!n)return!1;const r=new Date(n);return!(new Date>=r)||(this.clearAuthData(),!1)}isTokenExpired(){const e=localStorage.getItem("token_expires");if(!e)return!0;const t=new Date(e);return new Date>=t}getTokenExpirationTime(){const e=localStorage.getItem("token_expires");return e?new Date(e):null}initializeAuth(){const e=this.getCurrentToken();e&&this.isAuthenticated()?fr.setAuthToken(e):this.clearAuthData()}},mr=hr;var gr=n(579);const vr={user:null,token:null,isAuthenticated:!1,isLoading:!0,error:null},yr=(e,t)=>{switch(t.type){case"AUTH_START":return Fe(Fe({},e),{},{isLoading:!0,error:null});case"AUTH_SUCCESS":return Fe(Fe({},e),{},{user:t.payload.user,token:t.payload.token,isAuthenticated:!0,isLoading:!1,error:null});case"AUTH_FAILURE":return Fe(Fe({},e),{},{user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t.payload});case"AUTH_LOGOUT":return Fe(Fe({},e),{},{user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null});case"UPDATE_USER":return Fe(Fe({},e),{},{user:t.payload});case"CLEAR_ERROR":return Fe(Fe({},e),{},{error:null});case"SET_LOADING":return Fe(Fe({},e),{},{isLoading:t.payload});default:return e}},br=(0,r.createContext)(void 0),xr=e=>{let{children:t}=e;const[n,a]=(0,r.useReducer)(yr,vr);(0,r.useEffect)(()=>{(async()=>{try{if(a({type:"SET_LOADING",payload:!0}),mr.initializeAuth(),mr.isAuthenticated()){const e=mr.getCurrentUser(),t=mr.getCurrentToken();a(e&&t?{type:"AUTH_SUCCESS",payload:{user:e,token:t}}:{type:"AUTH_LOGOUT"})}else a({type:"AUTH_LOGOUT"})}catch(e){console.error("Auth initialization error:",e),a({type:"AUTH_LOGOUT"})}finally{a({type:"SET_LOADING",payload:!1})}})()},[]);const o={user:n.user,token:n.token,isAuthenticated:n.isAuthenticated,isLoading:n.isLoading,error:n.error,login:async e=>{try{a({type:"AUTH_START"});const t=await mr.login(e);if(!t.is_succeeded)throw new Error("Login failed");if(t.user.is_admin)throw a({type:"AUTH_LOGOUT"}),new Error("Admin users should use admin login page");a({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(t){const e=t.message||"Login failed. Please try again.";throw a({type:"AUTH_FAILURE",payload:e}),t}},register:async e=>{try{a({type:"AUTH_START"}),await mr.register(e),a({type:"SET_LOADING",payload:!1})}catch(t){const e=t.message||"Registration failed. Please try again.";throw a({type:"AUTH_FAILURE",payload:e}),t}},logout:async()=>{try{await mr.logout()}catch(e){console.error("Logout error:",e)}finally{a({type:"AUTH_LOGOUT"})}},updateProfile:async e=>{try{const t=await mr.updateProfile(e);a({type:"UPDATE_USER",payload:t})}catch(t){const e=t.message||"Profile update failed. Please try again.";throw a({type:"AUTH_FAILURE",payload:e}),t}},changePassword:async e=>{try{await mr.changePassword(e)}catch(t){const e=t.message||"Password change failed. Please try again.";throw a({type:"AUTH_FAILURE",payload:e}),t}},refreshUser:async()=>{try{if(mr.isAuthenticated()){const e=await mr.getProfile();a({type:"UPDATE_USER",payload:e})}}catch(e){console.error("Refresh user error:",e),401===e.status&&a({type:"AUTH_LOGOUT"})}},clearError:()=>{a({type:"CLEAR_ERROR"})}};return(0,gr.jsx)(br.Provider,{value:o,children:t})},wr=()=>{const e=(0,r.useContext)(br);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},kr=e=>{let{children:t,requireAuth:n=!0,redirectTo:r="/login"}=e;const{isAuthenticated:a,isLoading:o}=wr(),i=Z();if(o)return(0,gr.jsx)("div",{className:"min-h-screen bg-dark-900 flex items-center justify-center",children:(0,gr.jsxs)("div",{className:"text-center",children:[(0,gr.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,gr.jsx)("p",{className:"text-dark-300 text-lg",children:"Loading..."})]})});if(n&&!a)return(0,gr.jsx)(ve,{to:r,state:{from:i},replace:!0});if(!n&&a){var s,l;const e=(null===(s=i.state)||void 0===s||null===(l=s.from)||void 0===l?void 0:l.pathname)||"/dashboard";return(0,gr.jsx)(ve,{to:e,replace:!0})}return(0,gr.jsx)(gr.Fragment,{children:t})};function Sr(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const jr=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},Nr=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var Er={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Or=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],Pr=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:s="",children:l,iconNode:u}=e,c=Sr(e,Or);return(0,r.createElement)("svg",Fe(Fe(Fe({ref:t},Er),{},{width:a,height:a,stroke:n,strokeWidth:i?24*Number(o)/Number(a):o,className:Nr("lucide",s)},!l&&!(e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(c)&&{"aria-hidden":"true"}),c),[...u.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(l)?l:[l]])}),Cr=["className"],_r=(e,t)=>{const n=(0,r.forwardRef)((n,a)=>{let{className:o}=n,i=Sr(n,Cr);return(0,r.createElement)(Pr,Fe({ref:a,iconNode:t,className:Nr("lucide-".concat((s=jr(e),s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),"lucide-".concat(e),o)},i));var s});return n.displayName=jr(e),n},Tr=_r("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Rr=_r("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),Ar=_r("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),Lr=_r("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),zr=_r("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),Dr=_r("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Fr=_r("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Ir=()=>{var e,t;const n=te(),a=Z(),{login:o,isLoading:i,error:s,clearError:l}=wr(),[u,c]=(0,r.useState)({username:"",password:""}),[d,f]=(0,r.useState)(!1),[p,h]=(0,r.useState)("username"),[m,g]=(0,r.useState)(""),v=(null===(e=a.state)||void 0===e||null===(t=e.from)||void 0===t?void 0:t.pathname)||"/dashboard",y=e=>{const{name:t,value:n}=e.target;c(e=>Fe(Fe({},e),{},{[t]:n})),s&&l(),m&&g("")},b=s||m;return(0,gr.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 flex items-center justify-center p-4",children:[(0,gr.jsx)("div",{className:"absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-primary-500/10 to-secondary-500/10 rounded-full blur-3xl"}),(0,gr.jsx)("div",{className:"absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-secondary-500/10 to-primary-500/10 rounded-full blur-3xl"}),(0,gr.jsxs)("div",{className:"w-full max-w-md",children:[(0,gr.jsxs)("div",{className:"text-center mb-8",children:[(0,gr.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl mb-4",children:(0,gr.jsx)(Tr,{className:"w-8 h-8 text-white"})}),(0,gr.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Welcome Back"}),(0,gr.jsx)("p",{className:"text-dark-300",children:"Sign in to continue your KPSS journey"})]}),(0,gr.jsxs)("div",{className:"glass rounded-2xl p-8 shadow-2xl",children:[(0,gr.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),g(""),u.username&&u.password)try{const e={password:u.password};"email"===p?e.email=u.username:e.username=u.username,await o(e),n(v,{replace:!0})}catch(t){console.error("Login error:",t),g(t.message||"Login failed. Please try again.")}else g("Please fill in all fields")},className:"space-y-6",children:[b&&(0,gr.jsxs)("div",{className:"bg-danger-500/10 border border-danger-500/20 rounded-lg p-4 flex items-center space-x-3",children:[(0,gr.jsx)(Rr,{className:"w-5 h-5 text-danger-400 flex-shrink-0"}),(0,gr.jsx)("p",{className:"text-danger-400 text-sm",children:b})]}),(0,gr.jsxs)("div",{className:"flex bg-dark-700/50 rounded-lg p-1",children:[(0,gr.jsx)("button",{type:"button",onClick:()=>h("username"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ".concat("username"===p?"bg-primary-600 text-white shadow-lg":"text-dark-300 hover:text-white"),children:"Username"}),(0,gr.jsx)("button",{type:"button",onClick:()=>h("email"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ".concat("email"===p?"bg-primary-600 text-white shadow-lg":"text-dark-300 hover:text-white"),children:"Email"})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-dark-200 mb-2",children:"email"===p?"Email Address":"Username"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:"email"===p?(0,gr.jsx)(Ar,{className:"h-5 w-5 text-dark-400"}):(0,gr.jsx)(Tr,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"username",name:"username",type:"email"===p?"email":"text",required:!0,value:u.username,onChange:y,className:"input-primary pl-10 w-full",placeholder:"email"===p?"Enter your email":"Enter your username"})]})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-dark-200 mb-2",children:"Password"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,gr.jsx)(Lr,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"password",name:"password",type:d?"text":"password",required:!0,value:u.password,onChange:y,className:"input-primary pl-10 pr-10 w-full",placeholder:"Enter your password"}),(0,gr.jsx)("button",{type:"button",onClick:()=>f(!d),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-dark-400 hover:text-dark-200 transition-colors",children:d?(0,gr.jsx)(zr,{className:"h-5 w-5"}):(0,gr.jsx)(Dr,{className:"h-5 w-5"})})]})]}),(0,gr.jsx)("div",{className:"text-right",children:(0,gr.jsx)(_e,{to:"/forgot-password",className:"text-sm text-primary-400 hover:text-primary-300 transition-colors",children:"Forgot your password?"})}),(0,gr.jsx)("button",{type:"submit",disabled:i,className:"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:i?(0,gr.jsxs)(gr.Fragment,{children:[(0,gr.jsx)(Fr,{className:"w-5 h-5 animate-spin"}),(0,gr.jsx)("span",{children:"Signing in..."})]}):(0,gr.jsx)("span",{children:"Sign In"})})]}),(0,gr.jsx)("div",{className:"mt-6 text-center",children:(0,gr.jsxs)("p",{className:"text-dark-300",children:["Don't have an account?"," ",(0,gr.jsx)(_e,{to:"/register",className:"text-primary-400 hover:text-primary-300 font-medium transition-colors",children:"Sign up here"})]})})]}),(0,gr.jsx)("div",{className:"text-center mt-8",children:(0,gr.jsx)("p",{className:"text-dark-400 text-sm",children:"\xa9 2024 KPSS Plus. All rights reserved."})})]})]})},Ur=_r("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),Mr=_r("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),Br=()=>{const e=te(),{register:t,isLoading:n,error:a,clearError:o}=wr(),[i,s]=(0,r.useState)({username:"",email:"",password:"",name:"",phone:"",terms_accepted:!1}),[l,u]=(0,r.useState)(""),[c,d]=(0,r.useState)(!1),[f,p]=(0,r.useState)(!1),[h,m]=(0,r.useState)(""),[g,v]=(0,r.useState)(!1),y=e=>{const{name:t,value:n,type:r,checked:i}=e.target;"confirmPassword"===t?u(n):s(e=>Fe(Fe({},e),{},{[t]:"checkbox"===r?i:n})),a&&o(),h&&m("")},b=a||h;return g?(0,gr.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 flex items-center justify-center p-4",children:(0,gr.jsx)("div",{className:"w-full max-w-md",children:(0,gr.jsxs)("div",{className:"glass rounded-2xl p-8 shadow-2xl text-center",children:[(0,gr.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-success-600 to-success-700 rounded-2xl mb-4",children:(0,gr.jsx)(Ur,{className:"w-8 h-8 text-white"})}),(0,gr.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Registration Successful!"}),(0,gr.jsx)("p",{className:"text-dark-300 mb-4",children:"Your account has been created successfully. You will be redirected to the login page shortly."}),(0,gr.jsx)("div",{className:"spinner w-6 h-6 mx-auto"})]})})}):(0,gr.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 flex items-center justify-center p-4",children:[(0,gr.jsx)("div",{className:"absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-primary-500/10 to-secondary-500/10 rounded-full blur-3xl"}),(0,gr.jsx)("div",{className:"absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-secondary-500/10 to-primary-500/10 rounded-full blur-3xl"}),(0,gr.jsxs)("div",{className:"w-full max-w-md",children:[(0,gr.jsxs)("div",{className:"text-center mb-8",children:[(0,gr.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl mb-4",children:(0,gr.jsx)(Tr,{className:"w-8 h-8 text-white"})}),(0,gr.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Create Account"}),(0,gr.jsx)("p",{className:"text-dark-300",children:"Join KPSS Plus and start your learning journey"})]}),(0,gr.jsxs)("div",{className:"glass rounded-2xl p-8 shadow-2xl",children:[(0,gr.jsxs)("form",{onSubmit:async n=>{if(n.preventDefault(),m(""),i.username&&i.email&&i.password&&i.name?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i.email)?/^[a-zA-Z0-9_]{3,20}$/.test(i.username)?i.password.length<8?(m("Password must be at least 8 characters long"),0):i.password!==l?(m("Passwords do not match"),0):i.terms_accepted||(m("Please accept the terms and conditions"),0):(m("Username must be 3-20 characters and contain only letters, numbers, and underscores"),0):(m("Please enter a valid email address"),0):(m("Please fill in all required fields"),0))try{await t(i),v(!0),setTimeout(()=>{e("/login",{state:{message:"Registration successful! Please sign in with your credentials."}})},2e3)}catch(r){console.error("Registration error:",r),m(r.message||"Registration failed. Please try again.")}},className:"space-y-6",children:[b&&(0,gr.jsxs)("div",{className:"bg-danger-500/10 border border-danger-500/20 rounded-lg p-4 flex items-center space-x-3",children:[(0,gr.jsx)(Rr,{className:"w-5 h-5 text-danger-400 flex-shrink-0"}),(0,gr.jsx)("p",{className:"text-danger-400 text-sm",children:b})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-dark-200 mb-2",children:"Full Name *"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,gr.jsx)(Tr,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"name",name:"name",type:"text",required:!0,value:i.name,onChange:y,className:"input-primary pl-10 w-full",placeholder:"Enter your full name"})]})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-dark-200 mb-2",children:"Username *"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,gr.jsx)(Tr,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"username",name:"username",type:"text",required:!0,value:i.username,onChange:y,className:"input-primary pl-10 w-full",placeholder:"Choose a username"})]}),(0,gr.jsx)("p",{className:"text-xs text-dark-400 mt-1",children:"3-20 characters, letters, numbers, and underscores only"})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-dark-200 mb-2",children:"Email Address *"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,gr.jsx)(Ar,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:i.email,onChange:y,className:"input-primary pl-10 w-full",placeholder:"Enter your email"})]})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-dark-200 mb-2",children:"Phone Number (Optional)"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,gr.jsx)(Mr,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"phone",name:"phone",type:"tel",value:i.phone,onChange:y,className:"input-primary pl-10 w-full",placeholder:"Enter your phone number"})]})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-dark-200 mb-2",children:"Password *"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,gr.jsx)(Lr,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"password",name:"password",type:c?"text":"password",required:!0,value:i.password,onChange:y,className:"input-primary pl-10 pr-10 w-full",placeholder:"Create a password"}),(0,gr.jsx)("button",{type:"button",onClick:()=>d(!c),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-dark-400 hover:text-dark-200 transition-colors",children:c?(0,gr.jsx)(zr,{className:"h-5 w-5"}):(0,gr.jsx)(Dr,{className:"h-5 w-5"})})]}),(0,gr.jsx)("p",{className:"text-xs text-dark-400 mt-1",children:"At least 8 characters"})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-dark-200 mb-2",children:"Confirm Password *"}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,gr.jsx)(Lr,{className:"h-5 w-5 text-dark-400"})}),(0,gr.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:f?"text":"password",required:!0,value:l,onChange:y,className:"input-primary pl-10 pr-10 w-full",placeholder:"Confirm your password"}),(0,gr.jsx)("button",{type:"button",onClick:()=>p(!f),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-dark-400 hover:text-dark-200 transition-colors",children:f?(0,gr.jsx)(zr,{className:"h-5 w-5"}):(0,gr.jsx)(Dr,{className:"h-5 w-5"})})]})]}),(0,gr.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,gr.jsx)("input",{id:"terms_accepted",name:"terms_accepted",type:"checkbox",checked:i.terms_accepted,onChange:y,className:"mt-1 w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"}),(0,gr.jsxs)("label",{htmlFor:"terms_accepted",className:"text-sm text-dark-300",children:["I agree to the"," ",(0,gr.jsx)(_e,{to:"/terms",className:"text-primary-400 hover:text-primary-300 transition-colors",children:"Terms and Conditions"})," ","and"," ",(0,gr.jsx)(_e,{to:"/privacy",className:"text-primary-400 hover:text-primary-300 transition-colors",children:"Privacy Policy"})]})]}),(0,gr.jsx)("button",{type:"submit",disabled:n,className:"btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:n?(0,gr.jsxs)(gr.Fragment,{children:[(0,gr.jsx)(Fr,{className:"w-5 h-5 animate-spin"}),(0,gr.jsx)("span",{children:"Creating Account..."})]}):(0,gr.jsx)("span",{children:"Create Account"})})]}),(0,gr.jsx)("div",{className:"mt-6 text-center",children:(0,gr.jsxs)("p",{className:"text-dark-300",children:["Already have an account?"," ",(0,gr.jsx)(_e,{to:"/login",className:"text-primary-400 hover:text-primary-300 font-medium transition-colors",children:"Sign in here"})]})})]}),(0,gr.jsx)("div",{className:"text-center mt-8",children:(0,gr.jsx)("p",{className:"text-dark-400 text-sm",children:"\xa9 2024 KPSS Plus. All rights reserved."})})]})]})},qr=_r("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]),Hr=_r("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),Vr=_r("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),Wr=_r("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),Qr=_r("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]),Kr=_r("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),$r=_r("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Gr=_r("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),Jr=_r("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),Yr=_r("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),Xr=_r("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),Zr=_r("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),ea=e=>{let{className:t="",size:n="md"}=e;return(0,gr.jsxs)("div",{className:"flex items-center space-x-2 ".concat(t),children:[(0,gr.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center",children:(0,gr.jsx)("span",{className:"text-white font-bold text-sm",children:"K+"})}),(0,gr.jsx)("span",{className:"font-bold gradient-text ".concat({sm:"text-lg",md:"text-xl",lg:"text-2xl"}[n]),children:"KPSS Plus"})]})},ta=()=>{const e=Z(),t=te(),{user:n,logout:a}=wr(),[o,i]=(0,r.useState)(!1),[s,l]=(0,r.useState)(!1),u=[{name:"Dashboard",href:"/dashboard",icon:qr,description:"Overview and statistics"},{name:"Quizzes",href:"/quizzes",icon:Hr,description:"Practice tests and exams"},{name:"Progress",href:"/progress",icon:Vr,description:"Track your learning"},{name:"Social",href:"/social",icon:Wr,description:"Friends and leaderboards"},{name:"Content",href:"/content",icon:Qr,description:"Study materials"}],c=async()=>{try{await a(),t("/login")}catch(e){console.error("Logout error:",e)}};return(0,gr.jsxs)("div",{className:"flex h-screen bg-dark-900 relative overflow-hidden",children:[(0,gr.jsx)("div",{className:"absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 rounded-full blur-3xl pointer-events-none"}),(0,gr.jsx)("div",{className:"absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-secondary-500/5 to-primary-500/5 rounded-full blur-3xl pointer-events-none"}),o&&(0,gr.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm lg:hidden",onClick:()=>i(!1)}),(0,gr.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-dark-800/90 backdrop-blur-xl border-r border-dark-700/50 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ".concat(o?"translate-x-0":"-translate-x-full"),children:[(0,gr.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-dark-700/50",children:[(0,gr.jsx)(ea,{size:"md"}),(0,gr.jsx)("button",{onClick:()=>i(!1),className:"lg:hidden text-dark-400 hover:text-white transition-colors p-1",children:(0,gr.jsx)(Kr,{size:20})})]}),(0,gr.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:u.map(t=>{const n=t.icon,r=(t=>e.pathname===t)(t.href);return(0,gr.jsxs)(_e,{to:t.href,onClick:()=>i(!1),className:"group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ".concat(r?"bg-gradient-to-r from-primary-600/20 to-secondary-600/20 text-white border border-primary-500/30 shadow-lg":"text-dark-300 hover:bg-dark-700/50 hover:text-white"),children:[(0,gr.jsx)(n,{size:20,className:"mr-3 transition-colors ".concat(r?"text-primary-400":"text-dark-400 group-hover:text-primary-400")}),(0,gr.jsxs)("div",{className:"flex-1",children:[(0,gr.jsx)("div",{className:"font-medium",children:t.name}),(0,gr.jsx)("div",{className:"text-xs text-dark-400 group-hover:text-dark-300",children:t.description})]})]},t.name)})}),(0,gr.jsxs)("div",{className:"p-4 border-t border-dark-700/50",children:[n&&(0,gr.jsx)("div",{className:"mb-4 p-3 bg-dark-700/30 rounded-lg",children:(0,gr.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,gr.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center",children:n.avatar?(0,gr.jsx)("img",{src:n.avatar,alt:n.name,className:"w-10 h-10 rounded-full object-cover"}):(0,gr.jsx)(Tr,{size:16,className:"text-white"})}),(0,gr.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,gr.jsx)("p",{className:"text-sm font-medium text-white truncate",children:n.name}),(0,gr.jsxs)("p",{className:"text-xs text-dark-400 truncate",children:["@",n.username]})]})]})}),(0,gr.jsxs)("div",{className:"space-y-2",children:[(0,gr.jsxs)(_e,{to:"/settings",className:"flex items-center w-full px-3 py-2 text-sm text-dark-300 hover:bg-dark-700/50 hover:text-white rounded-lg transition-all duration-200",children:[(0,gr.jsx)($r,{size:16,className:"mr-3"}),"Settings"]}),(0,gr.jsxs)("button",{onClick:c,className:"flex items-center w-full px-3 py-2 text-sm text-dark-300 hover:bg-danger-600/20 hover:text-danger-400 rounded-lg transition-all duration-200",children:[(0,gr.jsx)(Gr,{size:16,className:"mr-3"}),"Sign Out"]})]})]})]}),(0,gr.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,gr.jsx)("header",{className:"bg-dark-800/50 backdrop-blur-xl border-b border-dark-700/50 px-4 py-3",children:(0,gr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,gr.jsx)("button",{onClick:()=>i(!0),className:"lg:hidden text-dark-400 hover:text-white transition-colors p-2",children:(0,gr.jsx)(Jr,{size:20})}),(0,gr.jsx)("div",{className:"flex-1 max-w-md mx-4",children:(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsx)(Yr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400 w-4 h-4"}),(0,gr.jsx)("input",{type:"text",placeholder:"Search quizzes, content...",className:"w-full pl-10 pr-4 py-2 bg-dark-700/50 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200"})]})}),(0,gr.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,gr.jsxs)("button",{className:"relative p-2 text-dark-400 hover:text-white transition-colors",children:[(0,gr.jsx)(Xr,{size:20}),(0,gr.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-danger-500 rounded-full"})]}),(0,gr.jsxs)("div",{className:"relative",children:[(0,gr.jsxs)("button",{onClick:()=>l(!s),className:"flex items-center space-x-2 p-2 text-dark-400 hover:text-white transition-colors",children:[(0,gr.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center",children:null!==n&&void 0!==n&&n.avatar?(0,gr.jsx)("img",{src:n.avatar,alt:n.name,className:"w-8 h-8 rounded-full object-cover"}):(0,gr.jsx)(Tr,{size:14,className:"text-white"})}),(0,gr.jsx)(Zr,{size:16,className:"transition-transform ".concat(s?"rotate-180":"")})]}),s&&(0,gr.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-dark-800 border border-dark-700 rounded-lg shadow-xl z-50",children:(0,gr.jsxs)("div",{className:"py-2",children:[(0,gr.jsx)(_e,{to:"/profile",className:"block px-4 py-2 text-sm text-dark-300 hover:bg-dark-700 hover:text-white transition-colors",onClick:()=>l(!1),children:"View Profile"}),(0,gr.jsx)(_e,{to:"/settings",className:"block px-4 py-2 text-sm text-dark-300 hover:bg-dark-700 hover:text-white transition-colors",onClick:()=>l(!1),children:"Settings"}),(0,gr.jsx)("hr",{className:"my-2 border-dark-700"}),(0,gr.jsx)("button",{onClick:()=>{l(!1),c()},className:"block w-full text-left px-4 py-2 text-sm text-danger-400 hover:bg-danger-600/20 transition-colors",children:"Sign Out"})]})})]})]})]})}),(0,gr.jsx)("main",{className:"flex-1 overflow-y-auto bg-dark-900 p-6",children:(0,gr.jsx)(ye,{})})]})]})};n(844);Object.create(null);function na(){if(console&&console.warn){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];"string"===typeof n[0]&&(n[0]="react-i18next:: ".concat(n[0])),(e=console).warn.apply(e,n)}}var ra={};function aa(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"string"===typeof t[0]&&ra[t[0]]||("string"===typeof t[0]&&(ra[t[0]]=new Date),na.apply(void 0,t))}var oa=function(e,t){return function(){if(e.isInitialized)t();else{e.on("initialized",function n(){setTimeout(function(){e.off("initialized",n)},0),t()})}}};function ia(e,t,n){e.loadNamespaces(t,oa(e,n))}function sa(e,t,n,r){"string"===typeof n&&(n=[n]),n.forEach(function(t){e.options.ns.indexOf(t)<0&&e.options.ns.push(t)}),e.loadLanguages(t,oa(e,r))}var la=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ua={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"\u2026","&#8230;":"\u2026","&#x2F;":"/","&#47;":"/"},ca=function(e){return ua[e]};function da(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function fa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?da(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):da(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var pa={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:function(e){return e.replace(la,ca)}};var ha;function ma(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ga(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Le(r.key),r)}}function va(e,t,n){return t&&ga(e.prototype,t),n&&ga(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var ya={type:"3rdParty",init:function(e){!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};pa=fa(fa({},pa),e)}(e.options.react),function(e){ha=e}(e)}};var ba=(0,r.createContext)(),xa=function(){function e(){ma(this,e),this.usedNamespaces={}}return va(e,[{key:"addUsedNamespaces",value:function(e){var t=this;e.forEach(function(e){t.usedNamespaces[e]||(t.usedNamespaces[e]=!0)})}},{key:"getUsedNamespaces",value:function(){return Object.keys(this.usedNamespaces)}}]),e}();function wa(e){if(Array.isArray(e))return e}function ka(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Sa(e,t){if(e){if("string"==typeof e)return ka(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ka(e,t):void 0}}function ja(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Na(e,t){return wa(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,s=[],l=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){u=!0,a=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw a}}return s}}(e,t)||Sa(e,t)||ja()}function Ea(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Oa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ea(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ea(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Pa(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.i18n,a=(0,r.useContext)(ba)||{},o=a.i18n,i=a.defaultNS,s=n||o||ha;if(s&&!s.reportNamespaces&&(s.reportNamespaces=new xa),!s){aa("You will need to pass in an i18next instance by using initReactI18next");var l=function(e,t){return"string"===typeof t?t:t&&"object"===Ae(t)&&"string"===typeof t.defaultValue?t.defaultValue:Array.isArray(e)?e[e.length-1]:e},u=[l,{},!1];return u.t=l,u.i18n={},u.ready=!1,u}s.options.react&&void 0!==s.options.react.wait&&aa("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");var c=Oa(Oa(Oa({},pa),s.options.react),t),d=c.useSuspense,f=c.keyPrefix,p=e||i||s.options&&s.options.defaultNS;p="string"===typeof p?[p]:p||["translation"],s.reportNamespaces.addUsedNamespaces&&s.reportNamespaces.addUsedNamespaces(p);var h=(s.isInitialized||s.initializedStoreOnce)&&p.every(function(e){return function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.languages&&t.languages.length?void 0!==t.options.ignoreJSONStructure?t.hasLoadedNamespace(e,{lng:n.lng,precheck:function(t,r){if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!r(t.isLanguageChangingTo,e))return!1}}):function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.languages[0],a=!!t.options&&t.options.fallbackLng,o=t.languages[t.languages.length-1];if("cimode"===r.toLowerCase())return!0;var i=function(e,n){var r=t.services.backendConnector.state["".concat(e,"|").concat(n)];return-1===r||2===r};return!(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!i(t.isLanguageChangingTo,e))&&(!!t.hasResourceBundle(r,e)||!(t.services.backendConnector.backend&&(!t.options.resources||t.options.partialBundledLanguages))||!(!i(r,e)||a&&!i(o,e)))}(e,t,n):(aa("i18n.languages were undefined or empty",t.languages),!0)}(e,s,c)});function m(){return s.getFixedT(t.lng||null,"fallback"===c.nsMode?p:p[0],f)}var g=Na((0,r.useState)(m),2),v=g[0],y=g[1],b=p.join();t.lng&&(b="".concat(t.lng).concat(b));var x=function(e,t){var n=(0,r.useRef)();return(0,r.useEffect)(function(){n.current=t?n.current:e},[e,t]),n.current}(b),w=(0,r.useRef)(!0);(0,r.useEffect)(function(){var e=c.bindI18n,n=c.bindI18nStore;function r(){w.current&&y(m)}return w.current=!0,h||d||(t.lng?sa(s,t.lng,p,function(){w.current&&y(m)}):ia(s,p,function(){w.current&&y(m)})),h&&x&&x!==b&&w.current&&y(m),e&&s&&s.on(e,r),n&&s&&s.store.on(n,r),function(){w.current=!1,e&&s&&e.split(" ").forEach(function(e){return s.off(e,r)}),n&&s&&n.split(" ").forEach(function(e){return s.store.off(e,r)})}},[s,b]);var k=(0,r.useRef)(!0);(0,r.useEffect)(function(){w.current&&!k.current&&y(m),k.current=!1},[s,f]);var S=[v,s,h];if(S.t=v,S.i18n=s,S.ready=h,h)return S;if(!h&&!d)return S;throw new Promise(function(e){t.lng?sa(s,t.lng,p,function(){return e()}):ia(s,p,function(){return e()})})}const Ca=_r("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),_a=_r("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),Ta=_r("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),Ra=_r("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),Aa=_r("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),La=_r("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),za=_r("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),Da=()=>{const{t:e}=Pa(),{user:t}=wr(),n=[{title:e("dashboard.totalQuizzes"),value:"24",change:"+3 this week",changeType:"positive",icon:Hr,gradient:"from-primary-500 to-primary-600"},{title:e("dashboard.averageScore"),value:"87%",change:"+5% from last month",changeType:"positive",icon:Vr,gradient:"from-success-500 to-success-600"},{title:e("dashboard.studyStreak"),value:"12 days",change:"Keep it up!",changeType:"positive",icon:Ca,gradient:"from-warning-500 to-warning-600"},{title:"Study Time",value:"45h",change:"+8h this week",changeType:"positive",icon:_a,gradient:"from-secondary-500 to-secondary-600"}],r=[{id:1,type:"quiz",title:"KPSS Genel K\xfclt\xfcr Testi",score:92,time:"2 hours ago",icon:Hr},{id:2,type:"achievement",title:"First Perfect Score!",description:"Scored 100% on a quiz",time:"1 day ago",icon:Qr},{id:3,type:"study",title:"Completed Tarih Chapter 5",progress:100,time:"2 days ago",icon:Ta}],a=[{title:e("dashboard.takeQuiz"),description:"Start a new practice test",icon:Hr,color:"from-primary-600 to-primary-700",href:"/quizzes"},{title:e("dashboard.viewProgress"),description:"Check your learning progress",icon:Ra,color:"from-success-600 to-success-700",href:"/progress"},{title:e("dashboard.browseContent"),description:"Explore study materials",icon:Aa,color:"from-secondary-600 to-secondary-700",href:"/content"}];return(0,gr.jsxs)("div",{className:"space-y-8",children:[(0,gr.jsxs)("div",{className:"mb-8",children:[(0,gr.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:e("dashboard.title")}),(0,gr.jsx)("p",{className:"text-dark-300 text-lg",children:e("dashboard.welcomeMessage",{name:t?", ".concat(t.name):""})})]}),(0,gr.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:n.map((e,t)=>{const n=e.icon;return(0,gr.jsxs)("div",{className:"card-hover group",children:[(0,gr.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,gr.jsxs)("div",{children:[(0,gr.jsx)("p",{className:"text-sm font-medium text-dark-400 mb-1",children:e.title}),(0,gr.jsx)("p",{className:"text-2xl font-bold text-white",children:e.value})]}),(0,gr.jsx)("div",{className:"p-3 bg-gradient-to-br ".concat(e.gradient," rounded-xl group-hover:scale-110 transition-transform duration-300"),children:(0,gr.jsx)(n,{className:"h-6 w-6 text-white"})})]}),(0,gr.jsxs)("div",{className:"flex items-center",children:[(0,gr.jsx)(La,{className:"h-4 w-4 text-success-400 mr-1"}),(0,gr.jsx)("span",{className:"text-sm font-medium text-success-400",children:e.change})]})]},t)})}),(0,gr.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,gr.jsx)("div",{className:"lg:col-span-2",children:(0,gr.jsxs)("div",{className:"card",children:[(0,gr.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,gr.jsx)("h3",{className:"text-xl font-semibold text-white",children:e("dashboard.recentActivity")}),(0,gr.jsx)("button",{className:"text-primary-400 hover:text-primary-300 text-sm font-medium transition-colors",children:"View All"})]}),(0,gr.jsx)("div",{className:"space-y-4",children:r.length>0?r.map(e=>{const t=e.icon;return(0,gr.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-dark-700/30 rounded-lg hover:bg-dark-700/50 transition-colors",children:[(0,gr.jsx)("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center ".concat("quiz"===e.type?"bg-primary-500/20 text-primary-400":"achievement"===e.type?"bg-warning-500/20 text-warning-400":"bg-success-500/20 text-success-400"),children:(0,gr.jsx)(t,{className:"h-5 w-5"})}),(0,gr.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,gr.jsx)("p",{className:"text-white font-medium truncate",children:e.title}),(0,gr.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-dark-400",children:[(0,gr.jsx)("span",{children:e.time}),e.score&&(0,gr.jsxs)("span",{className:"text-success-400",children:["Score: ",e.score,"%"]}),e.progress&&(0,gr.jsxs)("span",{className:"text-primary-400",children:["Progress: ",e.progress,"%"]})]})]})]},e.id)}):(0,gr.jsxs)("div",{className:"text-center py-12",children:[(0,gr.jsx)(za,{className:"h-16 w-16 text-dark-500 mx-auto mb-4"}),(0,gr.jsx)("p",{className:"text-dark-400 text-lg",children:e("dashboard.noRecentActivity")}),(0,gr.jsx)("p",{className:"text-dark-500 text-sm mt-2",children:e("dashboard.startLearning")})]})})]})}),(0,gr.jsxs)("div",{className:"space-y-6",children:[(0,gr.jsxs)("div",{className:"card",children:[(0,gr.jsx)("h3",{className:"text-xl font-semibold text-white mb-6",children:e("dashboard.quickActions")}),(0,gr.jsx)("div",{className:"space-y-4",children:a.map((e,t)=>{const n=e.icon;return(0,gr.jsx)("button",{className:"w-full p-4 bg-gradient-to-r ".concat(e.color," rounded-lg hover:shadow-lg transition-all duration-300 group text-left"),children:(0,gr.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,gr.jsx)(n,{className:"h-5 w-5 text-white group-hover:scale-110 transition-transform"}),(0,gr.jsxs)("div",{children:[(0,gr.jsx)("p",{className:"text-white font-medium",children:e.title}),(0,gr.jsx)("p",{className:"text-white/80 text-sm",children:e.description})]})]})},t)})})]}),(0,gr.jsxs)("div",{className:"card",children:[(0,gr.jsx)("h3",{className:"text-xl font-semibold text-white mb-6",children:"Today's Goals"}),(0,gr.jsxs)("div",{className:"space-y-4",children:[(0,gr.jsxs)("div",{children:[(0,gr.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,gr.jsx)("span",{className:"text-dark-300 text-sm",children:"Complete 2 Quizzes"}),(0,gr.jsx)("span",{className:"text-primary-400 text-sm",children:"1/2"})]}),(0,gr.jsx)("div",{className:"w-full bg-dark-700 rounded-full h-2",children:(0,gr.jsx)("div",{className:"bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full",style:{width:"50%"}})})]}),(0,gr.jsxs)("div",{children:[(0,gr.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,gr.jsx)("span",{className:"text-dark-300 text-sm",children:"Study 30 minutes"}),(0,gr.jsx)("span",{className:"text-success-400 text-sm",children:"45/30 min"})]}),(0,gr.jsx)("div",{className:"w-full bg-dark-700 rounded-full h-2",children:(0,gr.jsx)("div",{className:"bg-gradient-to-r from-success-500 to-success-600 h-2 rounded-full",style:{width:"100%"}})})]})]})]})]})]})]})},Fa=()=>{var e,t;const n=te(),a=Z(),[o,i]=(0,r.useState)({username:"",password:""}),[s,l]=(0,r.useState)(""),[u,c]=(0,r.useState)(!1),d={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:8000/api/v1",REACT_APP_NAME:"KPSS Plus",REACT_APP_VERSION:"1.0.0",REACT_APP_ENABLE_SOCIAL_LOGIN:"true",REACT_APP_ENABLE_NOTIFICATIONS:"true",REACT_APP_ENABLE_ANALYTICS:"false"}.REACT_APP_ADMIN_API_URL||"http://localhost:8100/api",f=(null===(e=a.state)||void 0===e||null===(t=e.from)||void 0===t?void 0:t.pathname)||"/admin",p=e=>{i(Fe(Fe({},o),{},{[e.target.name]:e.target.value}))};return(0,gr.jsx)("div",{className:"login-container",children:(0,gr.jsxs)("div",{className:"login-form",children:[(0,gr.jsx)("h2",{children:"Admin Login"}),s&&(0,gr.jsx)("div",{className:"error-message",children:s}),(0,gr.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),l(""),c(!0);try{const e=await cr.post("".concat(d,"/auth/login"),o);if(console.log("Response status:",e.status),console.log("Response data:",e.data),!e.data||!e.data.token)throw new Error("Invalid response structure");localStorage.setItem("adminToken",e.data.token),localStorage.setItem("adminUser",JSON.stringify(e.data.user)),console.log("Token saved to localStorage:",e.data.token),console.log("User saved to localStorage:",e.data.user),n(f,{replace:!0})}catch(a){var t,r;if(console.error("=== ADMIN LOGIN ERROR ==="),console.error("Error object:",a),console.error("Error response:",a.response),console.error("Error message:",a.message),console.error("Error config:",a.config),a.response)console.error("Status:",a.response.status),console.error("Data:",a.response.data),l((null===(t=a.response.data)||void 0===t?void 0:t.error)||(null===(r=a.response.data)||void 0===r?void 0:r.message)||"Server error: ".concat(a.response.status));else a.request?l("Network error - cannot connect to server"):l(a.message||"Login failed")}finally{c(!1)}},children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{htmlFor:"username",children:"Username"}),(0,gr.jsx)("input",{type:"text",id:"username",name:"username",className:"text-black",value:o.username,onChange:p,required:!0})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{htmlFor:"password",children:"Password"}),(0,gr.jsx)("input",{type:"password",id:"password",name:"password",className:"text-black",value:o.password,onChange:p,required:!0})]}),(0,gr.jsx)("button",{type:"submit",className:"login-btn",disabled:u,children:u?"Logging in...":"Login"})]})]})})},Ia=()=>{const[e,t]=(0,r.useState)({}),[n,a]=(0,r.useState)({}),[o,i]=(0,r.useState)({}),[s,l]=(0,r.useState)(!0),u={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:8000/api/v1",REACT_APP_NAME:"KPSS Plus",REACT_APP_VERSION:"1.0.0",REACT_APP_ENABLE_SOCIAL_LOGIN:"true",REACT_APP_ENABLE_NOTIFICATIONS:"true",REACT_APP_ENABLE_ANALYTICS:"false"}.REACT_APP_ADMIN_API_URL||"http://localhost:8100/api",c=(0,r.useCallback)(async()=>{const e=localStorage.getItem("adminToken");try{const[n,r,o]=await Promise.all([cr.get("".concat(u,"/analytics"),{headers:{Authorization:"Bearer ".concat(e)}}),cr.get("".concat(u,"/analytics/users"),{headers:{Authorization:"Bearer ".concat(e)}}),cr.get("".concat(u,"/analytics/content"),{headers:{Authorization:"Bearer ".concat(e)}})]);t(n.data),a(r.data),i(o.data)}catch(n){console.error("Failed to fetch analytics:",n)}finally{l(!1)}},[u]);return(0,r.useEffect)(()=>{c()},[c]),s?(0,gr.jsx)("div",{className:"loading",children:"Loading analytics..."}):(0,gr.jsxs)("div",{children:[(0,gr.jsx)("h2",{children:"Dashboard"}),(0,gr.jsxs)("div",{className:"dashboard-grid",children:[(0,gr.jsxs)("div",{className:"dashboard-card",children:[(0,gr.jsx)("h3",{children:"Total Users"}),(0,gr.jsx)("div",{className:"number",children:e.total_users||0}),(0,gr.jsx)("p",{children:"Registered users"})]}),(0,gr.jsxs)("div",{className:"dashboard-card",children:[(0,gr.jsx)("h3",{children:"Total Content"}),(0,gr.jsx)("div",{className:"number",children:e.total_content||0}),(0,gr.jsx)("p",{children:"Content items"})]}),(0,gr.jsxs)("div",{className:"dashboard-card",children:[(0,gr.jsx)("h3",{children:"Active Users"}),(0,gr.jsx)("div",{className:"number",children:n.active_users||0}),(0,gr.jsx)("p",{children:"Active in last 7 days"})]}),(0,gr.jsxs)("div",{className:"dashboard-card",children:[(0,gr.jsx)("h3",{children:"New Users Today"}),(0,gr.jsx)("div",{className:"number",children:n.new_users_today||0}),(0,gr.jsx)("p",{children:"Registered today"})]}),(0,gr.jsxs)("div",{className:"dashboard-card",children:[(0,gr.jsx)("h3",{children:"Published Content"}),(0,gr.jsx)("div",{className:"number",children:o.published_content||0}),(0,gr.jsx)("p",{children:"Published content items"})]}),(0,gr.jsxs)("div",{className:"dashboard-card",children:[(0,gr.jsx)("h3",{children:"Draft Content"}),(0,gr.jsx)("div",{className:"number",children:o.draft_content||0}),(0,gr.jsx)("p",{children:"Draft content items"})]})]})]})},Ua=()=>{const[e,t]=(0,r.useState)([]),[n,a]=(0,r.useState)(!0),[o,i]=(0,r.useState)(null),[s,l]=(0,r.useState)(!1),u={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:8000/api/v1",REACT_APP_NAME:"KPSS Plus",REACT_APP_VERSION:"1.0.0",REACT_APP_ENABLE_SOCIAL_LOGIN:"true",REACT_APP_ENABLE_NOTIFICATIONS:"true",REACT_APP_ENABLE_ANALYTICS:"false"}.REACT_APP_ADMIN_API_URL||"http://localhost:8100/api",c=(0,r.useCallback)(async()=>{const e=localStorage.getItem("adminToken");try{const n=await cr.get("".concat(u,"/users?limit=10"),{headers:{Authorization:"Bearer ".concat(e)}});t(n.data.data)}catch(n){console.error("Failed to fetch users:",n)}finally{a(!1)}},[u]);(0,r.useEffect)(()=>{c()},[c]);return n?(0,gr.jsx)("div",{className:"loading",children:"Loading users..."}):(0,gr.jsxs)("div",{children:[(0,gr.jsx)("h2",{children:"Users"}),(0,gr.jsxs)("div",{className:"table-container",children:[(0,gr.jsx)("h3",{children:"Manage Users"}),(0,gr.jsxs)("table",{children:[(0,gr.jsx)("thead",{children:(0,gr.jsxs)("tr",{children:[(0,gr.jsx)("th",{children:"ID"}),(0,gr.jsx)("th",{children:"Username"}),(0,gr.jsx)("th",{children:"Email"}),(0,gr.jsx)("th",{children:"Name"}),(0,gr.jsx)("th",{children:"Admin"}),(0,gr.jsx)("th",{children:"Active"}),(0,gr.jsx)("th",{children:"Actions"})]})}),(0,gr.jsx)("tbody",{children:e.map(e=>(0,gr.jsxs)("tr",{children:[(0,gr.jsx)("td",{children:e.id}),(0,gr.jsx)("td",{children:e.username}),(0,gr.jsx)("td",{children:e.email||"-"}),(0,gr.jsx)("td",{children:e.name}),(0,gr.jsx)("td",{children:e.is_admin?"Yes":"No"}),(0,gr.jsx)("td",{children:e.is_active?"Yes":"No"}),(0,gr.jsxs)("td",{className:"actions",children:[(0,gr.jsx)("button",{className:"btn btn-primary",onClick:()=>(e=>{i(e),l(!0)})(e),children:"Edit"}),(0,gr.jsx)("button",{className:"btn btn-danger",onClick:()=>(async e=>{if(!window.confirm("Are you sure you want to delete this user?"))return;const t=localStorage.getItem("adminToken");try{await cr.delete("".concat(u,"/admin/users/").concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),c()}catch(n){console.error("Failed to delete user:",n)}})(e.id),children:"Delete"})]})]},e.id))})]})]}),s&&(0,gr.jsx)("div",{className:"modal",children:(0,gr.jsxs)("div",{className:"modal-content",children:[(0,gr.jsxs)("div",{className:"modal-header",children:[(0,gr.jsx)("h3",{children:"Edit User"}),(0,gr.jsx)("button",{className:"close-btn",onClick:()=>l(!1),children:"\xd7"})]}),(0,gr.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=localStorage.getItem("adminToken");try{await cr.put("".concat(u,"/admin/users/").concat(o.id),o,{headers:{Authorization:"Bearer ".concat(t)}}),l(!1),c()}catch(n){console.error("Failed to update user:",n)}},children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Username"}),(0,gr.jsx)("input",{type:"text",value:o.username,onChange:e=>i(Fe(Fe({},o),{},{username:e.target.value})),required:!0})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Email"}),(0,gr.jsx)("input",{type:"email",value:o.email||"",onChange:e=>i(Fe(Fe({},o),{},{email:e.target.value}))})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Name"}),(0,gr.jsx)("input",{type:"text",value:o.name,onChange:e=>i(Fe(Fe({},o),{},{name:e.target.value})),required:!0})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsx)("div",{className:"form-group",children:(0,gr.jsxs)("label",{children:[(0,gr.jsx)("input",{type:"checkbox",checked:o.is_admin,onChange:e=>i(Fe(Fe({},o),{},{is_admin:e.target.checked}))}),"Admin"]})}),(0,gr.jsx)("div",{className:"form-group",children:(0,gr.jsxs)("label",{children:[(0,gr.jsx)("input",{type:"checkbox",checked:o.is_active,onChange:e=>i(Fe(Fe({},o),{},{is_active:e.target.checked}))}),"Active"]})})]}),(0,gr.jsxs)("div",{className:"actions",children:[(0,gr.jsx)("button",{type:"submit",className:"btn btn-success",children:"Save"}),(0,gr.jsx)("button",{type:"button",className:"btn btn-danger",onClick:()=>l(!1),children:"Cancel"})]})]})]})})]})},Ma=()=>{const[e,t]=(0,r.useState)([]),[n,a]=(0,r.useState)(!0),[o,i]=(0,r.useState)(null),[s,l]=(0,r.useState)(!1),u={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:8000/api/v1",REACT_APP_NAME:"KPSS Plus",REACT_APP_VERSION:"1.0.0",REACT_APP_ENABLE_SOCIAL_LOGIN:"true",REACT_APP_ENABLE_NOTIFICATIONS:"true",REACT_APP_ENABLE_ANALYTICS:"false"}.REACT_APP_ADMIN_API_URL||"http://localhost:8100/api",c=(0,r.useCallback)(async()=>{const e=localStorage.getItem("adminToken");try{const n=await cr.get("".concat(u,"/contents?limit=20"),{headers:{Authorization:"Bearer ".concat(e)}});t(n.data.data||[])}catch(n){console.error("Failed to fetch content:",n)}finally{a(!1)}},[u]);(0,r.useEffect)(()=>{c()},[c]);const d=e=>{switch(e){case"book":return"\ud83d\udcda";case"video":return"\ud83c\udfa5";case"playlist":return"\ud83d\udccb";case"question":return"\u2753";default:return"\ud83d\udcc4"}};return n?(0,gr.jsx)("div",{className:"loading",children:"Loading study content..."}):(0,gr.jsxs)("div",{children:[(0,gr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[(0,gr.jsx)("h2",{children:"Study Content"}),(0,gr.jsx)("button",{className:"btn btn-success",onClick:()=>{i({title:"",description:"",type:"book",url:"",subject:"",difficulty:"medium",is_official:!1,author:"",publisher:"",year:(new Date).getFullYear(),total_pages:null,total_time:null,channel_name:"",duration:"",isbn:""}),l(!0)},children:"Add New Content"})]}),(0,gr.jsxs)("div",{className:"table-container",children:[(0,gr.jsx)("h3",{children:"Manage Study Materials"}),(0,gr.jsxs)("table",{children:[(0,gr.jsx)("thead",{children:(0,gr.jsxs)("tr",{children:[(0,gr.jsx)("th",{children:"Type"}),(0,gr.jsx)("th",{children:"Title"}),(0,gr.jsx)("th",{children:"Subject"}),(0,gr.jsx)("th",{children:"Difficulty"}),(0,gr.jsx)("th",{children:"Creator"}),(0,gr.jsx)("th",{children:"Created"}),(0,gr.jsx)("th",{children:"Actions"})]})}),(0,gr.jsx)("tbody",{children:e.map(e=>(0,gr.jsxs)("tr",{children:[(0,gr.jsxs)("td",{children:[d(e.type)," ",e.type]}),(0,gr.jsx)("td",{children:e.title}),(0,gr.jsx)("td",{children:e.subject||"-"}),(0,gr.jsx)("td",{children:e.difficulty||"-"}),(0,gr.jsx)("td",{children:e.author||e.channel_name||"-"}),(0,gr.jsx)("td",{children:new Date(e.created_at).toLocaleDateString()}),(0,gr.jsxs)("td",{className:"actions",children:[(0,gr.jsx)("button",{className:"btn btn-primary",onClick:()=>(i(e),void l(!0)),children:"Edit"}),(0,gr.jsx)("button",{className:"btn btn-danger",onClick:()=>(async e=>{if(!window.confirm("Are you sure you want to delete this content?"))return;const t=localStorage.getItem("adminToken");try{await cr.delete("".concat(u,"/admin/content/").concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),c()}catch(n){console.error("Failed to delete content:",n)}})(e.id),children:"Delete"})]})]},e.id))})]})]}),s&&(0,gr.jsx)("div",{className:"modal",children:(0,gr.jsxs)("div",{className:"modal-content",children:[(0,gr.jsxs)("div",{className:"modal-header",children:[(0,gr.jsx)("h3",{children:o.id?"Edit Content":"Add New Content"}),(0,gr.jsx)("button",{className:"close-btn",onClick:()=>l(!1),children:"\xd7"})]}),(0,gr.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=localStorage.getItem("adminToken");try{const e=Fe({},o);Object.keys(e).forEach(t=>{""!==e[t]&&null!==e[t]||delete e[t]}),o.id?await cr.put("".concat(u,"/admin/content/").concat(o.id),e,{headers:{Authorization:"Bearer ".concat(t)}}):await cr.post("".concat(u,"/admin/content"),e,{headers:{Authorization:"Bearer ".concat(t)}}),l(!1),c()}catch(a){var n,r;console.error("Failed to save content:",a),console.error("Token:",t),console.error("Error response:",a.response),alert("Error: "+((null===(n=a.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.error)||"Failed to save content"))}},children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Content Type"}),(0,gr.jsxs)("select",{value:o.type,onChange:e=>i(Fe(Fe({},o),{},{type:e.target.value})),required:!0,children:[(0,gr.jsx)("option",{value:"book",children:"\ud83d\udcda Book"}),(0,gr.jsx)("option",{value:"video",children:"\ud83c\udfa5 Video"}),(0,gr.jsx)("option",{value:"playlist",children:"\ud83d\udccb Playlist"}),(0,gr.jsx)("option",{value:"question",children:"\u2753 Question Bank"}),(0,gr.jsx)("option",{value:"pdf",children:"\ud83d\udcc4 PDF"})]})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Title *"}),(0,gr.jsx)("input",{type:"text",value:o.title,onChange:e=>i(Fe(Fe({},o),{},{title:e.target.value})),required:!0})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Description"}),(0,gr.jsx)("textarea",{value:o.description||"",onChange:e=>i(Fe(Fe({},o),{},{description:e.target.value})),rows:3})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Subject"}),(0,gr.jsxs)("select",{value:o.subject||"",onChange:e=>i(Fe(Fe({},o),{},{subject:e.target.value})),children:[(0,gr.jsx)("option",{value:"",children:"Select subject"}),(0,gr.jsx)("option",{value:"Genel K\xfclt\xfcr",children:"Genel K\xfclt\xfcr"}),(0,gr.jsx)("option",{value:"Genel Yetenek",children:"Genel Yetenek"}),(0,gr.jsx)("option",{value:"E\u011fitim Bilimleri",children:"E\u011fitim Bilimleri"}),(0,gr.jsx)("option",{value:"\xd6ABT",children:"\xd6ABT"})]})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Difficulty"}),(0,gr.jsxs)("select",{value:o.difficulty||"",onChange:e=>i(Fe(Fe({},o),{},{difficulty:e.target.value})),children:[(0,gr.jsx)("option",{value:"",children:"Select difficulty"}),(0,gr.jsx)("option",{value:"easy",children:"Easy"}),(0,gr.jsx)("option",{value:"medium",children:"Medium"}),(0,gr.jsx)("option",{value:"hard",children:"Hard"})]})]})]}),"book"===o.type&&(0,gr.jsxs)(gr.Fragment,{children:[(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Author"}),(0,gr.jsx)("input",{type:"text",value:o.author||"",onChange:e=>i(Fe(Fe({},o),{},{author:e.target.value}))})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Publisher"}),(0,gr.jsx)("input",{type:"text",value:o.publisher||"",onChange:e=>i(Fe(Fe({},o),{},{publisher:e.target.value}))})]})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Year"}),(0,gr.jsx)("input",{type:"number",value:o.year||"",onChange:e=>i(Fe(Fe({},o),{},{year:parseInt(e.target.value)}))})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Total Pages"}),(0,gr.jsx)("input",{type:"number",value:o.total_pages||"",onChange:e=>i(Fe(Fe({},o),{},{total_pages:parseInt(e.target.value)}))})]})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"ISBN"}),(0,gr.jsx)("input",{type:"text",value:o.isbn||"",onChange:e=>i(Fe(Fe({},o),{},{isbn:e.target.value}))})]})]}),"video"===o.type&&(0,gr.jsxs)(gr.Fragment,{children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Video URL"}),(0,gr.jsx)("input",{type:"url",value:o.url||"",onChange:e=>i(Fe(Fe({},o),{},{url:e.target.value})),placeholder:"https://youtube.com/..."})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Channel Name"}),(0,gr.jsx)("input",{type:"text",value:o.channel_name||"",onChange:e=>i(Fe(Fe({},o),{},{channel_name:e.target.value}))})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Duration"}),(0,gr.jsx)("input",{type:"text",value:o.duration||"",onChange:e=>i(Fe(Fe({},o),{},{duration:e.target.value})),placeholder:"1:30:45"})]})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Total Time (seconds)"}),(0,gr.jsx)("input",{type:"number",value:o.total_time||"",onChange:e=>i(Fe(Fe({},o),{},{total_time:parseInt(e.target.value)}))})]})]}),"pdf"===o.type&&(0,gr.jsxs)(gr.Fragment,{children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"PDF URL"}),(0,gr.jsx)("input",{type:"url",value:o.url||"",onChange:e=>i(Fe(Fe({},o),{},{url:e.target.value})),placeholder:"https://example.com/file.pdf"})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Total Pages"}),(0,gr.jsx)("input",{type:"number",value:o.total_pages||"",onChange:e=>i(Fe(Fe({},o),{},{total_pages:parseInt(e.target.value)}))})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Author"}),(0,gr.jsx)("input",{type:"text",value:o.author||"",onChange:e=>i(Fe(Fe({},o),{},{author:e.target.value}))})]})]})]}),(0,gr.jsx)("div",{className:"form-group",children:(0,gr.jsxs)("label",{children:[(0,gr.jsx)("input",{type:"checkbox",checked:o.is_official||!1,onChange:e=>i(Fe(Fe({},o),{},{is_official:e.target.checked}))}),"Official KPSS Content"]})}),(0,gr.jsxs)("div",{className:"actions",children:[(0,gr.jsx)("button",{type:"submit",className:"btn btn-success",children:"Save"}),(0,gr.jsx)("button",{type:"button",className:"btn btn-danger",onClick:()=>l(!1),children:"Cancel"})]})]})]})})]})},Ba=()=>{const[e,t]=(0,r.useState)([]),[n,a]=(0,r.useState)(!0),[o,i]=(0,r.useState)(null),[s,l]=(0,r.useState)(!1),[u,c]=(0,r.useState)(!1),[d,f]=(0,r.useState)([]),p={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:8000/api/v1",REACT_APP_NAME:"KPSS Plus",REACT_APP_VERSION:"1.0.0",REACT_APP_ENABLE_SOCIAL_LOGIN:"true",REACT_APP_ENABLE_NOTIFICATIONS:"true",REACT_APP_ENABLE_ANALYTICS:"false"}.REACT_APP_ADMIN_API_URL||"http://localhost:8100/api",h=(0,r.useCallback)(async()=>{const e=localStorage.getItem("adminToken");try{const n=await cr.get("".concat(p,"/quizzes?limit=20"),{headers:{Authorization:"Bearer ".concat(e)}});t(n.data.data||[])}catch(n){console.error("Failed to fetch quizzes:",n)}finally{a(!1)}},[p]);(0,r.useEffect)(()=>{h()},[h]);const m=(e,t,n)=>{const r=[...d];r[e]=Fe(Fe({},r[e]),{},{[t]:n}),f(r)},g=e=>{switch(e){case"personal":return"Personal";case"shared":return"Shared";case"official":return"Official";default:return e}};return n?(0,gr.jsx)("div",{className:"loading",children:"Loading quizzes..."}):(0,gr.jsxs)("div",{children:[(0,gr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[(0,gr.jsx)("h2",{children:"Quizzes"}),(0,gr.jsx)("button",{className:"btn btn-success",onClick:()=>{i({title:"",description:"",type:"official",subject:"",difficulty:"medium",time_limit:null,is_public:!0,is_active:!0}),l(!0)},children:"Create New Quiz"})]}),(0,gr.jsxs)("div",{className:"table-container",children:[(0,gr.jsx)("h3",{children:"Manage Quizzes"}),(0,gr.jsxs)("table",{children:[(0,gr.jsx)("thead",{children:(0,gr.jsxs)("tr",{children:[(0,gr.jsx)("th",{children:"Title"}),(0,gr.jsx)("th",{children:"Type"}),(0,gr.jsx)("th",{children:"Subject"}),(0,gr.jsx)("th",{children:"Difficulty"}),(0,gr.jsx)("th",{children:"Time Limit"}),(0,gr.jsx)("th",{children:"Questions"}),(0,gr.jsx)("th",{children:"Created"}),(0,gr.jsx)("th",{children:"Actions"})]})}),(0,gr.jsx)("tbody",{children:e.map(e=>(0,gr.jsxs)("tr",{children:[(0,gr.jsx)("td",{children:e.title}),(0,gr.jsx)("td",{children:g(e.type)}),(0,gr.jsx)("td",{children:e.subject||"-"}),(0,gr.jsx)("td",{children:e.difficulty||"-"}),(0,gr.jsx)("td",{children:e.time_limit?"".concat(e.time_limit,"s"):"-"}),(0,gr.jsx)("td",{children:e.question_count||0}),(0,gr.jsx)("td",{children:new Date(e.created_at).toLocaleDateString()}),(0,gr.jsxs)("td",{className:"actions",children:[(0,gr.jsx)("button",{className:"btn btn-primary",onClick:()=>(e=>{i(e),l(!0)})(e),children:"Edit"}),(0,gr.jsx)("button",{className:"btn btn-info",onClick:()=>(e=>{i(e);const t=localStorage.getItem("adminToken");cr.get("".concat(p,"/questions?quiz_id=").concat(e.id),{headers:{Authorization:"Bearer ".concat(t)}}).then(e=>{f(e.data.data||[]),c(!0)}).catch(e=>{console.error("Failed to fetch questions:",e),f([]),c(!0)})})(e),children:"Questions"}),(0,gr.jsx)("button",{className:"btn btn-danger",onClick:()=>(async e=>{if(!window.confirm("Are you sure you want to delete this quiz? All questions will also be deleted."))return;const t=localStorage.getItem("adminToken");try{await cr.delete("".concat(p,"/quizzes/").concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),h()}catch(n){console.error("Failed to delete quiz:",n)}})(e.id),children:"Delete"})]})]},e.id))})]})]}),s&&(0,gr.jsx)("div",{className:"modal",children:(0,gr.jsxs)("div",{className:"modal-content",children:[(0,gr.jsxs)("div",{className:"modal-header",children:[(0,gr.jsx)("h3",{children:o.id?"Edit Quiz":"Create New Quiz"}),(0,gr.jsx)("button",{className:"close-btn",onClick:()=>l(!1),children:"\xd7"})]}),(0,gr.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t=localStorage.getItem("adminToken");try{const e=Fe({},o);let n;if(Object.keys(e).forEach(t=>{""!==e[t]&&null!==e[t]||delete e[t]}),o.id){n=(await cr.put("".concat(p,"/quizzes/").concat(o.id),e,{headers:{Authorization:"Bearer ".concat(t)}})).data}else{n=(await cr.post("".concat(p,"/quizzes"),e,{headers:{Authorization:"Bearer ".concat(t)}})).data}l(!1),o.id||window.confirm("Quiz created successfully! Would you like to add questions now?")&&(i(n),f([]),c(!0)),h()}catch(n){console.error("Failed to save quiz:",n)}},children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Title *"}),(0,gr.jsx)("input",{type:"text",value:o.title,onChange:e=>i(Fe(Fe({},o),{},{title:e.target.value})),required:!0})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Description"}),(0,gr.jsx)("textarea",{value:o.description||"",onChange:e=>i(Fe(Fe({},o),{},{description:e.target.value})),rows:3})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Type"}),(0,gr.jsxs)("select",{value:o.type,onChange:e=>i(Fe(Fe({},o),{},{type:e.target.value})),required:!0,children:[(0,gr.jsx)("option",{value:"personal",children:"Personal"}),(0,gr.jsx)("option",{value:"shared",children:"Shared"}),(0,gr.jsx)("option",{value:"official",children:"Official"})]})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Subject"}),(0,gr.jsxs)("select",{value:o.subject||"",onChange:e=>i(Fe(Fe({},o),{},{subject:e.target.value})),children:[(0,gr.jsx)("option",{value:"",children:"Select subject"}),(0,gr.jsx)("option",{value:"Genel K\xfclt\xfcr",children:"Genel K\xfclt\xfcr"}),(0,gr.jsx)("option",{value:"Genel Yetenek",children:"Genel Yetenek"}),(0,gr.jsx)("option",{value:"E\u011fitim Bilimleri",children:"E\u011fitim Bilimleri"}),(0,gr.jsx)("option",{value:"\xd6ABT",children:"\xd6ABT"})]})]})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Difficulty"}),(0,gr.jsxs)("select",{value:o.difficulty||"",onChange:e=>i(Fe(Fe({},o),{},{difficulty:e.target.value})),children:[(0,gr.jsx)("option",{value:"",children:"Select difficulty"}),(0,gr.jsx)("option",{value:"easy",children:"Easy"}),(0,gr.jsx)("option",{value:"medium",children:"Medium"}),(0,gr.jsx)("option",{value:"hard",children:"Hard"})]})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Time Limit (seconds)"}),(0,gr.jsx)("input",{type:"number",value:o.time_limit||"",onChange:e=>i(Fe(Fe({},o),{},{time_limit:parseInt(e.target.value)})),placeholder:"Leave empty for no limit"})]})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsx)("div",{className:"form-group",children:(0,gr.jsxs)("label",{children:[(0,gr.jsx)("input",{type:"checkbox",checked:o.is_public,onChange:e=>i(Fe(Fe({},o),{},{is_public:e.target.checked}))}),"Public"]})}),(0,gr.jsx)("div",{className:"form-group",children:(0,gr.jsxs)("label",{children:[(0,gr.jsx)("input",{type:"checkbox",checked:o.is_active,onChange:e=>i(Fe(Fe({},o),{},{is_active:e.target.checked}))}),"Active"]})})]}),(0,gr.jsxs)("div",{className:"actions",children:[(0,gr.jsx)("button",{type:"submit",className:"btn btn-success",children:"Save Quiz"}),(0,gr.jsx)("button",{type:"button",className:"btn btn-danger",onClick:()=>l(!1),children:"Cancel"})]})]})]})}),u&&(0,gr.jsx)("div",{className:"modal",children:(0,gr.jsxs)("div",{className:"modal-content",style:{maxWidth:"900px"},children:[(0,gr.jsxs)("div",{className:"modal-header",children:[(0,gr.jsxs)("h3",{children:["Manage Questions - ",null===o||void 0===o?void 0:o.title]}),(0,gr.jsx)("button",{className:"close-btn",onClick:()=>c(!1),children:"\xd7"})]}),(0,gr.jsxs)("div",{className:"questions-manager",children:[(0,gr.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,gr.jsx)("button",{className:"btn btn-success",onClick:()=>{const e={id:"temp_"+Date.now(),text:"",option_a:"",option_b:"",option_c:"",option_d:"",option_e:"",correct_answer:"A",explanation:"",subject:"",year:null,order_index:d.length};f([...d,e])},children:"Add Question"}),(0,gr.jsx)("button",{className:"btn btn-primary",onClick:async()=>{const e=localStorage.getItem("adminToken");try{for(const t of d){const n=Fe({},t);delete n.id,t.id&&!t.id.toString().startsWith("temp_")?await cr.put("".concat(p,"/admin/questions/").concat(t.id),n,{headers:{Authorization:"Bearer ".concat(e)}}):await cr.post("".concat(p,"/admin/quizzes/").concat(o.id,"/questions"),n,{headers:{Authorization:"Bearer ".concat(e)}})}c(!1),h()}catch(t){console.error("Failed to save questions:",t)}},style:{marginLeft:"10px"},children:"Save All Questions"})]}),(0,gr.jsx)("div",{className:"questions-list",children:d.map((e,t)=>(0,gr.jsxs)("div",{className:"question-item",style:{border:"1px solid #ddd",padding:"15px",marginBottom:"15px",borderRadius:"5px",backgroundColor:"#f9f9f9"},children:[(0,gr.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"10px"},children:[(0,gr.jsxs)("h4",{children:["Question #",t+1]}),(0,gr.jsx)("button",{className:"btn btn-danger",onClick:()=>(e=>{if(window.confirm("Remove this question?")){const t=[...d];t.splice(e,1),f(t)}})(t),style:{padding:"5px 10px"},children:"Remove"})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Question Text *"}),(0,gr.jsx)("textarea",{value:e.text,onChange:e=>m(t,"text",e.target.value),required:!0,rows:3,style:{width:"100%"}})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Option A *"}),(0,gr.jsx)("input",{type:"text",value:e.option_a,onChange:e=>m(t,"option_a",e.target.value),required:!0,style:{width:"100%"}})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Option B *"}),(0,gr.jsx)("input",{type:"text",value:e.option_b,onChange:e=>m(t,"option_b",e.target.value),required:!0,style:{width:"100%"}})]})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Option C *"}),(0,gr.jsx)("input",{type:"text",value:e.option_c,onChange:e=>m(t,"option_c",e.target.value),required:!0,style:{width:"100%"}})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Option D *"}),(0,gr.jsx)("input",{type:"text",value:e.option_d,onChange:e=>m(t,"option_d",e.target.value),required:!0,style:{width:"100%"}})]})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Option E (Optional)"}),(0,gr.jsx)("input",{type:"text",value:e.option_e||"",onChange:e=>m(t,"option_e",e.target.value),style:{width:"100%"}})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Correct Answer *"}),(0,gr.jsxs)("select",{value:e.correct_answer,onChange:e=>m(t,"correct_answer",e.target.value),required:!0,style:{width:"100%"},children:[(0,gr.jsx)("option",{value:"A",children:"A"}),(0,gr.jsx)("option",{value:"B",children:"B"}),(0,gr.jsx)("option",{value:"C",children:"C"}),(0,gr.jsx)("option",{value:"D",children:"D"}),e.option_e&&(0,gr.jsx)("option",{value:"E",children:"E"})]})]})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Explanation"}),(0,gr.jsx)("textarea",{value:e.explanation||"",onChange:e=>m(t,"explanation",e.target.value),rows:2,style:{width:"100%"}})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Subject"}),(0,gr.jsxs)("select",{value:e.subject||"",onChange:e=>m(t,"subject",e.target.value),style:{width:"100%"},children:[(0,gr.jsx)("option",{value:"",children:"Select subject"}),(0,gr.jsx)("option",{value:"Genel K\xfclt\xfcr",children:"Genel K\xfclt\xfcr"}),(0,gr.jsx)("option",{value:"Genel Yetenek",children:"Genel Yetenek"}),(0,gr.jsx)("option",{value:"E\u011fitim Bilimleri",children:"E\u011fitim Bilimleri"}),(0,gr.jsx)("option",{value:"\xd6ABT",children:"\xd6ABT"})]})]})]}),(0,gr.jsxs)("div",{className:"form-row",children:[(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Year (Optional)"}),(0,gr.jsx)("input",{type:"number",value:e.year||"",onChange:e=>m(t,"year",parseInt(e.target.value)),placeholder:"2023",style:{width:"100%"}})]}),(0,gr.jsxs)("div",{className:"form-group",children:[(0,gr.jsx)("label",{children:"Order"}),(0,gr.jsx)("input",{type:"number",value:e.order_index,onChange:e=>m(t,"order_index",parseInt(e.target.value)),style:{width:"100%"}})]})]})]},e.id))})]})]})})]})},qa=()=>{const e=Z();return(0,gr.jsxs)("div",{className:"sidebar",children:[(0,gr.jsx)("h2",{children:"Admin Panel"}),(0,gr.jsx)("ul",{children:[{path:"/admin/dashboard",label:"Dashboard",icon:"\ud83d\udcca"},{path:"/admin/users",label:"Users",icon:"\ud83d\udc65"},{path:"/admin/study-content",label:"Study Content",icon:"\ud83d\udcda"},{path:"/admin/quizzes",label:"Quizzes",icon:"\ud83d\udcdd"}].map(t=>(0,gr.jsx)("li",{children:(0,gr.jsxs)(_e,{to:t.path,className:e.pathname===t.path?"active":"",children:[(0,gr.jsx)("span",{style:{marginRight:"10px"},children:t.icon}),t.label]})},t.path))})]})},Ha=()=>{const e=te(),t=localStorage.getItem("adminUser")?JSON.parse(localStorage.getItem("adminUser")):{};return(0,gr.jsxs)("div",{className:"header",children:[(0,gr.jsxs)("h1",{children:["Welcome, ",t.name||"Admin"]}),(0,gr.jsx)("button",{className:"logout-btn",onClick:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),e("/admin")},children:"Logout"})]})},Va=e=>{let{children:t}=e;const n=localStorage.getItem("adminToken"),r=localStorage.getItem("adminUser");return n&&r?t:(0,gr.jsx)(ve,{to:"/admin",replace:!0})},Wa=()=>{const e=te(),t=Z(),n=localStorage.getItem("adminToken"),r=localStorage.getItem("adminUser");return n&&r?(0,gr.jsxs)("div",{className:"admin-panel",children:[(0,gr.jsx)(qa,{}),(0,gr.jsxs)("div",{className:"main-content",children:[(0,gr.jsx)(Ha,{}),(0,gr.jsx)("div",{className:"content",children:(0,gr.jsxs)(we,{children:[(0,gr.jsx)(be,{path:"/",element:(0,gr.jsx)(ve,{to:"/admin/dashboard",replace:!0})}),(0,gr.jsx)(be,{path:"/dashboard",element:(0,gr.jsx)(Va,{children:(0,gr.jsx)(Ia,{})})}),(0,gr.jsx)(be,{path:"/users",element:(0,gr.jsx)(Va,{children:(0,gr.jsx)(Ua,{})})}),(0,gr.jsx)(be,{path:"/study-content",element:(0,gr.jsx)(Va,{children:(0,gr.jsx)(Ma,{})})}),(0,gr.jsx)(be,{path:"/quizzes",element:(0,gr.jsx)(Va,{children:(0,gr.jsx)(Ba,{})})}),(0,gr.jsx)(be,{path:"*",element:(0,gr.jsx)(ve,{to:"/admin/dashboard",replace:!0})})]})})]})]}):"/admin/login"!==window.location.pathname?(e("/admin/login",{state:{from:t},replace:!0}),null):(0,gr.jsx)(Fa,{})};function Qa(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ka(e,t){return Ka=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ka(e,t)}function $a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ka(e,t)}function Ga(e,t){if(t&&("object"==Ae(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Qa(e)}function Ja(e){return Ja=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ja(e)}function Ya(e){return wa(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Sa(e)||ja()}function Xa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Za(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xa(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xa(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}var eo={type:"logger",log:function(e){this.output("log",e)},warn:function(e){this.output("warn",e)},error:function(e){this.output("error",e)},output:function(e,t){console&&console[e]&&console[e].apply(console,t)}},to=new(function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ma(this,e),this.init(t,n)}return va(e,[{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||eo,this.options=t,this.debug=t.debug}},{key:"setDebug",value:function(e){this.debug=e}},{key:"log",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}},{key:"warn",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}},{key:"error",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}},{key:"deprecate",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}},{key:"forward",value:function(e,t,n,r){return r&&!this.debug?null:("string"===typeof e[0]&&(e[0]="".concat(n).concat(this.prefix," ").concat(e[0])),this.logger[t](e))}},{key:"create",value:function(t){return new e(this.logger,Za(Za({},{prefix:"".concat(this.prefix,":").concat(t,":")}),this.options))}},{key:"clone",value:function(t){return(t=t||this.options).prefix=t.prefix||this.prefix,new e(this.logger,t)}}]),e}()),no=function(){function e(){ma(this,e),this.observers={}}return va(e,[{key:"on",value:function(e,t){var n=this;return e.split(" ").forEach(function(e){n.observers[e]=n.observers[e]||[],n.observers[e].push(t)}),this}},{key:"off",value:function(e,t){this.observers[e]&&(t?this.observers[e]=this.observers[e].filter(function(e){return e!==t}):delete this.observers[e])}},{key:"emit",value:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.observers[e]&&[].concat(this.observers[e]).forEach(function(e){e.apply(void 0,n)});this.observers["*"]&&[].concat(this.observers["*"]).forEach(function(t){t.apply(t,[e].concat(n))})}}]),e}();function ro(){var e,t,n=new Promise(function(n,r){e=n,t=r});return n.resolve=e,n.reject=t,n}function ao(e){return null==e?"":""+e}function oo(e,t,n){function r(e){return e&&e.indexOf("###")>-1?e.replace(/###/g,"."):e}function a(){return!e||"string"===typeof e}for(var o="string"!==typeof t?[].concat(t):t.split(".");o.length>1;){if(a())return{};var i=r(o.shift());!e[i]&&n&&(e[i]=new n),e=Object.prototype.hasOwnProperty.call(e,i)?e[i]:{}}return a()?{}:{obj:e,k:r(o.shift())}}function io(e,t,n){var r=oo(e,t,Object);r.obj[r.k]=n}function so(e,t){var n=oo(e,t),r=n.obj,a=n.k;if(r)return r[a]}function lo(e,t,n){for(var r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?"string"===typeof e[r]||e[r]instanceof String||"string"===typeof t[r]||t[r]instanceof String?n&&(e[r]=t[r]):lo(e[r],t[r],n):e[r]=t[r]);return e}function uo(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}var co={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function fo(e){return"string"===typeof e?e.replace(/[&<>"'\/]/g,function(e){return co[e]}):e}var po="undefined"!==typeof window&&window.navigator&&"undefined"===typeof window.navigator.userAgentData&&window.navigator.userAgent&&window.navigator.userAgent.indexOf("MSIE")>-1,ho=[" ",",","?","!",";"];function mo(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(e){if(e[t])return e[t];for(var r=t.split(n),a=e,o=0;o<r.length;++o){if(!a)return;if("string"===typeof a[r[o]]&&o+1<r.length)return;if(void 0===a[r[o]]){for(var i=2,s=r.slice(o,o+i).join(n),l=a[s];void 0===l&&r.length>o+i;)i++,l=a[s=r.slice(o,o+i).join(n)];if(void 0===l)return;if(null===l)return null;if(t.endsWith(s)){if("string"===typeof l)return l;if(s&&"string"===typeof l[s])return l[s]}var u=r.slice(o+i).join(n);return u?mo(l,u,n):void 0}a=a[r[o]]}return a}}function go(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function vo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?go(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):go(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function yo(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(Wi){return!1}}();return function(){var n,r=Ja(e);if(t){var a=Ja(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return Ga(this,n)}}var bo=function(e){$a(n,e);var t=yo(n);function n(e){var r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};return ma(this,n),r=t.call(this),po&&no.call(Qa(r)),r.data=e||{},r.options=a,void 0===r.options.keySeparator&&(r.options.keySeparator="."),void 0===r.options.ignoreJSONStructure&&(r.options.ignoreJSONStructure=!0),r}return va(n,[{key:"addNamespaces",value:function(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}},{key:"removeNamespaces",value:function(e){var t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}},{key:"getResource",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,o=void 0!==r.ignoreJSONStructure?r.ignoreJSONStructure:this.options.ignoreJSONStructure,i=[e,t];n&&"string"!==typeof n&&(i=i.concat(n)),n&&"string"===typeof n&&(i=i.concat(a?n.split(a):n)),e.indexOf(".")>-1&&(i=e.split("."));var s=so(this.data,i);return s||!o||"string"!==typeof n?s:mo(this.data&&this.data[e]&&this.data[e][t],n,a)}},{key:"addResource",value:function(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},o=void 0!==a.keySeparator?a.keySeparator:this.options.keySeparator,i=[e,t];n&&(i=i.concat(o?n.split(o):n)),e.indexOf(".")>-1&&(r=t,t=(i=e.split("."))[1]),this.addNamespaces(t),io(this.data,i,r),a.silent||this.emit("added",e,t,n,r)}},{key:"addResources",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(var a in n)"string"!==typeof n[a]&&"[object Array]"!==Object.prototype.toString.apply(n[a])||this.addResource(e,t,a,n[a],{silent:!0});r.silent||this.emit("added",e,t,n)}},{key:"addResourceBundle",value:function(e,t,n,r,a){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1},i=[e,t];e.indexOf(".")>-1&&(r=n,n=t,t=(i=e.split("."))[1]),this.addNamespaces(t);var s=so(this.data,i)||{};r?lo(s,n,a):s=vo(vo({},s),n),io(this.data,i,s),o.silent||this.emit("added",e,t,n)}},{key:"removeResourceBundle",value:function(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}},{key:"hasResourceBundle",value:function(e,t){return void 0!==this.getResource(e,t)}},{key:"getResourceBundle",value:function(e,t){return t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI?vo(vo({},{}),this.getResource(e,t)):this.getResource(e,t)}},{key:"getDataByLanguage",value:function(e){return this.data[e]}},{key:"hasLanguageSomeTranslations",value:function(e){var t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(function(e){return t[e]&&Object.keys(t[e]).length>0})}},{key:"toJSON",value:function(){return this.data}}]),n}(no),xo={processors:{},addPostProcessor:function(e){this.processors[e.name]=e},handle:function(e,t,n,r,a){var o=this;return e.forEach(function(e){o.processors[e]&&(t=o.processors[e].process(t,n,r,a))}),t}};function wo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function ko(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wo(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wo(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function So(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(Wi){return!1}}();return function(){var n,r=Ja(e);if(t){var a=Ja(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return Ga(this,n)}}var jo={},No=function(e){$a(n,e);var t=So(n);function n(e){var r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return ma(this,n),r=t.call(this),po&&no.call(Qa(r)),function(e,t,n){e.forEach(function(e){t[e]&&(n[e]=t[e])})}(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,Qa(r)),r.options=a,void 0===r.options.keySeparator&&(r.options.keySeparator="."),r.logger=to.create("translator"),r}return va(n,[{key:"changeLanguage",value:function(e){e&&(this.language=e)}},{key:"exists",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(void 0===e||null===e)return!1;var n=this.resolve(e,t);return n&&void 0!==n.res}},{key:"extractFromKey",value:function(e,t){var n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");var r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,a=t.ns||this.options.defaultNS||[],o=n&&e.indexOf(n)>-1,i=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!function(e,t,n){t=t||"",n=n||"";var r=ho.filter(function(e){return t.indexOf(e)<0&&n.indexOf(e)<0});if(0===r.length)return!0;var a=new RegExp("(".concat(r.map(function(e){return"?"===e?"\\?":e}).join("|"),")")),o=!a.test(e);if(!o){var i=e.indexOf(n);i>0&&!a.test(e.substring(0,i))&&(o=!0)}return o}(e,n,r);if(o&&!i){var s=e.match(this.interpolator.nestingRegexp);if(s&&s.length>0)return{key:e,namespaces:a};var l=e.split(n);(n!==r||n===r&&this.options.ns.indexOf(l[0])>-1)&&(a=l.shift()),e=l.join(r)}return"string"===typeof a&&(a=[a]),{key:e,namespaces:a}}},{key:"translate",value:function(e,t,r){var a=this;if("object"!==Ae(t)&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"===Ae(t)&&(t=ko({},t)),t||(t={}),void 0===e||null===e)return"";Array.isArray(e)||(e=[String(e)]);var o=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,i=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,s=this.extractFromKey(e[e.length-1],t),l=s.key,u=s.namespaces,c=u[u.length-1],d=t.lng||this.language,f=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(d&&"cimode"===d.toLowerCase()){if(f){var p=t.nsSeparator||this.options.nsSeparator;return o?{res:"".concat(c).concat(p).concat(l),usedKey:l,exactUsedKey:l,usedLng:d,usedNS:c}:"".concat(c).concat(p).concat(l)}return o?{res:l,usedKey:l,exactUsedKey:l,usedLng:d,usedNS:c}:l}var h=this.resolve(e,t),m=h&&h.res,g=h&&h.usedKey||l,v=h&&h.exactUsedKey||l,y=Object.prototype.toString.apply(m),b=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,x=!this.i18nFormat||this.i18nFormat.handleAsObject;if(x&&m&&("string"!==typeof m&&"boolean"!==typeof m&&"number"!==typeof m)&&["[object Number]","[object Function]","[object RegExp]"].indexOf(y)<0&&("string"!==typeof b||"[object Array]"!==y)){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");var w=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,m,ko(ko({},t),{},{ns:u})):"key '".concat(l," (").concat(this.language,")' returned an object instead of string.");return o?(h.res=w,h):w}if(i){var k="[object Array]"===y,S=k?[]:{},j=k?v:g;for(var N in m)if(Object.prototype.hasOwnProperty.call(m,N)){var E="".concat(j).concat(i).concat(N);S[N]=this.translate(E,ko(ko({},t),{joinArrays:!1,ns:u})),S[N]===E&&(S[N]=m[N])}m=S}}else if(x&&"string"===typeof b&&"[object Array]"===y)(m=m.join(b))&&(m=this.extendTranslation(m,e,t,r));else{var O=!1,P=!1,C=void 0!==t.count&&"string"!==typeof t.count,_=n.hasDefaultValue(t),T=C?this.pluralResolver.getSuffix(d,t.count,t):"",R=t["defaultValue".concat(T)]||t.defaultValue;!this.isValidLookup(m)&&_&&(O=!0,m=R),this.isValidLookup(m)||(P=!0,m=l);var A=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&P?void 0:m,L=_&&R!==m&&this.options.updateMissing;if(P||O||L){if(this.logger.log(L?"updateKey":"missingKey",d,c,l,L?R:m),i){var z=this.resolve(l,ko(ko({},t),{},{keySeparator:!1}));z&&z.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}var D=[],F=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&F&&F[0])for(var I=0;I<F.length;I++)D.push(F[I]);else"all"===this.options.saveMissingTo?D=this.languageUtils.toResolveHierarchy(t.lng||this.language):D.push(t.lng||this.language);var U=function(e,n,r){var o=_&&r!==m?r:A;a.options.missingKeyHandler?a.options.missingKeyHandler(e,c,n,o,L,t):a.backendConnector&&a.backendConnector.saveMissing&&a.backendConnector.saveMissing(e,c,n,o,L,t),a.emit("missingKey",e,c,n,m)};this.options.saveMissing&&(this.options.saveMissingPlurals&&C?D.forEach(function(e){a.pluralResolver.getSuffixes(e,t).forEach(function(n){U([e],l+n,t["defaultValue".concat(n)]||R)})}):U(D,l,R))}m=this.extendTranslation(m,e,t,h,r),P&&m===l&&this.options.appendNamespaceToMissingKey&&(m="".concat(c,":").concat(l)),(P||O)&&this.options.parseMissingKeyHandler&&(m="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?"".concat(c,":").concat(l):l,O?m:void 0):this.options.parseMissingKeyHandler(m))}return o?(h.res=m,h):m}},{key:"extendTranslation",value:function(e,t,n,r,a){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,ko(ko({},this.options.interpolation.defaultVariables),n),r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init(ko(ko({},n),{interpolation:ko(ko({},this.options.interpolation),n.interpolation)}));var i,s="string"===typeof e&&(n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(s){var l=e.match(this.interpolator.nestingRegexp);i=l&&l.length}var u=n.replace&&"string"!==typeof n.replace?n.replace:n;if(this.options.interpolation.defaultVariables&&(u=ko(ko({},this.options.interpolation.defaultVariables),u)),e=this.interpolator.interpolate(e,u,n.lng||this.language,n),s){var c=e.match(this.interpolator.nestingRegexp);i<(c&&c.length)&&(n.nest=!1)}!n.lng&&"v1"!==this.options.compatibilityAPI&&r&&r.res&&(n.lng=r.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return a&&a[0]===r[0]&&!n.context?(o.logger.warn("It seems you are nesting recursively key: ".concat(r[0]," in key: ").concat(t[0])),null):o.translate.apply(o,r.concat([t]))},n)),n.interpolation&&this.interpolator.reset()}var d=n.postProcess||this.options.postProcess,f="string"===typeof d?[d]:d;return void 0!==e&&null!==e&&f&&f.length&&!1!==n.applyPostProcessor&&(e=xo.handle(f,e,t,this.options&&this.options.postProcessPassResolved?ko({i18nResolved:r},n):n,this)),e}},{key:"resolve",value:function(e){var t,n,r,a,o,i=this,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"string"===typeof e&&(e=[e]),e.forEach(function(e){if(!i.isValidLookup(t)){var l=i.extractFromKey(e,s),u=l.key;n=u;var c=l.namespaces;i.options.fallbackNS&&(c=c.concat(i.options.fallbackNS));var d=void 0!==s.count&&"string"!==typeof s.count,f=d&&!s.ordinal&&0===s.count&&i.pluralResolver.shouldUseIntlApi(),p=void 0!==s.context&&("string"===typeof s.context||"number"===typeof s.context)&&""!==s.context,h=s.lngs?s.lngs:i.languageUtils.toResolveHierarchy(s.lng||i.language,s.fallbackLng);c.forEach(function(e){i.isValidLookup(t)||(o=e,!jo["".concat(h[0],"-").concat(e)]&&i.utils&&i.utils.hasLoadedNamespace&&!i.utils.hasLoadedNamespace(o)&&(jo["".concat(h[0],"-").concat(e)]=!0,i.logger.warn('key "'.concat(n,'" for languages "').concat(h.join(", "),'" won\'t get resolved as namespace "').concat(o,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),h.forEach(function(n){if(!i.isValidLookup(t)){a=n;var o,l=[u];if(i.i18nFormat&&i.i18nFormat.addLookupKeys)i.i18nFormat.addLookupKeys(l,u,n,e,s);else{var c;d&&(c=i.pluralResolver.getSuffix(n,s.count,s));var h="".concat(i.options.pluralSeparator,"zero");if(d&&(l.push(u+c),f&&l.push(u+h)),p){var m="".concat(u).concat(i.options.contextSeparator).concat(s.context);l.push(m),d&&(l.push(m+c),f&&l.push(m+h))}}for(;o=l.pop();)i.isValidLookup(t)||(r=o,t=i.getResource(n,e,o,s))}}))})}}),{res:t,usedKey:n,exactUsedKey:r,usedLng:a,usedNS:o}}},{key:"isValidLookup",value:function(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}},{key:"getResource",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}}],[{key:"hasDefaultValue",value:function(e){var t="defaultValue";for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,12)&&void 0!==e[n])return!0;return!1}}]),n}(no);function Eo(e){return e.charAt(0).toUpperCase()+e.slice(1)}var Oo=function(){function e(t){ma(this,e),this.options=t,this.supportedLngs=this.options.supportedLngs||!1,this.logger=to.create("languageUtils")}return va(e,[{key:"getScriptPartFromCode",value:function(e){if(!e||e.indexOf("-")<0)return null;var t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}},{key:"getLanguagePartFromCode",value:function(e){if(!e||e.indexOf("-")<0)return e;var t=e.split("-");return this.formatLanguageCode(t[0])}},{key:"formatLanguageCode",value:function(e){if("string"===typeof e&&e.indexOf("-")>-1){var t=["hans","hant","latn","cyrl","cans","mong","arab"],n=e.split("-");return this.options.lowerCaseLng?n=n.map(function(e){return e.toLowerCase()}):2===n.length?(n[0]=n[0].toLowerCase(),n[1]=n[1].toUpperCase(),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=Eo(n[1].toLowerCase()))):3===n.length&&(n[0]=n[0].toLowerCase(),2===n[1].length&&(n[1]=n[1].toUpperCase()),"sgn"!==n[0]&&2===n[2].length&&(n[2]=n[2].toUpperCase()),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=Eo(n[1].toLowerCase())),t.indexOf(n[2].toLowerCase())>-1&&(n[2]=Eo(n[2].toLowerCase()))),n.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}},{key:"isSupportedCode",value:function(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}},{key:"getBestMatchFromCodes",value:function(e){var t,n=this;return e?(e.forEach(function(e){if(!t){var r=n.formatLanguageCode(e);n.options.supportedLngs&&!n.isSupportedCode(r)||(t=r)}}),!t&&this.options.supportedLngs&&e.forEach(function(e){if(!t){var r=n.getLanguagePartFromCode(e);if(n.isSupportedCode(r))return t=r;t=n.options.supportedLngs.find(function(e){return e===r?e:e.indexOf("-")<0&&r.indexOf("-")<0?void 0:0===e.indexOf(r)?e:void 0})}}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}},{key:"getFallbackCodes",value:function(e,t){if(!e)return[];if("function"===typeof e&&(e=e(t)),"string"===typeof e&&(e=[e]),"[object Array]"===Object.prototype.toString.apply(e))return e;if(!t)return e.default||[];var n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}},{key:"toResolveHierarchy",value:function(e,t){var n=this,r=this.getFallbackCodes(t||this.options.fallbackLng||[],e),a=[],o=function(e){e&&(n.isSupportedCode(e)?a.push(e):n.logger.warn("rejecting language code not found in supportedLngs: ".concat(e)))};return"string"===typeof e&&e.indexOf("-")>-1?("languageOnly"!==this.options.load&&o(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&o(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&o(this.getLanguagePartFromCode(e))):"string"===typeof e&&o(this.formatLanguageCode(e)),r.forEach(function(e){a.indexOf(e)<0&&o(n.formatLanguageCode(e))}),a}}]),e}(),Po=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],Co={1:function(e){return Number(e>1)},2:function(e){return Number(1!=e)},3:function(e){return 0},4:function(e){return Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2)},5:function(e){return Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5)},6:function(e){return Number(1==e?0:e>=2&&e<=4?1:2)},7:function(e){return Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2)},8:function(e){return Number(1==e?0:2==e?1:8!=e&&11!=e?2:3)},9:function(e){return Number(e>=2)},10:function(e){return Number(1==e?0:2==e?1:e<7?2:e<11?3:4)},11:function(e){return Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3)},12:function(e){return Number(e%10!=1||e%100==11)},13:function(e){return Number(0!==e)},14:function(e){return Number(1==e?0:2==e?1:3==e?2:3)},15:function(e){return Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2)},16:function(e){return Number(e%10==1&&e%100!=11?0:0!==e?1:2)},17:function(e){return Number(1==e||e%10==1&&e%100!=11?0:1)},18:function(e){return Number(0==e?0:1==e?1:2)},19:function(e){return Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3)},20:function(e){return Number(1==e?0:0==e||e%100>0&&e%100<20?1:2)},21:function(e){return Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0)},22:function(e){return Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)}},_o=["v1","v2","v3"],To={zero:0,one:1,two:2,few:3,many:4,other:5};var Ro=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ma(this,e),this.languageUtils=t,this.options=n,this.logger=to.create("pluralResolver"),this.options.compatibilityJSON&&"v4"!==this.options.compatibilityJSON||"undefined"!==typeof Intl&&Intl.PluralRules||(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=function(){var e={};return Po.forEach(function(t){t.lngs.forEach(function(n){e[n]={numbers:t.nr,plurals:Co[t.fc]}})}),e}()}return va(e,[{key:"addRule",value:function(e,t){this.rules[e]=t}},{key:"getRule",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi())try{return new Intl.PluralRules(e,{type:t.ordinal?"ordinal":"cardinal"})}catch(n){return}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}},{key:"needsPlural",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.getRule(e,t);return this.shouldUseIntlApi()?n&&n.resolvedOptions().pluralCategories.length>1:n&&n.numbers.length>1}},{key:"getPluralFormsOfKey",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,n).map(function(e){return"".concat(t).concat(e)})}},{key:"getSuffixes",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.getRule(e,n);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort(function(e,t){return To[e]-To[t]}).map(function(e){return"".concat(t.options.prepend).concat(e)}):r.numbers.map(function(r){return t.getSuffix(e,r,n)}):[]}},{key:"getSuffix",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=this.getRule(e,n);return r?this.shouldUseIntlApi()?"".concat(this.options.prepend).concat(r.select(t)):this.getSuffixRetroCompatible(r,t):(this.logger.warn("no plural rule found for: ".concat(e)),"")}},{key:"getSuffixRetroCompatible",value:function(e,t){var n=this,r=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),a=e.numbers[r];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===a?a="plural":1===a&&(a=""));var o=function(){return n.options.prepend&&a.toString()?n.options.prepend+a.toString():a.toString()};return"v1"===this.options.compatibilityJSON?1===a?"":"number"===typeof a?"_plural_".concat(a.toString()):o():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?o():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}},{key:"shouldUseIntlApi",value:function(){return!_o.includes(this.options.compatibilityJSON)}}]),e}();function Ao(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Lo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ao(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ao(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function zo(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],o=function(e,t,n){var r=so(e,n);return void 0!==r?r:so(t,n)}(e,t,n);return!o&&a&&"string"===typeof n&&void 0===(o=mo(e,n,r))&&(o=mo(t,n,r)),o}var Do=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};ma(this,e),this.logger=to.create("interpolator"),this.options=t,this.format=t.interpolation&&t.interpolation.format||function(e){return e},this.init(t)}return va(e,[{key:"init",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});var t=e.interpolation;this.escape=void 0!==t.escape?t.escape:fo,this.escapeValue=void 0===t.escapeValue||t.escapeValue,this.useRawValueToEscape=void 0!==t.useRawValueToEscape&&t.useRawValueToEscape,this.prefix=t.prefix?uo(t.prefix):t.prefixEscaped||"{{",this.suffix=t.suffix?uo(t.suffix):t.suffixEscaped||"}}",this.formatSeparator=t.formatSeparator?t.formatSeparator:t.formatSeparator||",",this.unescapePrefix=t.unescapeSuffix?"":t.unescapePrefix||"-",this.unescapeSuffix=this.unescapePrefix?"":t.unescapeSuffix||"",this.nestingPrefix=t.nestingPrefix?uo(t.nestingPrefix):t.nestingPrefixEscaped||uo("$t("),this.nestingSuffix=t.nestingSuffix?uo(t.nestingSuffix):t.nestingSuffixEscaped||uo(")"),this.nestingOptionsSeparator=t.nestingOptionsSeparator?t.nestingOptionsSeparator:t.nestingOptionsSeparator||",",this.maxReplaces=t.maxReplaces?t.maxReplaces:1e3,this.alwaysFormat=void 0!==t.alwaysFormat&&t.alwaysFormat,this.resetRegExp()}},{key:"reset",value:function(){this.options&&this.init(this.options)}},{key:"resetRegExp",value:function(){var e="".concat(this.prefix,"(.+?)").concat(this.suffix);this.regexp=new RegExp(e,"g");var t="".concat(this.prefix).concat(this.unescapePrefix,"(.+?)").concat(this.unescapeSuffix).concat(this.suffix);this.regexpUnescape=new RegExp(t,"g");var n="".concat(this.nestingPrefix,"(.+?)").concat(this.nestingSuffix);this.nestingRegexp=new RegExp(n,"g")}},{key:"interpolate",value:function(e,t,n,r){var a,o,i,s=this,l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{};function u(e){return e.replace(/\$/g,"$$$$")}var c=function(e){if(e.indexOf(s.formatSeparator)<0){var a=zo(t,l,e,s.options.keySeparator,s.options.ignoreJSONStructure);return s.alwaysFormat?s.format(a,void 0,n,Lo(Lo(Lo({},r),t),{},{interpolationkey:e})):a}var o=e.split(s.formatSeparator),i=o.shift().trim(),u=o.join(s.formatSeparator).trim();return s.format(zo(t,l,i,s.options.keySeparator,s.options.ignoreJSONStructure),u,n,Lo(Lo(Lo({},r),t),{},{interpolationkey:i}))};this.resetRegExp();var d=r&&r.missingInterpolationHandler||this.options.missingInterpolationHandler,f=r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:function(e){return u(e)}},{regex:this.regexp,safeValue:function(e){return s.escapeValue?u(s.escape(e)):u(e)}}].forEach(function(t){for(i=0;a=t.regex.exec(e);){var n=a[1].trim();if(void 0===(o=c(n)))if("function"===typeof d){var l=d(e,a,r);o="string"===typeof l?l:""}else if(r&&Object.prototype.hasOwnProperty.call(r,n))o="";else{if(f){o=a[0];continue}s.logger.warn("missed to pass in variable ".concat(n," for interpolating ").concat(e)),o=""}else"string"===typeof o||s.useRawValueToEscape||(o=ao(o));var u=t.safeValue(o);if(e=e.replace(a[0],u),f?(t.regex.lastIndex+=o.length,t.regex.lastIndex-=a[0].length):t.regex.lastIndex=0,++i>=s.maxReplaces)break}}),e}},{key:"nest",value:function(e,t){var n,r,a,o=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};function s(e,t){var n=this.nestingOptionsSeparator;if(e.indexOf(n)<0)return e;var r=e.split(new RegExp("".concat(n,"[ ]*{"))),o="{".concat(r[1]);e=r[0];var i=(o=this.interpolate(o,a)).match(/'/g),s=o.match(/"/g);(i&&i.length%2===0&&!s||s.length%2!==0)&&(o=o.replace(/'/g,'"'));try{a=JSON.parse(o),t&&(a=Lo(Lo({},t),a))}catch(Wi){return this.logger.warn("failed parsing options string in nesting for key ".concat(e),Wi),"".concat(e).concat(n).concat(o)}return delete a.defaultValue,e}for(;n=this.nestingRegexp.exec(e);){var l=[];(a=(a=Lo({},i)).replace&&"string"!==typeof a.replace?a.replace:a).applyPostProcessor=!1,delete a.defaultValue;var u=!1;if(-1!==n[0].indexOf(this.formatSeparator)&&!/{.*}/.test(n[1])){var c=n[1].split(this.formatSeparator).map(function(e){return e.trim()});n[1]=c.shift(),l=c,u=!0}if((r=t(s.call(this,n[1].trim(),a),a))&&n[0]===e&&"string"!==typeof r)return r;"string"!==typeof r&&(r=ao(r)),r||(this.logger.warn("missed to resolve ".concat(n[1]," for nesting ").concat(e)),r=""),u&&(r=l.reduce(function(e,t){return o.format(e,t,i.lng,Lo(Lo({},i),{},{interpolationkey:n[1].trim()}))},r.trim())),e=e.replace(n[0],r),this.regexp.lastIndex=0}return e}}]),e}();function Fo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Io(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Fo(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Fo(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Uo(e){var t={};return function(n,r,a){var o=r+JSON.stringify(a),i=t[o];return i||(i=e(r,a),t[o]=i),i(n)}}var Mo=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};ma(this,e),this.logger=to.create("formatter"),this.options=t,this.formats={number:Uo(function(e,t){var n=new Intl.NumberFormat(e,Io({},t));return function(e){return n.format(e)}}),currency:Uo(function(e,t){var n=new Intl.NumberFormat(e,Io(Io({},t),{},{style:"currency"}));return function(e){return n.format(e)}}),datetime:Uo(function(e,t){var n=new Intl.DateTimeFormat(e,Io({},t));return function(e){return n.format(e)}}),relativetime:Uo(function(e,t){var n=new Intl.RelativeTimeFormat(e,Io({},t));return function(e){return n.format(e,t.range||"day")}}),list:Uo(function(e,t){var n=new Intl.ListFormat(e,Io({},t));return function(e){return n.format(e)}})},this.init(t)}return va(e,[{key:"init",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}}).interpolation;this.formatSeparator=t.formatSeparator?t.formatSeparator:t.formatSeparator||","}},{key:"add",value:function(e,t){this.formats[e.toLowerCase().trim()]=t}},{key:"addCached",value:function(e,t){this.formats[e.toLowerCase().trim()]=Uo(t)}},{key:"format",value:function(e,t,n){var r=this,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return t.split(this.formatSeparator).reduce(function(e,t){var o=function(e){var t=e.toLowerCase().trim(),n={};if(e.indexOf("(")>-1){var r=e.split("(");t=r[0].toLowerCase().trim();var a=r[1].substring(0,r[1].length-1);"currency"===t&&a.indexOf(":")<0?n.currency||(n.currency=a.trim()):"relativetime"===t&&a.indexOf(":")<0?n.range||(n.range=a.trim()):a.split(";").forEach(function(e){if(e){var t=Ya(e.split(":")),r=t[0],a=t.slice(1).join(":").trim().replace(/^'+|'+$/g,"");n[r.trim()]||(n[r.trim()]=a),"false"===a&&(n[r.trim()]=!1),"true"===a&&(n[r.trim()]=!0),isNaN(a)||(n[r.trim()]=parseInt(a,10))}})}return{formatName:t,formatOptions:n}}(t),i=o.formatName,s=o.formatOptions;if(r.formats[i]){var l=e;try{var u=a&&a.formatParams&&a.formatParams[a.interpolationkey]||{},c=u.locale||u.lng||a.locale||a.lng||n;l=r.formats[i](e,c,Io(Io(Io({},s),a),u))}catch(d){r.logger.warn(d)}return l}return r.logger.warn("there was no format function for ".concat(i)),e},e)}}]),e}();function Bo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function qo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Bo(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Bo(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ho(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(Wi){return!1}}();return function(){var n,r=Ja(e);if(t){var a=Ja(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return Ga(this,n)}}var Vo=function(e){$a(n,e);var t=Ho(n);function n(e,r,a){var o,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return ma(this,n),o=t.call(this),po&&no.call(Qa(o)),o.backend=e,o.store=r,o.services=a,o.languageUtils=a.languageUtils,o.options=i,o.logger=to.create("backendConnector"),o.waitingReads=[],o.maxParallelReads=i.maxParallelReads||10,o.readingCalls=0,o.maxRetries=i.maxRetries>=0?i.maxRetries:5,o.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,o.state={},o.queue=[],o.backend&&o.backend.init&&o.backend.init(a,i.backend,i),o}return va(n,[{key:"queueLoad",value:function(e,t,n,r){var a=this,o={},i={},s={},l={};return e.forEach(function(e){var r=!0;t.forEach(function(t){var s="".concat(e,"|").concat(t);!n.reload&&a.store.hasResourceBundle(e,t)?a.state[s]=2:a.state[s]<0||(1===a.state[s]?void 0===i[s]&&(i[s]=!0):(a.state[s]=1,r=!1,void 0===i[s]&&(i[s]=!0),void 0===o[s]&&(o[s]=!0),void 0===l[t]&&(l[t]=!0)))}),r||(s[e]=!0)}),(Object.keys(o).length||Object.keys(i).length)&&this.queue.push({pending:i,pendingCount:Object.keys(i).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(o),pending:Object.keys(i),toLoadLanguages:Object.keys(s),toLoadNamespaces:Object.keys(l)}}},{key:"loaded",value:function(e,t,n){var r=e.split("|"),a=r[0],o=r[1];t&&this.emit("failedLoading",a,o,t),n&&this.store.addResourceBundle(a,o,n),this.state[e]=t?-1:2;var i={};this.queue.forEach(function(n){!function(e,t,n,r){var a=oo(e,t,Object),o=a.obj,i=a.k;o[i]=o[i]||[],r&&(o[i]=o[i].concat(n)),r||o[i].push(n)}(n.loaded,[a],o),function(e,t){void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)}(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach(function(e){i[e]||(i[e]={});var t=n.loaded[e];t.length&&t.forEach(function(t){void 0===i[e][t]&&(i[e][t]=!0)})}),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())}),this.emit("loaded",i),this.queue=this.queue.filter(function(e){return!e.done})}},{key:"read",value:function(e,t,n){var r=this,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,i=arguments.length>5?arguments[5]:void 0;if(!e.length)return i(null,{});if(this.readingCalls>=this.maxParallelReads)this.waitingReads.push({lng:e,ns:t,fcName:n,tried:a,wait:o,callback:i});else{this.readingCalls++;var s=function(s,l){if(r.readingCalls--,r.waitingReads.length>0){var u=r.waitingReads.shift();r.read(u.lng,u.ns,u.fcName,u.tried,u.wait,u.callback)}s&&l&&a<r.maxRetries?setTimeout(function(){r.read.call(r,e,t,n,a+1,2*o,i)},o):i(s,l)},l=this.backend[n].bind(this.backend);if(2!==l.length)return l(e,t,s);try{var u=l(e,t);u&&"function"===typeof u.then?u.then(function(e){return s(null,e)}).catch(s):s(null,u)}catch(c){s(c)}}}},{key:"prepareLoading",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),a&&a();"string"===typeof e&&(e=this.languageUtils.toResolveHierarchy(e)),"string"===typeof t&&(t=[t]);var o=this.queueLoad(e,t,r,a);if(!o.toLoad.length)return o.pending.length||a(),null;o.toLoad.forEach(function(e){n.loadOne(e)})}},{key:"load",value:function(e,t,n){this.prepareLoading(e,t,{},n)}},{key:"reload",value:function(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}},{key:"loadOne",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e.split("|"),a=r[0],o=r[1];this.read(a,o,"read",void 0,void 0,function(r,i){r&&t.logger.warn("".concat(n,"loading namespace ").concat(o," for language ").concat(a," failed"),r),!r&&i&&t.logger.log("".concat(n,"loaded namespace ").concat(o," for language ").concat(a),i),t.loaded(e,r,i)})}},{key:"saveMissing",value:function(e,t,n,r,a){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:function(){};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t))this.logger.warn('did not save key "'.concat(n,'" as the namespace "').concat(t,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(void 0!==n&&null!==n&&""!==n){if(this.backend&&this.backend.create){var s=qo(qo({},o),{},{isUpdate:a}),l=this.backend.create.bind(this.backend);if(l.length<6)try{var u;(u=5===l.length?l(e,t,n,r,s):l(e,t,n,r))&&"function"===typeof u.then?u.then(function(e){return i(null,e)}).catch(i):i(null,u)}catch(c){i(c)}else l(e,t,n,r,i,s)}e&&e[0]&&this.store.addResource(e[0],t,n,r)}}}]),n}(no);function Wo(){return{debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!0,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:function(e){var t={};if("object"===Ae(e[1])&&(t=e[1]),"string"===typeof e[1]&&(t.defaultValue=e[1]),"string"===typeof e[2]&&(t.tDescription=e[2]),"object"===Ae(e[2])||"object"===Ae(e[3])){var n=e[3]||e[2];Object.keys(n).forEach(function(e){t[e]=n[e]})}return t},interpolation:{escapeValue:!0,format:function(e,t,n,r){return e},prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}}function Qo(e){return"string"===typeof e.ns&&(e.ns=[e.ns]),"string"===typeof e.fallbackLng&&(e.fallbackLng=[e.fallbackLng]),"string"===typeof e.fallbackNS&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e}function Ko(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function $o(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ko(Object(n),!0).forEach(function(t){ze(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ko(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Go(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(Wi){return!1}}();return function(){var n,r=Ja(e);if(t){var a=Ja(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return Ga(this,n)}}function Jo(){}var Yo=function(e){$a(n,e);var t=Go(n);function n(){var e,r,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0;if(ma(this,n),e=t.call(this),po&&no.call(Qa(e)),e.options=Qo(a),e.services={},e.logger=to,e.modules={external:[]},r=Qa(e),Object.getOwnPropertyNames(Object.getPrototypeOf(r)).forEach(function(e){"function"===typeof r[e]&&(r[e]=r[e].bind(r))}),o&&!e.isInitialized&&!a.isClone){if(!e.options.initImmediate)return e.init(a,o),Ga(e,Qa(e));setTimeout(function(){e.init(a,o)},0)}return e}return va(n,[{key:"init",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;"function"===typeof t&&(n=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&("string"===typeof t.ns?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));var r=Wo();function a(e){return e?"function"===typeof e?new e:e:null}if(this.options=$o($o($o({},r),this.options),Qo(t)),"v1"!==this.options.compatibilityAPI&&(this.options.interpolation=$o($o({},r.interpolation),this.options.interpolation)),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator),!this.options.isClone){var o;this.modules.logger?to.init(a(this.modules.logger),this.options):to.init(null,this.options),this.modules.formatter?o=this.modules.formatter:"undefined"!==typeof Intl&&(o=Mo);var i=new Oo(this.options);this.store=new bo(this.options.resources,this.options);var s=this.services;s.logger=to,s.resourceStore=this.store,s.languageUtils=i,s.pluralResolver=new Ro(i,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!o||this.options.interpolation.format&&this.options.interpolation.format!==r.interpolation.format||(s.formatter=a(o),s.formatter.init(s,this.options),this.options.interpolation.format=s.formatter.format.bind(s.formatter)),s.interpolator=new Do(this.options),s.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},s.backendConnector=new Vo(a(this.modules.backend),s.resourceStore,s,this.options),s.backendConnector.on("*",function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];e.emit.apply(e,[t].concat(r))}),this.modules.languageDetector&&(s.languageDetector=a(this.modules.languageDetector),s.languageDetector.init&&s.languageDetector.init(s,this.options.detection,this.options)),this.modules.i18nFormat&&(s.i18nFormat=a(this.modules.i18nFormat),s.i18nFormat.init&&s.i18nFormat.init(this)),this.translator=new No(this.services,this.options),this.translator.on("*",function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];e.emit.apply(e,[t].concat(r))}),this.modules.external.forEach(function(t){t.init&&t.init(e)})}if(this.format=this.options.interpolation.format,n||(n=Jo),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){var l=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);l.length>0&&"dev"!==l[0]&&(this.options.lng=l[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(function(t){e[t]=function(){var n;return(n=e.store)[t].apply(n,arguments)}});["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(function(t){e[t]=function(){var n;return(n=e.store)[t].apply(n,arguments),e}});var u=ro(),c=function(){var t=function(t,r){e.isInitialized&&!e.initializedStoreOnce&&e.logger.warn("init: i18next is already initialized. You should call init just once!"),e.isInitialized=!0,e.options.isClone||e.logger.log("initialized",e.options),e.emit("initialized",e.options),u.resolve(r),n(t,r)};if(e.languages&&"v1"!==e.options.compatibilityAPI&&!e.isInitialized)return t(null,e.t.bind(e));e.changeLanguage(e.options.lng,t)};return this.options.resources||!this.options.initImmediate?c():setTimeout(c,0),u}},{key:"loadResources",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Jo,r="string"===typeof e?e:this.language;if("function"===typeof e&&(n=e),!this.options.resources||this.options.partialBundledLanguages){if(r&&"cimode"===r.toLowerCase())return n();var a=[],o=function(e){e&&t.services.languageUtils.toResolveHierarchy(e).forEach(function(e){a.indexOf(e)<0&&a.push(e)})};if(r)o(r);else this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(function(e){return o(e)});this.options.preload&&this.options.preload.forEach(function(e){return o(e)}),this.services.backendConnector.load(a,this.options.ns,function(e){e||t.resolvedLanguage||!t.language||t.setResolvedLanguage(t.language),n(e)})}else n(null)}},{key:"reloadResources",value:function(e,t,n){var r=ro();return e||(e=this.languages),t||(t=this.options.ns),n||(n=Jo),this.services.backendConnector.reload(e,t,function(e){r.resolve(),n(e)}),r}},{key:"use",value:function(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&xo.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}},{key:"setResolvedLanguage",value:function(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(var t=0;t<this.languages.length;t++){var n=this.languages[t];if(!(["cimode","dev"].indexOf(n)>-1)&&this.store.hasLanguageSomeTranslations(n)){this.resolvedLanguage=n;break}}}},{key:"changeLanguage",value:function(e,t){var n=this;this.isLanguageChangingTo=e;var r=ro();this.emit("languageChanging",e);var a=function(e){n.language=e,n.languages=n.services.languageUtils.toResolveHierarchy(e),n.resolvedLanguage=void 0,n.setResolvedLanguage(e)},o=function(o){e||o||!n.services.languageDetector||(o=[]);var i="string"===typeof o?o:n.services.languageUtils.getBestMatchFromCodes(o);i&&(n.language||a(i),n.translator.language||n.translator.changeLanguage(i),n.services.languageDetector&&n.services.languageDetector.cacheUserLanguage&&n.services.languageDetector.cacheUserLanguage(i)),n.loadResources(i,function(e){!function(e,o){o?(a(o),n.translator.changeLanguage(o),n.isLanguageChangingTo=void 0,n.emit("languageChanged",o),n.logger.log("languageChanged",o)):n.isLanguageChangingTo=void 0,r.resolve(function(){return n.t.apply(n,arguments)}),t&&t(e,function(){return n.t.apply(n,arguments)})}(e,i)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e):o(this.services.languageDetector.detect()),r}},{key:"getFixedT",value:function(e,t,n){var r=this,a=function e(t,a){var o;if("object"!==Ae(a)){for(var i=arguments.length,s=new Array(i>2?i-2:0),l=2;l<i;l++)s[l-2]=arguments[l];o=r.options.overloadTranslationOptionHandler([t,a].concat(s))}else o=$o({},a);o.lng=o.lng||e.lng,o.lngs=o.lngs||e.lngs,o.ns=o.ns||e.ns,o.keyPrefix=o.keyPrefix||n||e.keyPrefix;var u,c=r.options.keySeparator||".";return u=o.keyPrefix&&Array.isArray(t)?t.map(function(e){return"".concat(o.keyPrefix).concat(c).concat(e)}):o.keyPrefix?"".concat(o.keyPrefix).concat(c).concat(t):t,r.t(u,o)};return"string"===typeof e?a.lng=e:a.lngs=e,a.ns=t,a.keyPrefix=n,a}},{key:"t",value:function(){var e;return this.translator&&(e=this.translator).translate.apply(e,arguments)}},{key:"exists",value:function(){var e;return this.translator&&(e=this.translator).exists.apply(e,arguments)}},{key:"setDefaultNamespace",value:function(e){this.options.defaultNS=e}},{key:"hasLoadedNamespace",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;var r=n.lng||this.resolvedLanguage||this.languages[0],a=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===r.toLowerCase())return!0;var i=function(e,n){var r=t.services.backendConnector.state["".concat(e,"|").concat(n)];return-1===r||2===r};if(n.precheck){var s=n.precheck(this,i);if(void 0!==s)return s}return!!this.hasResourceBundle(r,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!i(r,e)||a&&!i(o,e)))}},{key:"loadNamespaces",value:function(e,t){var n=this,r=ro();return this.options.ns?("string"===typeof e&&(e=[e]),e.forEach(function(e){n.options.ns.indexOf(e)<0&&n.options.ns.push(e)}),this.loadResources(function(e){r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}},{key:"loadLanguages",value:function(e,t){var n=ro();"string"===typeof e&&(e=[e]);var r=this.options.preload||[],a=e.filter(function(e){return r.indexOf(e)<0});return a.length?(this.options.preload=r.concat(a),this.loadResources(function(e){n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}},{key:"dir",value:function(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";var t=this.services&&this.services.languageUtils||new Oo(Wo());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}},{key:"cloneInstance",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Jo,a=$o($o($o({},this.options),t),{isClone:!0}),o=new n(a);void 0===t.debug&&void 0===t.prefix||(o.logger=o.logger.clone(t));return["store","services","language"].forEach(function(t){o[t]=e[t]}),o.services=$o({},this.services),o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o.translator=new No(o.services,o.options),o.translator.on("*",function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];o.emit.apply(o,[e].concat(n))}),o.init(a,r),o.translator.options=o.options,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}},{key:"toJSON",value:function(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}]),n}(no);ze(Yo,"createInstance",function(){return new Yo(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)});var Xo=Yo.createInstance();Xo.createInstance=Yo.createInstance;Xo.createInstance,Xo.dir,Xo.init,Xo.loadResources,Xo.reloadResources,Xo.use,Xo.changeLanguage,Xo.getFixedT,Xo.t,Xo.exists,Xo.setDefaultNamespace,Xo.hasLoadedNamespace,Xo.loadNamespaces,Xo.loadLanguages;var Zo=[],ei=Zo.forEach,ti=Zo.slice;var ni=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,ri=function(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{path:"/",sameSite:"strict"};n&&(a.expires=new Date,a.expires.setTime(a.expires.getTime()+60*n*1e3)),r&&(a.domain=r),document.cookie=function(e,t,n){var r=n||{};r.path=r.path||"/";var a=encodeURIComponent(t),o="".concat(e,"=").concat(a);if(r.maxAge>0){var i=r.maxAge-0;if(Number.isNaN(i))throw new Error("maxAge should be a Number");o+="; Max-Age=".concat(Math.floor(i))}if(r.domain){if(!ni.test(r.domain))throw new TypeError("option domain is invalid");o+="; Domain=".concat(r.domain)}if(r.path){if(!ni.test(r.path))throw new TypeError("option path is invalid");o+="; Path=".concat(r.path)}if(r.expires){if("function"!==typeof r.expires.toUTCString)throw new TypeError("option expires is invalid");o+="; Expires=".concat(r.expires.toUTCString())}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.sameSite)switch("string"===typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"strict":o+="; SameSite=Strict";break;case"none":o+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return o}(e,encodeURIComponent(t),a)},ai=function(e){for(var t="".concat(e,"="),n=document.cookie.split(";"),r=0;r<n.length;r++){for(var a=n[r];" "===a.charAt(0);)a=a.substring(1,a.length);if(0===a.indexOf(t))return a.substring(t.length,a.length)}return null},oi={name:"cookie",lookup:function(e){var t;if(e.lookupCookie&&"undefined"!==typeof document){var n=ai(e.lookupCookie);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupCookie&&"undefined"!==typeof document&&ri(t.lookupCookie,e,t.cookieMinutes,t.cookieDomain,t.cookieOptions)}},ii={name:"querystring",lookup:function(e){var t;if("undefined"!==typeof window){var n=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));for(var r=n.substring(1).split("&"),a=0;a<r.length;a++){var o=r[a].indexOf("=");if(o>0)r[a].substring(0,o)===e.lookupQuerystring&&(t=r[a].substring(o+1))}}return t}},si=null,li=function(){if(null!==si)return si;try{si="undefined"!==window&&null!==window.localStorage;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch(Wi){si=!1}return si},ui={name:"localStorage",lookup:function(e){var t;if(e.lookupLocalStorage&&li()){var n=window.localStorage.getItem(e.lookupLocalStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupLocalStorage&&li()&&window.localStorage.setItem(t.lookupLocalStorage,e)}},ci=null,di=function(){if(null!==ci)return ci;try{ci="undefined"!==window&&null!==window.sessionStorage;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch(Wi){ci=!1}return ci},fi={name:"sessionStorage",lookup:function(e){var t;if(e.lookupSessionStorage&&di()){var n=window.sessionStorage.getItem(e.lookupSessionStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupSessionStorage&&di()&&window.sessionStorage.setItem(t.lookupSessionStorage,e)}},pi={name:"navigator",lookup:function(e){var t=[];if("undefined"!==typeof navigator){if(navigator.languages)for(var n=0;n<navigator.languages.length;n++)t.push(navigator.languages[n]);navigator.userLanguage&&t.push(navigator.userLanguage),navigator.language&&t.push(navigator.language)}return t.length>0?t:void 0}},hi={name:"htmlTag",lookup:function(e){var t,n=e.htmlTag||("undefined"!==typeof document?document.documentElement:null);return n&&"function"===typeof n.getAttribute&&(t=n.getAttribute("lang")),t}},mi={name:"path",lookup:function(e){var t;if("undefined"!==typeof window){var n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(n instanceof Array)if("number"===typeof e.lookupFromPathIndex){if("string"!==typeof n[e.lookupFromPathIndex])return;t=n[e.lookupFromPathIndex].replace("/","")}else t=n[0].replace("/","")}return t}},gi={name:"subdomain",lookup:function(e){var t="number"===typeof e.lookupFromSubdomainIndex?e.lookupFromSubdomainIndex+1:1,n="undefined"!==typeof window&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(n)return n[t]}},vi=!1;try{document.cookie,vi=!0}catch(Wi){}var yi=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];vi||yi.splice(1,1);var bi=function(){return va(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ma(this,e),this.type="languageDetector",this.detectors={},this.init(t,n)},[{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=e||{languageUtils:{}},this.options=function(e){return ei.call(ti.call(arguments,1),function(t){if(t)for(var n in t)void 0===e[n]&&(e[n]=t[n])}),e}(t,this.options||{},{order:yi,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:function(e){return e}}),"string"===typeof this.options.convertDetectedLanguage&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=function(e){return e.replace("-","_")}),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=n,this.addDetector(oi),this.addDetector(ii),this.addDetector(ui),this.addDetector(fi),this.addDetector(pi),this.addDetector(hi),this.addDetector(mi),this.addDetector(gi)}},{key:"addDetector",value:function(e){return this.detectors[e.name]=e,this}},{key:"detect",value:function(e){var t=this;e||(e=this.options.order);var n=[];return e.forEach(function(e){if(t.detectors[e]){var r=t.detectors[e].lookup(t.options);r&&"string"===typeof r&&(r=[r]),r&&(n=n.concat(r))}}),n=n.map(function(e){return t.options.convertDetectedLanguage(e)}),this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}},{key:"cacheUserLanguage",value:function(e,t){var n=this;t||(t=this.options.caches),t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach(function(t){n.detectors[t]&&n.detectors[t].cacheUserLanguage(e,n.options)}))}}])}();function xi(e){return xi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xi(e)}bi.type="languageDetector";function wi(){return"function"===typeof XMLHttpRequest||"object"===("undefined"===typeof XMLHttpRequest?"undefined":xi(XMLHttpRequest))}const ki=n.p+"static/media/getFetch.7a7a1c81840c96d5a3ea.cjs";var Si=n.t(ki);function ji(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ni(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ji(Object(n),!0).forEach(function(t){Ei(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ji(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ei(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=Oi(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Oi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Oi(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Oi(e){return Oi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Oi(e)}var Pi,Ci,_i="function"===typeof fetch?fetch:void 0;"undefined"!==typeof global&&global.fetch?_i=global.fetch:"undefined"!==typeof window&&window.fetch&&(_i=window.fetch),wi()&&("undefined"!==typeof global&&global.XMLHttpRequest?Pi=global.XMLHttpRequest:"undefined"!==typeof window&&window.XMLHttpRequest&&(Pi=window.XMLHttpRequest)),"function"===typeof ActiveXObject&&("undefined"!==typeof global&&global.ActiveXObject?Ci=global.ActiveXObject:"undefined"!==typeof window&&window.ActiveXObject&&(Ci=window.ActiveXObject)),_i||!Si||Pi||Ci||(_i=ki||Si),"function"!==typeof _i&&(_i=void 0);var Ti=function(e,t){if(t&&"object"===Oi(t)){var n="";for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r]);if(!n)return e;e=e+(-1!==e.indexOf("?")?"&":"?")+n.slice(1)}return e},Ri=function(e,t,n,r){var a=function(e){if(!e.ok)return n(e.statusText||"Error",{status:e.status});e.text().then(function(t){n(null,{status:e.status,data:t})}).catch(n)};if(r){var o=r(e,t);if(o instanceof Promise)return void o.then(a).catch(n)}"function"===typeof fetch?fetch(e,t).then(a).catch(n):_i(e,t).then(a).catch(n)},Ai=!1;const Li=function(e,t,n,r){return"function"===typeof n&&(r=n,n=void 0),r=r||function(){},_i&&0!==t.indexOf("file:")?function(e,t,n,r){e.queryStringParams&&(t=Ti(t,e.queryStringParams));var a=Ni({},"function"===typeof e.customHeaders?e.customHeaders():e.customHeaders);"undefined"===typeof window&&"undefined"!==typeof global&&"undefined"!==typeof global.process&&global.process.versions&&global.process.versions.node&&(a["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),n&&(a["Content-Type"]="application/json");var o="function"===typeof e.requestOptions?e.requestOptions(n):e.requestOptions,i=Ni({method:n?"POST":"GET",body:n?e.stringify(n):void 0,headers:a},Ai?{}:o),s="function"===typeof e.alternateFetch&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{Ri(t,i,r,s)}catch(Wi){if(!o||0===Object.keys(o).length||!Wi.message||Wi.message.indexOf("not implemented")<0)return r(Wi);try{Object.keys(o).forEach(function(e){delete i[e]}),Ri(t,i,r,s),Ai=!0}catch(l){r(l)}}}(e,t,n,r):wi()||"function"===typeof ActiveXObject?function(e,t,n,r){n&&"object"===Oi(n)&&(n=Ti("",n).slice(1)),e.queryStringParams&&(t=Ti(t,e.queryStringParams));try{var a;(a=Pi?new Pi:new Ci("MSXML2.XMLHTTP.3.0")).open(n?"POST":"GET",t,1),e.crossDomain||a.setRequestHeader("X-Requested-With","XMLHttpRequest"),a.withCredentials=!!e.withCredentials,n&&a.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),a.overrideMimeType&&a.overrideMimeType("application/json");var o=e.customHeaders;if(o="function"===typeof o?o():o)for(var i in o)a.setRequestHeader(i,o[i]);a.onreadystatechange=function(){a.readyState>3&&r(a.status>=400?a.statusText:null,{status:a.status,data:a.responseText})},a.send(n)}catch(Wi){console&&console.log(Wi)}}(e,t,n,r):void r(new Error("No fetch and no xhr implementation found!"))};function zi(e){return zi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zi(e)}function Di(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Fi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Di(Object(n),!0).forEach(function(t){Ui(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Di(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ii(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Mi(r.key),r)}}function Ui(e,t,n){return(t=Mi(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Mi(e){var t=function(e,t){if("object"!=zi(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=zi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==zi(t)?t:t+""}var Bi=function(e,t,n){return t&&Ii(e.prototype,t),n&&Ii(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.services=t,this.options=n,this.allOptions=r,this.type="backend",this.init(t,n,r)},[{key:"init",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.services=e,this.options=Fi(Fi(Fi({},{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(e){return JSON.parse(e)},stringify:JSON.stringify,parsePayload:function(e,t,n){return Ui({},t,n||"")},parseLoadPayload:function(e,t){},request:Li,reloadInterval:"undefined"===typeof window&&36e5,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}),this.options||{}),n),this.allOptions=r,this.services&&this.options.reloadInterval){var a=setInterval(function(){return t.reload()},this.options.reloadInterval);"object"===zi(a)&&"function"===typeof a.unref&&a.unref()}}},{key:"readMulti",value:function(e,t,n){this._readAny(e,e,t,t,n)}},{key:"read",value:function(e,t,n){this._readAny([e],e,[t],t,n)}},{key:"_readAny",value:function(e,t,n,r,a){var o,i=this,s=this.options.loadPath;"function"===typeof this.options.loadPath&&(s=this.options.loadPath(e,n)),(s=function(e){return!!e&&"function"===typeof e.then}(o=s)?o:Promise.resolve(o)).then(function(o){if(!o)return a(null,{});var s=i.services.interpolator.interpolate(o,{lng:e.join("+"),ns:n.join("+")});i.loadUrl(s,a,t,r)})}},{key:"loadUrl",value:function(e,t,n,r){var a=this,o="string"===typeof n?[n]:n,i="string"===typeof r?[r]:r,s=this.options.parseLoadPayload(o,i);this.options.request(this.options,e,s,function(o,i){if(i&&(i.status>=500&&i.status<600||!i.status))return t("failed loading "+e+"; status code: "+i.status,!0);if(i&&i.status>=400&&i.status<500)return t("failed loading "+e+"; status code: "+i.status,!1);if(!i&&o&&o.message){var s=o.message.toLowerCase();if(["failed","fetch","network","load"].find(function(e){return s.indexOf(e)>-1}))return t("failed loading "+e+": "+o.message,!0)}if(o)return t(o,!1);var l,u;try{l="string"===typeof i.data?a.options.parse(i.data,n,r):i.data}catch(Wi){u="failed parsing "+e+" to json"}if(u)return t(u,!1);t(null,l)})}},{key:"create",value:function(e,t,n,r,a){var o=this;if(this.options.addPath){"string"===typeof e&&(e=[e]);var i=this.options.parsePayload(t,n,r),s=0,l=[],u=[];e.forEach(function(n){var r=o.options.addPath;"function"===typeof o.options.addPath&&(r=o.options.addPath(n,t));var c=o.services.interpolator.interpolate(r,{lng:n,ns:t});o.options.request(o.options,c,i,function(t,n){s+=1,l.push(t),u.push(n),s===e.length&&"function"===typeof a&&a(l,u)})})}}},{key:"reload",value:function(){var e=this,t=this.services,n=t.backendConnector,r=t.languageUtils,a=t.logger,o=n.language;if(!o||"cimode"!==o.toLowerCase()){var i=[],s=function(e){r.toResolveHierarchy(e).forEach(function(e){i.indexOf(e)<0&&i.push(e)})};s(o),this.allOptions.preload&&this.allOptions.preload.forEach(function(e){return s(e)}),i.forEach(function(t){e.allOptions.ns.forEach(function(e){n.read(t,e,"read",null,null,function(r,o){r&&a.warn("loading namespace ".concat(e," for language ").concat(t," failed"),r),!r&&o&&a.log("loaded namespace ".concat(e," for language ").concat(t),o),n.loaded("".concat(t,"|").concat(e),r,o)})})})}}}]);Bi.type="backend";const qi=Bi;Xo.use(qi).use(bi).use(ya).init({resources:{en:{translation:{common:{loading:"Loading...",error:"Error",success:"Success",cancel:"Cancel",save:"Save",delete:"Delete",edit:"Edit",view:"View",search:"Search",filter:"Filter",sort:"Sort",next:"Next",previous:"Previous",submit:"Submit",close:"Close",confirm:"Confirm",yes:"Yes",no:"No"},navigation:{dashboard:"Dashboard",quizzes:"Quizzes",progress:"Progress",social:"Social",content:"Content",settings:"Settings",profile:"Profile",logout:"Sign Out"},auth:{login:"Sign In",register:"Sign Up",logout:"Sign Out",forgotPassword:"Forgot Password?",resetPassword:"Reset Password",email:"Email",username:"Username",password:"Password",confirmPassword:"Confirm Password",name:"Full Name",phone:"Phone Number",welcomeBack:"Welcome Back",createAccount:"Create Account",alreadyHaveAccount:"Already have an account?",dontHaveAccount:"Don't have an account?",signInHere:"Sign in here",signUpHere:"Sign up here",termsAndConditions:"Terms and Conditions",privacyPolicy:"Privacy Policy",agreeToTerms:"I agree to the Terms and Conditions and Privacy Policy",loginSuccess:"Login successful!",registerSuccess:"Registration successful!",loginFailed:"Login failed. Please try again.",registerFailed:"Registration failed. Please try again."},dashboard:{title:"Dashboard",welcomeMessage:"Welcome back{{name}}!",overview:"Overview",recentActivity:"Recent Activity",quickActions:"Quick Actions",statistics:"Statistics",totalQuizzes:"Total Quizzes",completedQuizzes:"Completed",averageScore:"Average Score",studyStreak:"Study Streak",weeklyProgress:"Weekly Progress",monthlyProgress:"Monthly Progress",takeQuiz:"Take Quiz",viewProgress:"View Progress",browseContent:"Browse Content",noRecentActivity:"No recent activity",startLearning:"Start learning to see your activity here"},quiz:{title:"Quiz",quizzes:"Quizzes",takeQuiz:"Take Quiz",createQuiz:"Create Quiz",myQuizzes:"My Quizzes",popularQuizzes:"Popular Quizzes",featuredQuizzes:"Featured Quizzes",difficulty:"Difficulty",easy:"Easy",medium:"Medium",hard:"Hard",questions:"Questions",timeLimit:"Time Limit",minutes:"minutes",startQuiz:"Start Quiz",submitAnswer:"Submit Answer",nextQuestion:"Next Question",previousQuestion:"Previous Question",finishQuiz:"Finish Quiz",quizCompleted:"Quiz Completed!",yourScore:"Your Score",correctAnswers:"Correct Answers",timeTaken:"Time Taken",viewResults:"View Results",retakeQuiz:"Retake Quiz",shareQuiz:"Share Quiz",noQuizzes:"No quizzes available",searchQuizzes:"Search quizzes..."},progress:{title:"Progress",overview:"Progress Overview",studyStats:"Study Statistics",achievements:"Achievements",goals:"Goals",streak:"Study Streak",totalStudyTime:"Total Study Time",quizzesCompleted:"Quizzes Completed",averageScore:"Average Score",weakAreas:"Areas to Improve",strongAreas:"Strong Areas",recentProgress:"Recent Progress",setGoal:"Set Goal",dailyGoal:"Daily Goal",weeklyGoal:"Weekly Goal",monthlyGoal:"Monthly Goal",goalAchieved:"Goal Achieved!",keepItUp:"Keep up the great work!"},social:{title:"Social",friends:"Friends",leaderboard:"Leaderboard",findFriends:"Find Friends",friendRequests:"Friend Requests",addFriend:"Add Friend",removeFriend:"Remove Friend",acceptRequest:"Accept",declineRequest:"Decline",sendRequest:"Send Request",rank:"Rank",score:"Score",globalRanking:"Global Ranking",friendsRanking:"Friends Ranking",weeklyLeaderboard:"Weekly Leaderboard",monthlyLeaderboard:"Monthly Leaderboard",allTimeLeaderboard:"All Time Leaderboard",noFriends:"No friends yet",startConnecting:"Start connecting with other learners"},content:{title:"Content",studyMaterials:"Study Materials",myLibrary:"My Library",recommended:"Recommended",subjects:"Subjects",addToLibrary:"Add to Library",removeFromLibrary:"Remove from Library",downloadContent:"Download",shareContent:"Share",contentType:"Type",document:"Document",video:"Video",audio:"Audio",interactive:"Interactive",noContent:"No content available",browseContent:"Browse available content"},settings:{title:"Settings",profile:"Profile Settings",account:"Account Settings",notifications:"Notification Settings",privacy:"Privacy Settings",language:"Language",theme:"Theme",changePassword:"Change Password",deleteAccount:"Delete Account",saveChanges:"Save Changes",profileUpdated:"Profile updated successfully",passwordChanged:"Password changed successfully",settingsSaved:"Settings saved successfully"},errors:{generic:"Something went wrong. Please try again.",network:"Network error. Please check your connection.",unauthorized:"You are not authorized to perform this action.",notFound:"The requested resource was not found.",validation:"Please check your input and try again.",serverError:"Server error. Please try again later."}}},tr:{translation:{common:{loading:"Y\xfckleniyor...",error:"Hata",success:"Ba\u015far\u0131l\u0131",cancel:"\u0130ptal",save:"Kaydet",delete:"Sil",edit:"D\xfczenle",view:"G\xf6r\xfcnt\xfcle",search:"Ara",filter:"Filtrele",sort:"S\u0131rala",next:"Sonraki",previous:"\xd6nceki",submit:"G\xf6nder",close:"Kapat",confirm:"Onayla",yes:"Evet",no:"Hay\u0131r"},navigation:{dashboard:"Ana Sayfa",quizzes:"Testler",progress:"\u0130lerleme",social:"Sosyal",content:"\u0130\xe7erik",settings:"Ayarlar",profile:"Profil",logout:"\xc7\u0131k\u0131\u015f Yap"},auth:{login:"Giri\u015f Yap",register:"Kay\u0131t Ol",logout:"\xc7\u0131k\u0131\u015f Yap",forgotPassword:"\u015eifremi Unuttum?",resetPassword:"\u015eifre S\u0131f\u0131rla",email:"E-posta",username:"Kullan\u0131c\u0131 Ad\u0131",password:"\u015eifre",confirmPassword:"\u015eifre Tekrar",name:"Ad Soyad",phone:"Telefon Numaras\u0131",welcomeBack:"Tekrar Ho\u015f Geldiniz",createAccount:"Hesap Olu\u015ftur",alreadyHaveAccount:"Zaten hesab\u0131n\u0131z var m\u0131?",dontHaveAccount:"Hesab\u0131n\u0131z yok mu?",signInHere:"Buradan giri\u015f yap\u0131n",signUpHere:"Buradan kay\u0131t olun",termsAndConditions:"Kullan\u0131m \u015eartlar\u0131",privacyPolicy:"Gizlilik Politikas\u0131",agreeToTerms:"Kullan\u0131m \u015eartlar\u0131 ve Gizlilik Politikas\u0131n\u0131 kabul ediyorum",loginSuccess:"Giri\u015f ba\u015far\u0131l\u0131!",registerSuccess:"Kay\u0131t ba\u015far\u0131l\u0131!",loginFailed:"Giri\u015f ba\u015far\u0131s\u0131z. L\xfctfen tekrar deneyin.",registerFailed:"Kay\u0131t ba\u015far\u0131s\u0131z. L\xfctfen tekrar deneyin."},dashboard:{title:"Ana Sayfa",welcomeMessage:"Tekrar ho\u015f geldiniz{{name}}!",overview:"Genel Bak\u0131\u015f",recentActivity:"Son Aktiviteler",quickActions:"H\u0131zl\u0131 \u0130\u015flemler",statistics:"\u0130statistikler",totalQuizzes:"Toplam Test",completedQuizzes:"Tamamlanan",averageScore:"Ortalama Puan",studyStreak:"\xc7al\u0131\u015fma Serisi",weeklyProgress:"Haftal\u0131k \u0130lerleme",monthlyProgress:"Ayl\u0131k \u0130lerleme",takeQuiz:"Test \xc7\xf6z",viewProgress:"\u0130lerlemeyi G\xf6r",browseContent:"\u0130\xe7erikleri G\xf6zat",noRecentActivity:"Son aktivite yok",startLearning:"Aktivitelerinizi g\xf6rmek i\xe7in \xf6\u011frenmeye ba\u015flay\u0131n"},quiz:{title:"Test",quizzes:"Testler",takeQuiz:"Test \xc7\xf6z",createQuiz:"Test Olu\u015ftur",myQuizzes:"Testlerim",popularQuizzes:"Pop\xfcler Testler",featuredQuizzes:"\xd6ne \xc7\u0131kan Testler",difficulty:"Zorluk",easy:"Kolay",medium:"Orta",hard:"Zor",questions:"Sorular",timeLimit:"S\xfcre S\u0131n\u0131r\u0131",minutes:"dakika",startQuiz:"Teste Ba\u015fla",submitAnswer:"Cevab\u0131 G\xf6nder",nextQuestion:"Sonraki Soru",previousQuestion:"\xd6nceki Soru",finishQuiz:"Testi Bitir",quizCompleted:"Test Tamamland\u0131!",yourScore:"Puan\u0131n\u0131z",correctAnswers:"Do\u011fru Cevaplar",timeTaken:"Ge\xe7en S\xfcre",viewResults:"Sonu\xe7lar\u0131 G\xf6r",retakeQuiz:"Tekrar \xc7\xf6z",shareQuiz:"Testi Payla\u015f",noQuizzes:"Mevcut test yok",searchQuizzes:"Test ara..."},progress:{title:"\u0130lerleme",overview:"\u0130lerleme \xd6zeti",studyStats:"\xc7al\u0131\u015fma \u0130statistikleri",achievements:"Ba\u015far\u0131lar",goals:"Hedefler",streak:"\xc7al\u0131\u015fma Serisi",totalStudyTime:"Toplam \xc7al\u0131\u015fma S\xfcresi",quizzesCompleted:"Tamamlanan Testler",averageScore:"Ortalama Puan",weakAreas:"Geli\u015ftirilmesi Gereken Alanlar",strongAreas:"G\xfc\xe7l\xfc Alanlar",recentProgress:"Son \u0130lerleme",setGoal:"Hedef Belirle",dailyGoal:"G\xfcnl\xfck Hedef",weeklyGoal:"Haftal\u0131k Hedef",monthlyGoal:"Ayl\u0131k Hedef",goalAchieved:"Hedef Ba\u015far\u0131ld\u0131!",keepItUp:"B\xf6yle devam edin!"},social:{title:"Sosyal",friends:"Arkada\u015flar",leaderboard:"Lider Tablosu",findFriends:"Arkada\u015f Bul",friendRequests:"Arkada\u015fl\u0131k \u0130stekleri",addFriend:"Arkada\u015f Ekle",removeFriend:"Arkada\u015f\u0131 Kald\u0131r",acceptRequest:"Kabul Et",declineRequest:"Reddet",sendRequest:"\u0130stek G\xf6nder",rank:"S\u0131ralama",score:"Puan",globalRanking:"Genel S\u0131ralama",friendsRanking:"Arkada\u015f S\u0131ralamas\u0131",weeklyLeaderboard:"Haftal\u0131k Lider Tablosu",monthlyLeaderboard:"Ayl\u0131k Lider Tablosu",allTimeLeaderboard:"T\xfcm Zamanlar Lider Tablosu",noFriends:"Hen\xfcz arkada\u015f\u0131n\u0131z yok",startConnecting:"Di\u011fer \xf6\u011frencilerle ba\u011flant\u0131 kurmaya ba\u015flay\u0131n"},content:{title:"\u0130\xe7erik",studyMaterials:"\xc7al\u0131\u015fma Materyalleri",myLibrary:"K\xfct\xfcphanem",recommended:"\xd6nerilen",subjects:"Konular",addToLibrary:"K\xfct\xfcphaneye Ekle",removeFromLibrary:"K\xfct\xfcphaneden Kald\u0131r",downloadContent:"\u0130ndir",shareContent:"Payla\u015f",contentType:"T\xfcr",document:"Dok\xfcman",video:"Video",audio:"Ses",interactive:"Etkile\u015fimli",noContent:"Mevcut i\xe7erik yok",browseContent:"Mevcut i\xe7erikleri g\xf6zat"},settings:{title:"Ayarlar",profile:"Profil Ayarlar\u0131",account:"Hesap Ayarlar\u0131",notifications:"Bildirim Ayarlar\u0131",privacy:"Gizlilik Ayarlar\u0131",language:"Dil",theme:"Tema",changePassword:"\u015eifre De\u011fi\u015ftir",deleteAccount:"Hesab\u0131 Sil",saveChanges:"De\u011fi\u015fiklikleri Kaydet",profileUpdated:"Profil ba\u015far\u0131yla g\xfcncellendi",passwordChanged:"\u015eifre ba\u015far\u0131yla de\u011fi\u015ftirildi",settingsSaved:"Ayarlar ba\u015far\u0131yla kaydedildi"},errors:{generic:"Bir \u015feyler ters gitti. L\xfctfen tekrar deneyin.",network:"A\u011f hatas\u0131. L\xfctfen ba\u011flant\u0131n\u0131z\u0131 kontrol edin.",unauthorized:"Bu i\u015flemi ger\xe7ekle\u015ftirme yetkiniz yok.",notFound:"\u0130stenen kaynak bulunamad\u0131.",validation:"L\xfctfen giri\u015finizi kontrol edin ve tekrar deneyin.",serverError:"Sunucu hatas\u0131. L\xfctfen daha sonra tekrar deneyin."}}}},fallbackLng:"en",debug:!1,interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]},backend:{loadPath:"/locales/{{lng}}/{{ns}}.json"}});const Hi=function(){return(0,gr.jsx)(xr,{children:(0,gr.jsx)(Oe,{children:(0,gr.jsx)("div",{className:"min-h-screen bg-dark-900",children:(0,gr.jsxs)(we,{children:[(0,gr.jsx)(be,{path:"/login",element:(0,gr.jsx)(kr,{requireAuth:!1,children:(0,gr.jsx)(Ir,{})})}),(0,gr.jsx)(be,{path:"/register",element:(0,gr.jsx)(kr,{requireAuth:!1,children:(0,gr.jsx)(Br,{})})}),(0,gr.jsxs)(be,{path:"/",element:(0,gr.jsx)(kr,{children:(0,gr.jsx)(ta,{})}),children:[(0,gr.jsx)(be,{index:!0,element:(0,gr.jsx)(ve,{to:"/dashboard",replace:!0})}),(0,gr.jsx)(be,{path:"dashboard",element:(0,gr.jsx)(Da,{})}),(0,gr.jsx)(be,{path:"quizzes",element:(0,gr.jsx)("div",{className:"text-white",children:"Quizzes Page - Coming Soon"})}),(0,gr.jsx)(be,{path:"progress",element:(0,gr.jsx)("div",{className:"text-white",children:"Progress Page - Coming Soon"})}),(0,gr.jsx)(be,{path:"social",element:(0,gr.jsx)("div",{className:"text-white",children:"Social Page - Coming Soon"})}),(0,gr.jsx)(be,{path:"content",element:(0,gr.jsx)("div",{className:"text-white",children:"Content Page - Coming Soon"})}),(0,gr.jsx)(be,{path:"settings",element:(0,gr.jsx)("div",{className:"text-white",children:"Settings Page - Coming Soon"})}),(0,gr.jsx)(be,{path:"profile",element:(0,gr.jsx)("div",{className:"text-white",children:"Profile Page - Coming Soon"})})]}),(0,gr.jsx)(be,{path:"/admin/*",element:(0,gr.jsx)(Wa,{})}),(0,gr.jsx)(be,{path:"*",element:(0,gr.jsx)(ve,{to:"/dashboard",replace:!0})})]})})})})},Vi=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then(t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:o,getTTFB:i}=t;n(e),r(e),a(e),o(e),i(e)})};o.createRoot(document.getElementById("root")).render((0,gr.jsx)(r.StrictMode,{children:(0,gr.jsx)(Hi,{})})),Vi()})()})();
//# sourceMappingURL=main.6fe558c5.js.map