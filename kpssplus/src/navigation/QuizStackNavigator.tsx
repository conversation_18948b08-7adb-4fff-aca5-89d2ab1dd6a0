import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {QuizStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {QuizHomeScreen} from '../screens/quiz/QuizHomeScreen';
import {QuizDetailScreen} from '../screens/quiz/QuizDetailScreen';
import {QuizPlayScreen} from '../screens/quiz/QuizPlayScreen';
import {QuizResultScreen} from '../screens/quiz/QuizResultScreen';
import {QuizSearchScreen} from '../screens/quiz/QuizSearchScreen';
import {QuizLeaderboardScreen} from '../screens/quiz/QuizLeaderboardScreen';

const Stack = createStackNavigator<QuizStackParamList>();

export const QuizStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.white,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackTitle: '',
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="QuizHome"
        component={QuizHomeScreen}
        options={{
          title: 'Quiz',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="QuizDetail"
        component={QuizDetailScreen}
        options={{
          title: 'Quiz Detayı',
        }}
      />
      <Stack.Screen
        name="QuizPlay"
        component={QuizPlayScreen}
        options={{
          title: 'Quiz Çöz',
          headerShown: false, // Full screen quiz
        }}
      />
      <Stack.Screen
        name="QuizResult"
        component={QuizResultScreen}
        options={{
          title: 'Quiz Sonucu',
        }}
      />
      <Stack.Screen
        name="QuizSearch"
        component={QuizSearchScreen}
        options={{
          title: 'Quiz Ara',
        }}
      />
      <Stack.Screen
        name="QuizLeaderboard"
        component={QuizLeaderboardScreen}
        options={{
          title: 'Liderlik Tablosu',
        }}
      />
    </Stack.Navigator>
  );
};
