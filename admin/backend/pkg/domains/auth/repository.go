package auth

import (
    "github.com/nocytech/kpss-plus-admin/pkg/database"
    "github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Repository interface {
    GetByUsername(username string) (entities.AdminUser, error)
    ListAdmins() ([]entities.AdminUser, error)
    CreateAdmin(u *entities.AdminUser) error
    UpdateAdmin(id string, updates map[string]any) error
    DeleteAdmin(id string) error
}

type repository struct{}

func NewRepository() Repository { return &repository{} }

func (r *repository) GetByUsername(username string) (entities.AdminUser, error) {
    var u entities.AdminUser
    err := database.DB().Where("username = ?", username).First(&u).Error
    return u, err
}

func (r *repository) ListAdmins() ([]entities.AdminUser, error) {
    var list []entities.AdminUser
    err := database.DB().Order("username ASC").Find(&list).Error
    return list, err
}

func (r *repository) CreateAdmin(u *entities.AdminUser) error {
    return database.DB().Create(u).Error
}

func (r *repository) UpdateAdmin(id string, updates map[string]any) error {
    return database.DB().Model(&entities.AdminUser{}).Where("id = ?", id).Updates(updates).Error
}

func (r *repository) DeleteAdmin(id string) error {
    return database.DB().Delete(&entities.AdminUser{}, "id = ?", id).Error
}

