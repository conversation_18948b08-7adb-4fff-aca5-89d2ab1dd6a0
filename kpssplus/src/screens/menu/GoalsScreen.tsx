import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Card, Button, Input} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';

interface Goal {
  id: string;
  title: string;
  description: string;
  target: number;
  current: number;
  unit: string;
  deadline: string;
  category: 'daily' | 'weekly' | 'monthly';
  completed: boolean;
}

interface GoalCardProps {
  goal: Goal;
  onEdit: () => void;
  onDelete: () => void;
  onToggleComplete: () => void;
}

const GoalCard: React.FC<GoalCardProps> = ({
  goal,
  onEdit,
  onDelete,
  onToggleComplete,
}) => {
  const progress = Math.min(100, (goal.current / goal.target) * 100);
  const isOverdue = new Date(goal.deadline) < new Date() && !goal.completed;

  return (
    <Card
      style={
        goal.completed
          ? [styles.goalCard, styles.completedGoal]
          : styles.goalCard
      }>
      <View style={styles.goalHeader}>
        <View style={styles.goalInfo}>
          <Text style={styles.goalTitle}>{goal.title}</Text>
          <Text style={styles.goalDescription}>{goal.description}</Text>
        </View>
        <View style={styles.goalActions}>
          <TouchableOpacity onPress={onEdit} style={styles.actionButton}>
            <Icon name="pencil-outline" size={16} color={colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity onPress={onDelete} style={styles.actionButton}>
            <Icon name="trash-outline" size={16} color={colors.error} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.goalProgress}>
        <View style={styles.progressInfo}>
          <Text style={styles.progressText}>
            {goal.current}/{goal.target} {goal.unit}
          </Text>
          <Text style={styles.progressPercentage}>{Math.round(progress)}%</Text>
        </View>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${progress}%`,
                backgroundColor: goal.completed
                  ? colors.success
                  : isOverdue
                  ? colors.error
                  : colors.primary,
              },
            ]}
          />
        </View>
      </View>

      <View style={styles.goalFooter}>
        <View style={styles.goalMeta}>
          <Icon
            name={
              goal.category === 'daily'
                ? 'today-outline'
                : goal.category === 'weekly'
                ? 'calendar-outline'
                : 'calendar-number-outline'
            }
            size={16}
            color={colors.text.secondary}
          />
          <Text style={styles.goalDeadline}>
            {isOverdue ? 'Süresi geçti' : `Bitiş: ${goal.deadline}`}
          </Text>
        </View>
        <TouchableOpacity
          onPress={onToggleComplete}
          style={[
            styles.completeButton,
            goal.completed && styles.completeButtonActive,
          ]}>
          <Icon
            name={
              goal.completed ? 'checkmark-circle' : 'checkmark-circle-outline'
            }
            size={20}
            color={goal.completed ? colors.success : colors.text.secondary}
          />
        </TouchableOpacity>
      </View>
    </Card>
  );
};

export const GoalsScreen: React.FC = () => {
  const {isAuthenticated} = useAuth();
  const [goals, setGoals] = useState<Goal[]>([
    {
      id: '1',
      title: 'Günlük Çalışma',
      description: 'Her gün en az 2 saat çalış',
      target: 14,
      current: 10,
      unit: 'saat',
      deadline: '2024-01-31',
      category: 'weekly',
      completed: false,
    },
    {
      id: '2',
      title: 'Quiz Çözme',
      description: 'Bu hafta 10 quiz çöz',
      target: 10,
      current: 7,
      unit: 'quiz',
      deadline: '2024-01-28',
      category: 'weekly',
      completed: false,
    },
    {
      id: '3',
      title: 'Ders Tamamlama',
      description: 'Matematik derslerini bitir',
      target: 20,
      current: 20,
      unit: 'ders',
      deadline: '2024-01-25',
      category: 'monthly',
      completed: true,
    },
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [newGoal, setNewGoal] = useState({
    title: '',
    description: '',
    target: '',
    unit: '',
    deadline: '',
    category: 'weekly' as Goal['category'],
  });

  const handleAddGoal = () => {
    if (!newGoal.title.trim() || !newGoal.target) {
      Alert.alert('Hata', 'Lütfen tüm alanları doldurun');
      return;
    }

    const goal: Goal = {
      id: Date.now().toString(),
      title: newGoal.title,
      description: newGoal.description,
      target: parseInt(newGoal.target, 10),
      current: 0,
      unit: newGoal.unit,
      deadline: newGoal.deadline,
      category: newGoal.category,
      completed: false,
    };

    setGoals(prev => [...prev, goal]);
    setNewGoal({
      title: '',
      description: '',
      target: '',
      unit: '',
      deadline: '',
      category: 'weekly',
    });
    setShowAddForm(false);
  };

  const handleDeleteGoal = (goalId: string) => {
    Alert.alert('Hedefi Sil', 'Bu hedefi silmek istediğinizden emin misiniz?', [
      {text: 'İptal', style: 'cancel'},
      {
        text: 'Sil',
        style: 'destructive',
        onPress: () => setGoals(prev => prev.filter(g => g.id !== goalId)),
      },
    ]);
  };

  const handleToggleComplete = (goalId: string) => {
    setGoals(prev =>
      prev.map(goal =>
        goal.id === goalId ? {...goal, completed: !goal.completed} : goal,
      ),
    );
  };

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <View style={styles.emptyState}>
          <Icon name="flag-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.emptyTitle}>Giriş Yapın</Text>
          <Text style={styles.emptyDescription}>
            Hedeflerinizi görmek ve yönetmek için giriş yapmanız gerekiyor
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const activeGoals = goals.filter(g => !g.completed);
  const completedGoals = goals.filter(g => g.completed);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {/* Stats */}
        <View style={styles.statsContainer}>
          <Card style={styles.statCard}>
            <Text style={styles.statNumber}>{activeGoals.length}</Text>
            <Text style={styles.statLabel}>Aktif Hedef</Text>
          </Card>
          <Card style={styles.statCard}>
            <Text style={styles.statNumber}>{completedGoals.length}</Text>
            <Text style={styles.statLabel}>Tamamlanan</Text>
          </Card>
          <Card style={styles.statCard}>
            <Text style={styles.statNumber}>
              {Math.round(
                goals.reduce(
                  (acc, goal) => acc + (goal.current / goal.target) * 100,
                  0,
                ) / goals.length || 0,
              )}
              %
            </Text>
            <Text style={styles.statLabel}>Ortalama</Text>
          </Card>
        </View>

        {/* Add Goal Button */}
        <Button
          title="Yeni Hedef Ekle"
          onPress={() => setShowAddForm(!showAddForm)}
          variant="outline"
          style={styles.addButton}
        />

        {/* Add Goal Form */}
        {showAddForm && (
          <Card style={styles.addForm}>
            <Text style={styles.formTitle}>Yeni Hedef</Text>
            <Input
              label="Hedef Başlığı"
              value={newGoal.title}
              onChangeText={value =>
                setNewGoal(prev => ({...prev, title: value}))
              }
              placeholder="Örn: Günlük çalışma"
            />
            <Input
              label="Açıklama"
              value={newGoal.description}
              onChangeText={value =>
                setNewGoal(prev => ({...prev, description: value}))
              }
              placeholder="Hedef açıklaması"
            />
            <View style={styles.formRow}>
              <Input
                label="Hedef Sayı"
                value={newGoal.target}
                onChangeText={value =>
                  setNewGoal(prev => ({...prev, target: value}))
                }
                placeholder="10"
                keyboardType="numeric"
                containerStyle={styles.halfInput}
              />
              <Input
                label="Birim"
                value={newGoal.unit}
                onChangeText={value =>
                  setNewGoal(prev => ({...prev, unit: value}))
                }
                placeholder="saat, ders, quiz"
                containerStyle={styles.halfInput}
              />
            </View>
            <Input
              label="Bitiş Tarihi"
              value={newGoal.deadline}
              onChangeText={value =>
                setNewGoal(prev => ({...prev, deadline: value}))
              }
              placeholder="2024-01-31"
            />
            <View style={styles.formActions}>
              <Button
                title="İptal"
                onPress={() => setShowAddForm(false)}
                variant="ghost"
                style={styles.formButton}
              />
              <Button
                title="Ekle"
                onPress={handleAddGoal}
                style={styles.formButton}
              />
            </View>
          </Card>
        )}

        {/* Active Goals */}
        {activeGoals.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Aktif Hedefler</Text>
            {activeGoals.map(goal => (
              <GoalCard
                key={goal.id}
                goal={goal}
                onEdit={() => {}}
                onDelete={() => handleDeleteGoal(goal.id)}
                onToggleComplete={() => handleToggleComplete(goal.id)}
              />
            ))}
          </View>
        )}

        {/* Completed Goals */}
        {completedGoals.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Tamamlanan Hedefler</Text>
            {completedGoals.map(goal => (
              <GoalCard
                key={goal.id}
                goal={goal}
                onEdit={() => {}}
                onDelete={() => handleDeleteGoal(goal.id)}
                onToggleComplete={() => handleToggleComplete(goal.id)}
              />
            ))}
          </View>
        )}

        {goals.length === 0 && (
          <View style={styles.emptyGoals}>
            <Icon name="flag-outline" size={60} color={colors.gray[400]} />
            <Text style={styles.emptyGoalsTitle}>Henüz hedef yok</Text>
            <Text style={styles.emptyGoalsDescription}>
              İlk hedefinizi ekleyerek başlayın
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
  },
  emptyTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing[6],
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: spacing[4],
    marginHorizontal: spacing[1],
  },
  statNumber: {
    ...typography.h3,
    color: colors.primary,
    marginBottom: spacing[1],
  },
  statLabel: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  addButton: {
    marginBottom: spacing[4],
  },
  addForm: {
    marginBottom: spacing[6],
    padding: spacing[4],
  },
  formTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    flex: 1,
    marginHorizontal: spacing[1],
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing[4],
  },
  formButton: {
    flex: 1,
    marginHorizontal: spacing[1],
  },
  section: {
    marginBottom: spacing[6],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  goalCard: {
    marginBottom: spacing[3],
    padding: spacing[4],
  },
  completedGoal: {
    opacity: 0.7,
    backgroundColor: colors.gray[50],
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  goalInfo: {
    flex: 1,
  },
  goalTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  goalDescription: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  goalActions: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  actionButton: {
    padding: spacing[1],
  },
  goalProgress: {
    marginBottom: spacing[3],
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  progressText: {
    ...typography.body2,
    fontWeight: '600',
  },
  progressPercentage: {
    ...typography.body2,
    color: colors.primary,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  goalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalDeadline: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  completeButton: {
    padding: spacing[1],
  },
  completeButtonActive: {
    backgroundColor: colors.successMuted,
    borderRadius: 4,
  },
  emptyGoals: {
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyGoalsTitle: {
    ...typography.h5,
    marginTop: spacing[3],
    marginBottom: spacing[2],
  },
  emptyGoalsDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});
