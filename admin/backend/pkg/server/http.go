package server

import (
	"encoding/csv"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	routes "github.com/nocytech/kpss-plus-admin/app/api/routes"
	"github.com/nocytech/kpss-plus-admin/pkg/config"
	"github.com/nocytech/kpss-plus-admin/pkg/database"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/area"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/auth"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/content"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/question"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/subject"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/topic"
	admuser "github.com/nocytech/kpss-plus-admin/pkg/domains/user"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"github.com/nocytech/kpss-plus-admin/pkg/utils"
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	gin.SetMode(gin.ReleaseMode)
	app := gin.New()
	app.Use(gin.Logger())
	app.Use(gin.Recovery())

	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(ginprom.Engine(app), ginprom.Subsystem("gin"), ginprom.Path("/metrics"))
	app.Use(p.Instrument())

	api := app.Group("/api/v1")

	// public endpoints
	api.GET("/healthz", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// serve static openapi json
	api.GET("/openapi.json", func(c *gin.Context) {
		c.File("./docs/openapi.json")
	})

	// lightweight Swagger UI via CDN
	api.GET("/docs", func(c *gin.Context) {
		c.Data(200, "text/html; charset=utf-8", []byte(`<!doctype html>
<html>
  <head>
    <title>KPSS Plus Admin API Docs</title>
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@5.17.14/swagger-ui.css">
  </head>
  <body>
    <div id="swagger"></div>
    <script src="https://unpkg.com/swagger-ui-dist@5.17.14/swagger-ui-bundle.js"></script>
    <script>
      window.ui = SwaggerUIBundle({ url: '/api/openapi.json', dom_id: '#swagger' });
    </script>
  </body>
</html>`))
	})

	// Wire new route structure using domain services
	// Build reusable auth groups using closures below

	// auth middleware
	authz := func(c *gin.Context) {
		bearer := c.GetHeader("Authorization")
		if len(bearer) < 8 || bearer[:7] != "Bearer " {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "missing bearer"})
			return
		}
		token := bearer[7:]
		if claims, ok := utils.ParseAndValidate(appc.JwtSecret, token); !ok {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
			return
		} else {
			// attach role to context
			c.Set("admin_role", claims.Role)
			c.Set("admin_username", claims.Username)
			c.Set("admin_id", claims.AdminID)
		}
		c.Next()
	}

	requireRole := func(required string) gin.HandlerFunc {
		return func(c *gin.Context) {
			role := c.GetString("admin_role")
			if !hasRole(role, required) {
				c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "forbidden"})
				return
			}
			c.Next()
		}
	}

	// protected endpoints moved into routes below where applicable

	// Build groups for new route registrars
	public := api.Group("")
	authGroup := api.Group("", authz)
	adminOnly := api.Group("", authz, requireRole("admin"))

	// Initialize domain services
	authRepo := auth.NewRepository()
	authSvc := auth.NewService(authRepo, appc)
	userRepo := admuser.NewRepository()
	userSvc := admuser.NewService(userRepo)

	// Initialize new taxonomy services
	areaRepo := area.NewRepository(database.DB())
	areaSvc := area.NewService(areaRepo)
	subjectRepo := subject.NewRepository(database.DB())
	subjectSvc := subject.NewService(subjectRepo)
	topicRepo := topic.NewRepository(database.DB())
	topicSvc := topic.NewService(topicRepo)
	contentRepo := content.NewRepository(database.DB())
	contentSvc := content.NewService(contentRepo)
	questionRepo := question.NewRepository(database.DB())
	questionSvc := question.NewService(questionRepo)

	// Register domain routes
	routes.RegisterAuthRoutes(public, adminOnly, authSvc)
	routes.RegisterUserRoutes(authGroup, userSvc)

	// Register new taxonomy routes
	setupAreaRoutes(authGroup, areaSvc)
	setupSubjectRoutes(authGroup, subjectSvc)
	setupTopicRoutes(authGroup, topicSvc)

	// Register content and question routes using the new route files
	routes.ContentRoutes(authGroup, contentSvc)
	routes.QuestionRoutes(authGroup, questionSvc)

	// Register analytics and quiz routes
	routes.AnalyticsRoutes(authGroup)
	routes.QuizRoutes(authGroup)

	// Extra endpoints for export/import and rendered previews remain below
	// Export/Import minimal JSON for questions
	api.GET("/export/questions", authz, func(c *gin.Context) {
		var list []entities.Question
		dbq := database.DB().Model(&entities.Question{})
		if quiz := c.Query("quiz_id"); quiz != "" {
			if uid, err := uuid.Parse(quiz); err == nil {
				dbq = dbq.Where("quiz_id = ?", uid)
			}
		}
		if err := dbq.Order("created_at DESC").Find(&list).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(200, gin.H{"items": list})
	})
	api.POST("/import/questions", authz, requireRole("editor"), func(c *gin.Context) {
		var payload struct {
			Items []entities.Question `json:"items"`
		}
		if err := c.ShouldBindJSON(&payload); err != nil || len(payload.Items) == 0 {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "items required"})
			return
		}
		// basic validation
		for _, it := range payload.Items {
			if it.Text == "" {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid question in items - text is required"})
				return
			}
		}
		if err := database.DB().Create(&payload.Items).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(201, gin.H{"inserted": len(payload.Items)})
	})

	// CSV import (Questions) with per-row error reporting
	api.POST("/import/questions/csv", authz, requireRole("editor"), func(c *gin.Context) {
		file, err := c.FormFile("file")
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "file required"})
			return
		}
		f, err := file.Open()
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		defer f.Close()

		r := csv.NewReader(f)
		r.FieldsPerRecord = -1 // allow variable, we will validate
		rows, err := r.ReadAll()
		if err != nil && err != io.EOF {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		if len(rows) == 0 {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "empty csv"})
			return
		}

		// Optional header detection
		start := 0
		if isHeaderRow(rows[0]) {
			start = 1
		}

		inserted := 0
		var errorsArr []gin.H
		for i := start; i < len(rows); i++ {
			line := rows[i]
			// Expected: quiz_id,text,option_a,option_b,option_c,option_d,option_e(optional),correct_answer
			if len(line) < 7 { // without E: 7 elements (index 0..6)
				errorsArr = append(errorsArr, gin.H{"row": i + 1, "error": "insufficient columns"})
				continue
			}
			idx := 0
			quizStr := strings.TrimSpace(line[idx])
			idx++
			text := strings.TrimSpace(line[idx])
			idx++
			a := strings.TrimSpace(line[idx])
			idx++
			b := strings.TrimSpace(line[idx])
			idx++
			copt := strings.TrimSpace(line[idx])
			idx++
			d := strings.TrimSpace(line[idx])
			idx++

			var eopt string
			var correct string

			if len(line) == 7 {
				// no E column, next is correct
				correct = strings.ToUpper(strings.TrimSpace(line[idx]))
				idx++
			} else {
				// E provided
				eVal := strings.TrimSpace(line[idx])
				idx++
				if eVal != "" {
					eopt = eVal
				}
				if idx >= len(line) {
					errorsArr = append(errorsArr, gin.H{"row": i + 1, "error": "missing correct_answer"})
					continue
				}
				correct = strings.ToUpper(strings.TrimSpace(line[idx]))
				idx++
			}

			uid, err := uuid.Parse(quizStr)
			if err != nil {
				errorsArr = append(errorsArr, gin.H{"row": i + 1, "error": "invalid quiz_id"})
				continue
			}
			if text == "" || a == "" || b == "" || copt == "" || d == "" {
				errorsArr = append(errorsArr, gin.H{"row": i + 1, "error": "text and options A-D required"})
				continue
			}
			if correct == "" || !strings.Contains("ABCDE", correct) {
				errorsArr = append(errorsArr, gin.H{"row": i + 1, "error": "correct_answer must be A-E"})
				continue
			}

			q := entities.Question{QuizID: &uid, Text: text, OptionA: a, OptionB: b, OptionC: copt, OptionD: d, CorrectAnswer: correct}
			if eopt != "" {
				q.OptionE = &eopt
			}
			if err := database.DB().Create(&q).Error; err != nil {
				errorsArr = append(errorsArr, gin.H{"row": i + 1, "error": err.Error()})
				continue
			}
			inserted++
		}

		c.JSON(201, gin.H{"inserted": inserted, "errors": errorsArr})
	})

	// CSV template download
	api.GET("/import/questions/template.csv", authz, func(c *gin.Context) {
		template := "quiz_id,text,option_a,option_b,option_c,option_d,option_e,correct_answer\n" +
			"11111111-1111-1111-1111-111111111111,Ornek soru metni?,Secenek A,Secenek B,Secenek C,Secenek D,Secenek E,C\n"
		c.Header("Content-Type", "text/csv; charset=utf-8")
		c.Header("Content-Disposition", "attachment; filename=questions_template.csv")
		c.Data(200, "text/csv; charset=utf-8", []byte(template))
	})

	// Deterministic shuffled options preview
	api.GET("/questions/:id/rendered", authz, func(c *gin.Context) {
		id := c.Param("id")
		//userID := c.Query("user_id")
		var qst entities.Question
		if err := database.DB().First(&qst, "id = ?", id).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"error": "question not found"})
			return
		}
		// collect options
		type opt struct {
			Key  string
			Text string
		}
		opts := []opt{{"A", qst.OptionA}, {"B", qst.OptionB}, {"C", qst.OptionC}, {"D", qst.OptionD}}
		if qst.OptionE != nil && *qst.OptionE != "" {
			opts = append(opts, opt{"E", *qst.OptionE})
		}
		// seed: user_id + question_id
		//	seed := seedFrom(userID + id)
		//r := rand.New(rand.NewSource(seed))

		rand.Shuffle(len(opts), func(i, j int) { opts[i], opts[j] = opts[j], opts[i] })
		// map correct index
		var correctKey string
		if qst.CorrectAnswer != "" {
			correctKey = qst.CorrectAnswer
		}
		correctIndex := -1
		for i, o := range opts {
			if o.Key == correctKey {
				correctIndex = i
				break
			}
		}
		c.JSON(200, gin.H{"id": qst.ID, "text": qst.Text, "options": opts, "correct_index": correctIndex})
	})

	// Question CRUD handled by setupQuestionRoutes

	if _, err := os.Stat("./dist/index.html"); err == nil {
		// Serve explicit mappings for known asset directories and files
		app.Static("/static", "./dist/static")
		app.Static("/assets", "./dist/assets")
		app.StaticFile("/favicon.ico", "./dist/favicon.ico")
		app.StaticFile("/robots.txt", "./dist/robots.txt")
		app.StaticFile("/manifest.json", "./dist/manifest.json")
		app.StaticFile("/asset-manifest.json", "./dist/asset-manifest.json")
		app.StaticFile("/logo192.png", "./dist/logo192.png")
		app.StaticFile("/logo512.png", "./dist/logo512.png")
		app.StaticFile("/service-worker.js", "./dist/service-worker.js")

		// Serve privacy policy
		app.StaticFile("/privacy-policy.html", "./dist/privacy-policy.html")
		app.StaticFile("/privacy", "./dist/privacy-policy.html")
		app.StaticFile("/gizlilik", "./dist/privacy-policy.html")

		// Serve the main index.html for root
		app.GET("/", func(c *gin.Context) {
			c.File("./dist/index.html")
		})

		// Fallback for unknown routes (SPA routing)
		app.NoRoute(func(c *gin.Context) {
			p := c.Request.URL.Path
			// Do not hijack API routes
			if strings.HasPrefix(p, "/api/") {
				c.Status(404)
				return
			}
			// If the request looks like a file (has an extension), only serve if it exists
			if ext := path.Ext(p); ext != "" {
				full := path.Join("./dist", p)
				if _, err := os.Stat(full); err == nil {
					c.File(full)
					return
				}
				c.Status(404)
				return
			}
			// Otherwise, serve index.html for SPA client-side routes
			c.File("./dist/index.html")
		})
	}

	fmt.Println("Admin server running on port ", appc.Port)
	app.Run(":" + appc.Port)
}

// simple role hierarchy: admin > editor > viewer
func hasRole(actual, required string) bool {
	rank := map[string]int{"viewer": 1, "editor": 2, "admin": 3}
	return rank[actual] >= rank[required]
}

func isHeaderRow(cols []string) bool {
	if len(cols) < 7 {
		return false
	}
	first := strings.ToLower(strings.TrimSpace(cols[0]))
	return first == "quiz_id" || first == "id" || first == "quizid"
}

// Area Handlers
func setupAreaRoutes(api *gin.RouterGroup, areaService area.Service) {
	areas := api.Group("/areas")
	{
		areas.GET("", func(c *gin.Context) {
			areas, err := areaService.GetAll()
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"data": areas})
		})

		areas.GET("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			area, err := areaService.GetByID(id)
			if err != nil {
				c.JSON(http.StatusNotFound, gin.H{"error": "Area not found"})
				return
			}
			c.JSON(http.StatusOK, area)
		})

		areas.POST("", func(c *gin.Context) {
			var area entities.Area
			if err := c.ShouldBindJSON(&area); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			if err := areaService.Create(&area); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusCreated, area)
		})

		areas.PUT("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			var area entities.Area
			if err := c.ShouldBindJSON(&area); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			area.ID = id
			if err := areaService.Update(&area); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, area)
		})

		areas.DELETE("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			if err := areaService.Delete(id); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"message": "Area deleted successfully"})
		})
	}
}

// Subject Handlers
func setupSubjectRoutes(api *gin.RouterGroup, subjectService subject.Service) {
	subjects := api.Group("/subjects")
	{
		subjects.GET("", func(c *gin.Context) {
			areaIDStr := c.Query("area_id")
			if areaIDStr != "" {
				areaID, err := uuid.Parse(areaIDStr)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid area_id"})
					return
				}
				subjects, err := subjectService.GetByAreaID(areaID)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusOK, gin.H{"data": subjects})
				return
			}

			subjects, err := subjectService.GetAll()
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"data": subjects})
		})

		subjects.GET("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			subject, err := subjectService.GetByID(id)
			if err != nil {
				c.JSON(http.StatusNotFound, gin.H{"error": "Subject not found"})
				return
			}
			c.JSON(http.StatusOK, subject)
		})

		subjects.POST("", func(c *gin.Context) {
			var subject entities.Subject
			if err := c.ShouldBindJSON(&subject); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			if err := subjectService.Create(&subject); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusCreated, subject)
		})

		subjects.PUT("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			var subject entities.Subject
			if err := c.ShouldBindJSON(&subject); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			subject.ID = id
			if err := subjectService.Update(&subject); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, subject)
		})

		subjects.DELETE("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			if err := subjectService.Delete(id); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"message": "Subject deleted successfully"})
		})
	}
}

// Topic Handlers
func setupTopicRoutes(api *gin.RouterGroup, topicService topic.Service) {
	topics := api.Group("/topics")
	{
		topics.GET("", func(c *gin.Context) {
			limitStr := c.DefaultQuery("limit", "20")
			offsetStr := c.DefaultQuery("offset", "0")
			search := c.Query("q")
			subjectIDStr := c.Query("subject_id")
			parentIDStr := c.Query("parent_id")

			limit, _ := strconv.Atoi(limitStr)
			offset, _ := strconv.Atoi(offsetStr)

			if subjectIDStr != "" {
				subjectID, err := uuid.Parse(subjectIDStr)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subject_id"})
					return
				}
				topics, err := topicService.GetBySubjectID(subjectID)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusOK, gin.H{"data": topics, "total": len(topics)})
				return
			}

			if parentIDStr != "" {
				if parentIDStr == "null" {
					topics, err := topicService.GetByParentID(nil)
					if err != nil {
						c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
						return
					}
					c.JSON(http.StatusOK, gin.H{"data": topics, "total": len(topics)})
					return
				}
				parentID, err := uuid.Parse(parentIDStr)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid parent_id"})
					return
				}
				topics, err := topicService.GetByParentID(&parentID)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusOK, gin.H{"data": topics, "total": len(topics)})
				return
			}

			topics, total, err := topicService.GetAll(limit, offset, search)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"data": topics, "total": total})
		})

		topics.GET("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			topic, err := topicService.GetByID(id)
			if err != nil {
				c.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
				return
			}
			c.JSON(http.StatusOK, topic)
		})

		topics.POST("", func(c *gin.Context) {
			var topic entities.Topic
			if err := c.ShouldBindJSON(&topic); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			if err := topicService.Create(&topic); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusCreated, topic)
		})

		topics.PUT("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			var topic entities.Topic
			if err := c.ShouldBindJSON(&topic); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			topic.ID = id
			if err := topicService.Update(&topic); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, topic)
		})

		topics.DELETE("/:id", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			if err := topicService.Delete(id); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"message": "Topic deleted successfully"})
		})

		topics.PATCH("/:id/publish", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			if err := topicService.Publish(id); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"message": "Topic published successfully"})
		})

		topics.PATCH("/:id/unpublish", func(c *gin.Context) {
			id, err := uuid.Parse(c.Param("id"))
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
				return
			}

			if err := topicService.Unpublish(id); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"message": "Topic unpublished successfully"})
		})
	}
}
