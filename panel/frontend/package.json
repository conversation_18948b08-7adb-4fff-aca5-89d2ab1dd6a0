{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^9.3.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "i18next": "^22.5.1", "i18next-browser-languagedetector": "^7.2.1", "i18next-http-backend": "^2.2.0", "lucide-react": "^0.539.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.3.1", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.0"}}