package notification

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	// Notification management
	GetUserNotifications(userID string, payload *dtos.GetNotificationsRequest) (*dtos.NotificationsResponse, error)
	GetNotification(notificationID string) (*dtos.NotificationResponse, error)
	MarkNotificationAsRead(notificationID string) error
	MarkAllNotificationsAsRead(userID string) error
	DeleteNotification(notificationID string) error
	GetUnreadNotificationCount(userID string) (*dtos.UnreadCountResponse, error)

	// Notification creation
	CreateNotification(payload *dtos.CreateNotificationRequest) (*dtos.NotificationResponse, error)
	CreateBulkNotifications(payload *dtos.CreateBulkNotificationsRequest) (*dtos.BulkNotificationResponse, error)

	// Push notification management
	RegisterPushToken(userID string, payload *dtos.RegisterPushTokenRequest) error
	UnregisterPushToken(userID string, payload *dtos.UnregisterPushTokenRequest) error
	SendPushNotification(userID string, payload *dtos.SendPushNotificationRequest) error
	SendBulkPushNotifications(payload *dtos.SendBulkPushNotificationsRequest) error

	// Email notification management
	SendEmailNotification(userID string, payload *dtos.SendEmailNotificationRequest) error
	SendBulkEmailNotifications(payload *dtos.SendBulkEmailNotificationsRequest) error

	// Notification preferences
	UpdateNotificationPreferences(userID string, payload *dtos.UpdateNotificationPreferencesRequest) (*dtos.NotificationPreferencesResponse, error)
	GetNotificationPreferences(userID string) (*dtos.NotificationPreferencesResponse, error)

	// Notification templates
	CreateNotificationTemplate(payload *dtos.CreateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error)
	UpdateNotificationTemplate(templateID string, payload *dtos.UpdateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error)
	DeleteNotificationTemplate(templateID string) error
	GetNotificationTemplates(payload *dtos.GetNotificationTemplatesRequest) (*dtos.NotificationTemplatesResponse, error)

	// Notification analytics
	GetNotificationStats(userID string, payload *dtos.GetNotificationStatsRequest) (*dtos.NotificationStatsResponse, error)
	GetNotificationAnalytics(payload *dtos.GetNotificationAnalyticsRequest) (*dtos.NotificationAnalyticsResponse, error)

	// Notification scheduling
	ScheduleNotification(payload *dtos.ScheduleNotificationRequest) (*dtos.ScheduledNotificationResponse, error)
	CancelScheduledNotification(notificationID string) error
	GetScheduledNotifications(payload *dtos.GetScheduledNotificationsRequest) (*dtos.ScheduledNotificationsResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Notification management
func (r *repository) GetUserNotifications(userID string, payload *dtos.GetNotificationsRequest) (*dtos.NotificationsResponse, error) {
	page := payload.Page
	if page < 1 {
		page = 1
	}

	limit := payload.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	query := r.db.Model(&entities.Notification{}).Where("user_id = ?", userID)

	// Apply filters
	if payload.Type != "" {
		query = query.Where("type = ?", payload.Type)
	}
	if payload.IsRead != nil {
		query = query.Where("is_read = ?", *payload.IsRead)
	}

	// Apply sorting
	sortBy := payload.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := "ASC"
	if payload.SortDesc {
		sortOrder = "DESC"
	}

	query = query.Order(sortBy + " " + sortOrder)

	// Get total count
	var total int64
	query.Count(&total)

	// Get notifications
	var notifications []entities.Notification
	err := query.Offset(offset).Limit(limit).Find(&notifications).Error
	if err != nil {
		return nil, errors.New("Failed to get notifications")
	}

	// Convert to response format
	notificationResponses := make([]dtos.NotificationResponse, len(notifications))
	for i, notification := range notifications {
		response, err := r.getNotificationResponse(&notification)
		if err != nil {
			continue
		}
		notificationResponses[i] = *response
	}

	// Get unread count
	var unreadCount int64
	r.db.Model(&entities.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Count(&unreadCount)

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	return &dtos.NotificationsResponse{
		Notifications: notificationResponses,
		Total:         int(total),
		Page:          page,
		Limit:         limit,
		TotalPages:    totalPages,
		UnreadCount:   int(unreadCount),
	}, nil
}

func (r *repository) GetNotification(notificationID string) (*dtos.NotificationResponse, error) {
	var notification entities.Notification
	err := r.db.Where("id = ?", notificationID).First(&notification).Error
	if err != nil {
		return nil, errors.New("Notification not found")
	}

	return r.getNotificationResponse(&notification)
}

func (r *repository) MarkNotificationAsRead(notificationID string) error {
	now := time.Now()
	err := r.db.Model(&entities.Notification{}).Where("id = ?", notificationID).Updates(map[string]interface{}{
		"is_read": true,
		"read_at": &now,
	}).Error
	if err != nil {
		return errors.New("Failed to mark notification as read")
	}
	return nil
}

func (r *repository) MarkAllNotificationsAsRead(userID string) error {
	now := time.Now()
	err := r.db.Model(&entities.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Updates(map[string]interface{}{
		"is_read": true,
		"read_at": &now,
	}).Error
	if err != nil {
		return errors.New("Failed to mark all notifications as read")
	}
	return nil
}

func (r *repository) DeleteNotification(notificationID string) error {
	err := r.db.Where("id = ?", notificationID).Delete(&entities.Notification{}).Error
	if err != nil {
		return errors.New("Failed to delete notification")
	}
	return nil
}

func (r *repository) GetUnreadNotificationCount(userID string) (*dtos.UnreadCountResponse, error) {
	var count int64
	err := r.db.Model(&entities.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Count(&count).Error
	if err != nil {
		return nil, errors.New("Failed to get unread notification count")
	}

	return &dtos.UnreadCountResponse{
		UnreadCount: int(count),
	}, nil
}

// Notification creation
func (r *repository) CreateNotification(payload *dtos.CreateNotificationRequest) (*dtos.NotificationResponse, error) {
	notification := entities.Notification{
		UserID:           payload.UserID,
		Type:             entities.NotificationType(payload.Type),
		Title:            payload.Title,
		Message:          payload.Message,
		Data:             payload.Data,
		RelatedUserID:    payload.RelatedUserID,
		RelatedContentID: payload.RelatedContentID,
		RelatedQuizID:    payload.RelatedQuizID,
	}

	err := r.db.Create(&notification).Error
	if err != nil {
		return nil, errors.New("Failed to create notification")
	}

	return r.getNotificationResponse(&notification)
}

func (r *repository) CreateBulkNotifications(payload *dtos.CreateBulkNotificationsRequest) (*dtos.BulkNotificationResponse, error) {
	notifications := make([]entities.Notification, len(payload.UserIDs))
	for i, userID := range payload.UserIDs {
		notifications[i] = entities.Notification{
			UserID:  userID,
			Type:    entities.NotificationType(payload.Type),
			Title:   payload.Title,
			Message: payload.Message,
			Data:    payload.Data,
		}
	}

	err := r.db.Create(&notifications).Error
	if err != nil {
		return &dtos.BulkNotificationResponse{
			SuccessCount: 0,
			FailureCount: len(payload.UserIDs),
			Errors:       []string{"Failed to create bulk notifications"},
		}, nil
	}

	// Convert to response format
	notificationResponses := make([]dtos.NotificationResponse, len(notifications))
	for i, notification := range notifications {
		response, err := r.getNotificationResponse(&notification)
		if err != nil {
			continue
		}
		notificationResponses[i] = *response
	}

	return &dtos.BulkNotificationResponse{
		SuccessCount:  len(notifications),
		FailureCount:  0,
		Notifications: notificationResponses,
	}, nil
}

// Helper method to convert entity to response
func (r *repository) getNotificationResponse(notification *entities.Notification) (*dtos.NotificationResponse, error) {
	response := &dtos.NotificationResponse{
		ID:               notification.ID,
		UserID:           notification.UserID,
		Type:             string(notification.Type),
		Title:            notification.Title,
		Message:          notification.Message,
		Data:             notification.Data,
		IsRead:           notification.IsRead,
		ReadAt:           notification.ReadAt,
		RelatedUserID:    notification.RelatedUserID,
		RelatedContentID: notification.RelatedContentID,
		RelatedQuizID:    notification.RelatedQuizID,
		CreatedAt:        notification.CreatedAt,
		UpdatedAt:        notification.UpdatedAt,
	}

	// Load related user if exists
	if notification.RelatedUserID != nil {
		var relatedUser entities.User
		err := r.db.Where("id = ?", *notification.RelatedUserID).First(&relatedUser).Error
		if err == nil {
			response.RelatedUser = &dtos.UserInfo{
				ID:       relatedUser.ID,
				Username: relatedUser.Username,
				Name:     relatedUser.Name,
				// Add other fields as needed
			}
		}
	}

	return response, nil
}

// Push notification management (placeholder implementations)
func (r *repository) RegisterPushToken(userID string, payload *dtos.RegisterPushTokenRequest) error {
	// TODO: Implement push token registration
	return nil
}

func (r *repository) UnregisterPushToken(userID string, payload *dtos.UnregisterPushTokenRequest) error {
	// TODO: Implement push token unregistration
	return nil
}

func (r *repository) SendPushNotification(userID string, payload *dtos.SendPushNotificationRequest) error {
	// TODO: Implement push notification sending
	return nil
}

func (r *repository) SendBulkPushNotifications(payload *dtos.SendBulkPushNotificationsRequest) error {
	// TODO: Implement bulk push notification sending
	return nil
}

// Email notification management (placeholder implementations)
func (r *repository) SendEmailNotification(userID string, payload *dtos.SendEmailNotificationRequest) error {
	// TODO: Implement email notification sending
	return nil
}

func (r *repository) SendBulkEmailNotifications(payload *dtos.SendBulkEmailNotificationsRequest) error {
	// TODO: Implement bulk email notification sending
	return nil
}

// Notification preferences (placeholder implementations)
func (r *repository) UpdateNotificationPreferences(userID string, payload *dtos.UpdateNotificationPreferencesRequest) (*dtos.NotificationPreferencesResponse, error) {
	// TODO: Implement notification preferences update
	return &dtos.NotificationPreferencesResponse{
		UserID:                          uuid.MustParse(userID),
		PushEnabled:                     true,
		EmailEnabled:                    true,
		FriendRequestsEnabled:           true,
		QuizNotificationsEnabled:        true,
		ProgressNotificationsEnabled:    true,
		AchievementNotificationsEnabled: true,
		SystemNotificationsEnabled:      true,
		MarketingEmailsEnabled:          false,
		WeeklyDigestEnabled:             true,
		DailyRemindersEnabled:           true,
		UpdatedAt:                       time.Now(),
	}, nil
}

func (r *repository) GetNotificationPreferences(userID string) (*dtos.NotificationPreferencesResponse, error) {
	// TODO: Implement notification preferences retrieval
	return &dtos.NotificationPreferencesResponse{
		UserID:                          uuid.MustParse(userID),
		PushEnabled:                     true,
		EmailEnabled:                    true,
		FriendRequestsEnabled:           true,
		QuizNotificationsEnabled:        true,
		ProgressNotificationsEnabled:    true,
		AchievementNotificationsEnabled: true,
		SystemNotificationsEnabled:      true,
		MarketingEmailsEnabled:          false,
		WeeklyDigestEnabled:             true,
		DailyRemindersEnabled:           true,
		UpdatedAt:                       time.Now(),
	}, nil
}

// Notification templates (placeholder implementations)
func (r *repository) CreateNotificationTemplate(payload *dtos.CreateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error) {
	// TODO: Implement notification template creation
	return &dtos.NotificationTemplateResponse{
		ID:           uuid.New(),
		Name:         payload.Name,
		Type:         payload.Type,
		Title:        payload.Title,
		Message:      payload.Message,
		EmailSubject: payload.EmailSubject,
		EmailBody:    payload.EmailBody,
		Variables:    payload.Variables,
		IsActive:     payload.IsActive,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}, nil
}

func (r *repository) UpdateNotificationTemplate(templateID string, payload *dtos.UpdateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error) {
	// TODO: Implement notification template update
	return &dtos.NotificationTemplateResponse{
		ID:        uuid.MustParse(templateID),
		UpdatedAt: time.Now(),
	}, nil
}

func (r *repository) DeleteNotificationTemplate(templateID string) error {
	// TODO: Implement notification template deletion
	return nil
}

func (r *repository) GetNotificationTemplates(payload *dtos.GetNotificationTemplatesRequest) (*dtos.NotificationTemplatesResponse, error) {
	// TODO: Implement notification templates retrieval
	return &dtos.NotificationTemplatesResponse{
		Templates:  []dtos.NotificationTemplateResponse{},
		Total:      0,
		Page:       1,
		Limit:      20,
		TotalPages: 0,
	}, nil
}

// Notification analytics (placeholder implementations)
func (r *repository) GetNotificationStats(userID string, payload *dtos.GetNotificationStatsRequest) (*dtos.NotificationStatsResponse, error) {
	// TODO: Implement notification statistics
	return &dtos.NotificationStatsResponse{
		UserID:                   uuid.MustParse(userID),
		Period:                   "custom",
		StartDate:                payload.StartDate,
		EndDate:                  payload.EndDate,
		TotalNotifications:       0,
		ReadNotifications:        0,
		UnreadNotifications:      0,
		PushNotificationsSent:    0,
		EmailNotificationsSent:   0,
		FriendNotifications:      0,
		QuizNotifications:        0,
		ProgressNotifications:    0,
		AchievementNotifications: 0,
		SystemNotifications:      0,
		ReadRate:                 0.0,
		ClickThroughRate:         0.0,
		AverageReadTime:          0.0,
		DailyStats:               []dtos.DailyNotificationStat{},
	}, nil
}

func (r *repository) GetNotificationAnalytics(payload *dtos.GetNotificationAnalyticsRequest) (*dtos.NotificationAnalyticsResponse, error) {
	// TODO: Implement notification analytics
	return &dtos.NotificationAnalyticsResponse{
		Period:                      "custom",
		StartDate:                   payload.StartDate,
		EndDate:                     payload.EndDate,
		TotalNotifications:          0,
		TotalUsers:                  0,
		AverageNotificationsPerUser: 0.0,
		OverallReadRate:             0.0,
		PlatformStats:               make(map[string]dtos.PlatformNotificationStat),
		TypeStats:                   make(map[string]dtos.TypeNotificationStat),
		HourlyDistribution:          []dtos.HourlyNotificationStat{},
	}, nil
}

// Notification scheduling (placeholder implementations)
func (r *repository) ScheduleNotification(payload *dtos.ScheduleNotificationRequest) (*dtos.ScheduledNotificationResponse, error) {
	// TODO: Implement notification scheduling
	return &dtos.ScheduledNotificationResponse{
		ID:          uuid.New(),
		UserID:      payload.UserID,
		Type:        payload.Type,
		Title:       payload.Title,
		Message:     payload.Message,
		Data:        payload.Data,
		ScheduledAt: payload.ScheduledAt,
		SendPush:    payload.SendPush,
		SendEmail:   payload.SendEmail,
		Status:      "pending",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}, nil
}

func (r *repository) CancelScheduledNotification(notificationID string) error {
	// TODO: Implement scheduled notification cancellation
	return nil
}

func (r *repository) GetScheduledNotifications(payload *dtos.GetScheduledNotificationsRequest) (*dtos.ScheduledNotificationsResponse, error) {
	// TODO: Implement scheduled notifications retrieval
	return &dtos.ScheduledNotificationsResponse{
		Notifications: []dtos.ScheduledNotificationResponse{},
		Total:         0,
		Page:          1,
		Limit:         20,
		TotalPages:    0,
	}, nil
}
