import React from 'react';
import { Routes, Route } from 'react-router-dom';
import QuizList from './QuizList';
import QuizDetail from './QuizDetail';
import QuizSession from './QuizSession';
import QuizResults from './QuizResults';

const QuizPage: React.FC = () => {
  return (
    <Routes>
      <Route index element={<QuizList />} />
      <Route path=":id" element={<QuizDetail />} />
      <Route path=":quizId/session/:sessionId" element={<QuizSession />} />
      <Route path=":quizId/results/:resultId" element={<QuizResults />} />
    </Routes>
  );
};

export default QuizPage;
