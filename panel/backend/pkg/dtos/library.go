package dtos

import "github.com/google/uuid"

type CreateFolderRequest struct {
    Name string `json:"name" validate:"required,min=1,max=100"`
}

type UpdateFolderRequest struct {
    Name string `json:"name" validate:"required,min=1,max=100"`
}

type FolderResponse struct {
    ID   uuid.UUID `json:"id"`
    Name string    `json:"name"`
}

type FoldersResponse struct {
    Folders []FolderResponse `json:"folders"`
}

type LibraryItemResponse struct {
    ID       uuid.UUID  `json:"id"`
    ItemType string     `json:"item_type"`
    ItemID   uuid.UUID  `json:"item_id"`
    FolderID *uuid.UUID `json:"folder_id"`
}

type GetLibraryRequest struct {
    Type     string     `json:"type" validate:"omitempty,oneof=content topic quiz"`
    FolderID *uuid.UUID `json:"folder_id"`
    Page     int        `json:"page"`
    Limit    int        `json:"limit"`
}

type LibraryListResponse struct {
    Items     []LibraryItemResponse `json:"items"`
    Total     int                   `json:"total"`
    Page      int                   `json:"page"`
    Limit     int                   `json:"limit"`
    TotalPages int                  `json:"total_pages"`
}

// Minimal details to render cards in library
type LibraryDetailContent struct {
    ID      uuid.UUID `json:"id"`
    Title   string    `json:"title"`
    Type    string    `json:"type"`
    Subject *string   `json:"subject"`
}

type LibraryDetailTopic struct {
    ID      uuid.UUID `json:"id"`
    Title   string    `json:"title"`
    Subject *string   `json:"subject"`
}

type LibraryDetailQuiz struct {
    ID        uuid.UUID `json:"id"`
    Title     string    `json:"title"`
    Difficulty *string  `json:"difficulty"`
    TimeLimit  *int     `json:"time_limit"`
}

type LibraryItemDetail struct {
    Content *LibraryDetailContent `json:"content,omitempty"`
    Topic   *LibraryDetailTopic   `json:"topic,omitempty"`
    Quiz    *LibraryDetailQuiz    `json:"quiz,omitempty"`
}

type LibraryListWithDetailsResponse struct {
    Items      []LibraryItemResponse `json:"items"`
    Details    map[string]LibraryItemDetail `json:"details"` // key: item_id
    Total      int                   `json:"total"`
    Page       int                   `json:"page"`
    Limit      int                   `json:"limit"`
    TotalPages int                   `json:"total_pages"`
}
