import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {SocialStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {SocialHomeScreen} from '../screens/social/SocialHomeScreen';
import {TimelineScreen} from '../screens/social/TimelineScreen';
import {ProfileScreen} from '../screens/social/ProfileScreen';
import {FriendsScreen} from '../screens/social/FriendsScreen';
import {FriendRequestsScreen} from '../screens/social/FriendRequestsScreen';
import {UserSearchScreen} from '../screens/social/UserSearchScreen';
import {PostDetailScreen} from '../screens/social/PostDetailScreen';
import {CreatePostScreen} from '../screens/social/CreatePostScreen';
import {LeaderboardScreen} from '../screens/social/LeaderboardScreen';

const Stack = createStackNavigator<SocialStackParamList>();

export const SocialStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.white,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackTitle: '',
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="SocialHome"
        component={SocialHomeScreen}
        options={{
          title: 'Sosyal',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="Timeline"
        component={TimelineScreen}
        options={{
          title: 'Zaman Akışı',
        }}
      />
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profil',
        }}
      />
      <Stack.Screen
        name="Friends"
        component={FriendsScreen}
        options={{
          title: 'Arkadaşlar',
        }}
      />
      <Stack.Screen
        name="FriendRequests"
        component={FriendRequestsScreen}
        options={{
          title: 'Arkadaşlık İstekleri',
        }}
      />
      <Stack.Screen
        name="UserSearch"
        component={UserSearchScreen}
        options={{
          title: 'Kullanıcı Ara',
        }}
      />
      <Stack.Screen
        name="PostDetail"
        component={PostDetailScreen}
        options={{
          title: 'Gönderi',
        }}
      />
      <Stack.Screen
        name="CreatePost"
        component={CreatePostScreen}
        options={{
          title: 'Gönderi Oluştur',
        }}
      />
      <Stack.Screen
        name="Leaderboard"
        component={LeaderboardScreen}
        options={{
          title: 'Liderlik Tablosu',
        }}
      />
    </Stack.Navigator>
  );
};
