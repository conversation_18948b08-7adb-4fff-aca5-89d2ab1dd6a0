package entities

import (
	"time"

	"github.com/google/uuid"
)

// NotificationType represents different types of notifications
type NotificationType string

const (
	NotificationTypeFriend      NotificationType = "friend"
	NotificationTypeQuiz        NotificationType = "quiz"
	NotificationTypeProgress    NotificationType = "progress"
	NotificationTypeAchievement NotificationType = "achievement"
	NotificationTypeSystem      NotificationType = "system"
)

// Notification represents push and in-app notifications
type Notification struct {
	Base
	UserID  uuid.UUID              `json:"user_id" gorm:"type:uuid;not null;index"`
	Type    NotificationType       `json:"type" gorm:"type:varchar(30);not null"`
	Title   string                 `json:"title" gorm:"not null"`
	Message string                 `json:"message" gorm:"not null"`
	Data    map[string]interface{} `json:"data" gorm:"type:jsonb"` // Additional data for the notification

	// Status
	IsRead bool       `json:"is_read" gorm:"default:false"`
	ReadAt *time.Time `json:"read_at"`

	// Reference IDs for different notification types
	RelatedUserID    *uuid.UUID `json:"related_user_id" gorm:"type:uuid"` // User who triggered the notification
	RelatedContentID *uuid.UUID `json:"related_content_id" gorm:"type:uuid"`
	RelatedQuizID    *uuid.UUID `json:"related_quiz_id" gorm:"type:uuid"`
}

// NotificationSettings represents user notification preferences
type NotificationSettings struct {
	Base
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null;uniqueIndex"`

	// Push notification settings
	FriendRequestPush   bool `json:"friend_request_push" gorm:"default:true"`
	FriendAcceptedPush  bool `json:"friend_accepted_push" gorm:"default:true"`
	NewFollowerPush     bool `json:"new_follower_push" gorm:"default:true"`
	QuizInvitePush      bool `json:"quiz_invite_push" gorm:"default:true"`
	ProgressUpdatePush  bool `json:"progress_update_push" gorm:"default:false"`
	BadgeEarnedPush     bool `json:"badge_earned_push" gorm:"default:true"`
	LeaderboardPush     bool `json:"leaderboard_push" gorm:"default:false"`
	ReminderPush        bool `json:"reminder_push" gorm:"default:true"`
	TimelineLikePush    bool `json:"timeline_like_push" gorm:"default:true"`
	TimelineCommentPush bool `json:"timeline_comment_push" gorm:"default:true"`

	// Email notification settings
	FriendRequestEmail  bool `json:"friend_request_email" gorm:"default:false"`
	FriendAcceptedEmail bool `json:"friend_accepted_email" gorm:"default:false"`
	WeeklyReportEmail   bool `json:"weekly_report_email" gorm:"default:true"`
	MonthlyReportEmail  bool `json:"monthly_report_email" gorm:"default:true"`

	// Study reminder settings
	DailyReminderEnabled bool      `json:"daily_reminder_enabled" gorm:"default:true"`
	ReminderTime         time.Time `json:"reminder_time"`
	WeekendReminders     bool      `json:"weekend_reminders" gorm:"default:false"`
}

func (Notification) TableName() string {
	return "notifications"
}

func (NotificationSettings) TableName() string {
	return "notification_settings"
}
