import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout';
import { apiGet, apiPost, apiPut, apiDel } from '../lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  BookOpenIcon,
  FolderIcon
} from '@heroicons/react/24/outline';

interface Area {
  id: string;
  name: string;
  code: string;
}

interface Subject {
  id: string;
  area_id: string;
  name: string;
  code: string;
  area?: Area;
}

interface Topic {
  id: string;
  subject_id?: string;
  parent_id?: string;
  title: string;
  description?: string;
  body?: string;
  order?: number;
  is_published?: boolean;
  achievement_code?: string;
  subject?: Subject;
  parent?: Topic;
  children?: Topic[];
}

const Topics: React.FC = () => {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    body: '',
    subject_id: '',
    parent_id: '',
    order: '',
    achievement_code: '',
    is_published: true
  });

  useEffect(() => {
    loadTopics();
    loadSubjects();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, selectedSubject]);

  const loadTopics = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchQuery) params.append('q', searchQuery);
      if (selectedSubject) params.append('subject_id', selectedSubject);
      params.append('limit', '50');

      const response = await apiGet<{ data: Topic[]; total: number }>(`/topics?${params}`);
      setTopics(response.data || []);
    } catch (error) {
      console.error('Error loading topics:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSubjects = async () => {
    try {
      const response = await apiGet<{ data: Subject[] }>('/subjects');
      setSubjects(response.data || []);
    } catch (error) {
      console.error('Error loading subjects:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const data = {
        ...formData,
        order: formData.order ? parseInt(formData.order) : undefined
      };

      if (editingTopic) {
        await apiPut(`/topics/${editingTopic.id}`, data);
      } else {
        await apiPost('/topics', data);
      }

      setShowCreateModal(false);
      setEditingTopic(null);
      resetForm();
      loadTopics();
    } catch (error) {
      console.error('Error saving topic:', error);
    }
  };

  const handleEdit = (topic: Topic) => {
    setEditingTopic(topic);
    setFormData({
      title: topic.title,
      description: topic.description || '',
      body: topic.body || '',
      subject_id: topic.subject_id || '',
      parent_id: topic.parent_id || '',
      order: topic.order?.toString() || '',
      achievement_code: topic.achievement_code || '',
      is_published: topic.is_published ?? true
    });
    setShowCreateModal(true);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Bu konuyu silmek istediğinizden emin misiniz?')) return;

    try {
      await apiDel(`/topics/${id}`);
      loadTopics();
    } catch (error) {
      console.error('Error deleting topic:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      body: '',
      subject_id: '',
      parent_id: '',
      order: '',
      achievement_code: '',
      is_published: true
    });
  };

  const handleCreateNew = () => {
    setEditingTopic(null);
    resetForm();
    setShowCreateModal(true);
  };

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Konular
            </h1>
            <p className="mt-2 text-slate-300">
              KPSS konularını yönetin (ünite/alt-konu ağaç yapısı)
            </p>
          </div>
          <button
            onClick={handleCreateNew}
            className="mt-4 sm:mt-0 btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Konu Ekle
          </button>
        </div>

        {/* Filters */}
        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Arama
              </label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                <input
                  type="text"
                  placeholder="Konu ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="input-field pl-10"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Ders Filtresi
              </label>
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="input-field"
              >
                <option value="">Tüm Dersler</option>
                {subjects.map((subject) => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Topics List */}
        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-slate-400">Yükleniyor...</p>
            </div>
          ) : topics.length === 0 ? (
            <div className="p-8 text-center">
              <FolderIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-400">Henüz konu bulunmuyor.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-slate-900/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Konu
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Ders
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Sıra
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Durum
                    </th>
                    <th className="px-6 py-4 text-right text-xs font-medium text-slate-300 uppercase tracking-wider">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-700/50">
                  {topics.map((topic) => (
                    <tr key={topic.id} className="hover:bg-slate-700/30 transition-colors duration-200">
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <BookOpenIcon className="h-5 w-5 text-blue-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-slate-200">
                              {topic.title}
                            </div>
                            {topic.description && (
                              <div className="text-sm text-slate-400 mt-1">
                                {topic.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-300">
                        {topic.subject?.name || '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-300">
                        {topic.order || '-'}
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          topic.is_published
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {topic.is_published ? (
                            <>
                              <EyeIcon className="h-3 w-3 mr-1" />
                              Yayında
                            </>
                          ) : (
                            <>
                              <EyeSlashIcon className="h-3 w-3 mr-1" />
                              Taslak
                            </>
                          )}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleEdit(topic)}
                            className="text-blue-400 hover:text-blue-300 transition-colors duration-200"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(topic.id)}
                            className="text-red-400 hover:text-red-300 transition-colors duration-200"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-slate-800 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-white mb-6">
              {editingTopic ? 'Konu Düzenle' : 'Yeni Konu Ekle'}
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Başlık *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="input-field"
                    placeholder="Konu başlığı"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Ders
                  </label>
                  <select
                    value={formData.subject_id}
                    onChange={(e) => setFormData({ ...formData, subject_id: e.target.value })}
                    className="input-field"
                  >
                    <option value="">Ders Seçin</option>
                    {subjects.map((subject) => (
                      <option key={subject.id} value={subject.id}>
                        {subject.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Açıklama
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="input-field"
                  rows={3}
                  placeholder="Konu açıklaması"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  İçerik
                </label>
                <textarea
                  value={formData.body}
                  onChange={(e) => setFormData({ ...formData, body: e.target.value })}
                  className="input-field"
                  rows={6}
                  placeholder="Konu içeriği"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Sıra
                  </label>
                  <input
                    type="number"
                    value={formData.order}
                    onChange={(e) => setFormData({ ...formData, order: e.target.value })}
                    className="input-field"
                    placeholder="Sıra numarası"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Kazanım Kodu
                  </label>
                  <input
                    type="text"
                    value={formData.achievement_code}
                    onChange={(e) => setFormData({ ...formData, achievement_code: e.target.value })}
                    className="input-field"
                    placeholder="Kazanım kodu"
                  />
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_published"
                  checked={formData.is_published}
                  onChange={(e) => setFormData({ ...formData, is_published: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_published" className="ml-2 block text-sm text-slate-300">
                  Yayında
                </label>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="btn-secondary"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  {editingTopic ? 'Güncelle' : 'Oluştur'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default Topics;
