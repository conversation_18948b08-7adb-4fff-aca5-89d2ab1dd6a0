import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

const Dashboard = () => {
  const [analytics, setAnalytics] = useState({});
  const [userAnalytics, setUserAnalytics] = useState({});
  const [contentAnalytics, setContentAnalytics] = useState({});
  const [loading, setLoading] = useState(true);

  const API_URL = process.env.REACT_APP_ADMIN_API_URL || 'http://localhost:8100/api';

  const fetchAnalytics = useCallback(async () => {
    const token = localStorage.getItem('adminToken');
    
    try {
      const [analyticsRes, userAnalyticsRes, contentAnalyticsRes] = await Promise.all([
        axios.get(`${API_URL}/analytics`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get(`${API_URL}/analytics/users`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        axios.get(`${API_URL}/analytics/content`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      ]);

      setAnalytics(analyticsRes.data);
      setUserAnalytics(userAnalyticsRes.data);
      setContentAnalytics(contentAnalyticsRes.data);
    } catch (err) {
      console.error('Failed to fetch analytics:', err);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  useEffect(() => {
    const loadAnalytics = () => {
      fetchAnalytics();
    };
    loadAnalytics();
  }, [fetchAnalytics]);

  if (loading) {
    return <div className="loading">Loading analytics...</div>;
  }

  return (
    <div>
      <h2>Dashboard</h2>
      
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h3>Total Users</h3>
          <div className="number">{analytics.total_users || 0}</div>
          <p>Registered users</p>
        </div>
        
        <div className="dashboard-card">
          <h3>Total Content</h3>
          <div className="number">{analytics.total_content || 0}</div>
          <p>Content items</p>
        </div>
        
        <div className="dashboard-card">
          <h3>Active Users</h3>
          <div className="number">{userAnalytics.active_users || 0}</div>
          <p>Active in last 7 days</p>
        </div>
        
        <div className="dashboard-card">
          <h3>New Users Today</h3>
          <div className="number">{userAnalytics.new_users_today || 0}</div>
          <p>Registered today</p>
        </div>
        
        <div className="dashboard-card">
          <h3>Published Content</h3>
          <div className="number">{contentAnalytics.published_content || 0}</div>
          <p>Published content items</p>
        </div>
        
        <div className="dashboard-card">
          <h3>Draft Content</h3>
          <div className="number">{contentAnalytics.draft_content || 0}</div>
          <p>Draft content items</p>
        </div>
      </div>
    </div>
  );
};

export { Dashboard };