import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors, typography, spacing} from '../../theme';

export const QuizResultScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Quiz Sonucu</Text>
        <Text style={styles.description}>
          Bu ekran henüz geliştirilme aşamasında. Yakında burada quiz sonucu
          ilgili özellikler yer alacak.
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing[4],
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    ...typography.h3,
    marginBottom: spacing[4],
    textAlign: 'center',
  },
  description: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
