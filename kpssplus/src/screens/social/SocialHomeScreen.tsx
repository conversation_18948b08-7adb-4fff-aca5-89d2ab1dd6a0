import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {CompositeScreenProps} from '@react-navigation/native';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {
  SocialStackParamList,
  MainTabParamList,
  RootStackParamList,
} from '../../navigation/types';
import {Button, Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';

type Props = CompositeScreenProps<
  StackScreenProps<SocialStackParamList, 'SocialHome'>,
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, 'Social'>,
    StackScreenProps<RootStackParamList>
  >
>;

export const SocialHomeScreen: React.FC<Props> = ({navigation}) => {
  const {isGuest} = useAuth();

  if (isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.guestContainer}>
          <Icon name="people-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.guestTitle}>Sosyal Özellikler</Text>
          <Text style={styles.guestDescription}>
            Arkadaşlarınızla etkileşime geçmek, paylaşımlar yapmak ve sosyal
            özelliklerden yararlanmak için giriş yapın.
          </Text>
          <Button
            title="Giriş Yap"
            onPress={() => {
              navigation.navigate('Auth', {
                screen: 'Login',
              });
            }}
            style={styles.loginButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={() => console.log('Navigation disabled')}>
            <Icon name="newspaper-outline" size={24} color={colors.primary} />
            <Text style={styles.actionTitle}>Zaman Akışı</Text>
            <Text style={styles.actionSubtitle}>Son paylaşımları gör</Text>
          </Card>

          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={() => console.log('Navigation disabled')}>
            <Icon name="people-outline" size={24} color={colors.primary} />
            <Text style={styles.actionTitle}>Arkadaşlar</Text>
            <Text style={styles.actionSubtitle}>Arkadaş listeni gör</Text>
          </Card>
        </View>

        <View style={styles.quickActions}>
          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={() => console.log('Navigation disabled')}>
            <Icon name="person-add-outline" size={24} color={colors.primary} />
            <Text style={styles.actionTitle}>İstekler</Text>
            <Text style={styles.actionSubtitle}>Arkadaşlık istekleri</Text>
          </Card>

          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={() => console.log('Navigation disabled')}>
            <Icon name="trophy-outline" size={24} color={colors.primary} />
            <Text style={styles.actionTitle}>Liderlik Tablosu</Text>
            <Text style={styles.actionSubtitle}>Sıralamayı gör</Text>
          </Card>
        </View>

        <View style={styles.quickActions}>
          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={() => console.log('Navigation disabled')}>
            <Icon name="search-outline" size={24} color={colors.primary} />
            <Text style={styles.actionTitle}>Kullanıcı Ara</Text>
            <Text style={styles.actionSubtitle}>Yeni arkadaşlar bul</Text>
          </Card>

          <Card
            style={styles.actionCard}
            variant="elevated"
            onPress={() => console.log('Navigation disabled')}>
            <Icon name="add-circle-outline" size={24} color={colors.primary} />
            <Text style={styles.actionTitle}>Paylaşım Yap</Text>
            <Text style={styles.actionSubtitle}>Yeni gönderi oluştur</Text>
          </Card>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Son Aktiviteler</Text>

          {[1, 2, 3, 4].map(item => (
            <Card
              key={item}
              style={styles.activityCard}
              variant="outlined"
              onPress={() => console.log('Navigation disabled')}>
              <View style={styles.activityHeader}>
                <View style={styles.avatar}>
                  <Text style={styles.avatarText}>A{item}</Text>
                </View>
                <View style={styles.activityInfo}>
                  <Text style={styles.activityUser}>Ahmet Yılmaz {item}</Text>
                  <Text style={styles.activityTime}>2 saat önce</Text>
                </View>
              </View>
              <Text style={styles.activityContent}>
                Bugün matematik quizinde harika bir sonuç aldım! 95 puan ile
                liderlik tablosunda 3. sıradayım 🎉
              </Text>
              <View style={styles.activityFooter}>
                <View style={styles.activityAction}>
                  <Icon
                    name="heart-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.actionCount}>{5 + item}</Text>
                </View>
                <View style={styles.activityAction}>
                  <Icon
                    name="chatbubble-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.actionCount}>{2 + item}</Text>
                </View>
                <View style={styles.activityAction}>
                  <Icon
                    name="share-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.actionCount}>{item}</Text>
                </View>
              </View>
            </Card>
          ))}
        </View>

        {/* Friend Suggestions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Arkadaş Önerileri</Text>

          {[1, 2, 3].map(item => (
            <Card key={item} style={styles.suggestionCard} variant="outlined">
              <View style={styles.suggestionHeader}>
                <View style={styles.avatar}>
                  <Text style={styles.avatarText}>M{item}</Text>
                </View>
                <View style={styles.suggestionInfo}>
                  <Text style={styles.suggestionName}>Mehmet Kaya {item}</Text>
                  <Text style={styles.suggestionMutual}>
                    {item + 2} ortak arkadaş
                  </Text>
                </View>
                <Button
                  title="Ekle"
                  size="small"
                  style={styles.addButton}
                  onPress={() => {
                    /* Send friend request */
                  }}
                />
              </View>
            </Card>
          ))}
        </View>

        {/* Leaderboard Preview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bu Hafta Liderler</Text>

          {[1, 2, 3].map(item => (
            <Card key={item} style={styles.leaderCard} variant="outlined">
              <View style={styles.leaderRank}>
                <Text style={styles.rankNumber}>#{item}</Text>
              </View>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>L{item}</Text>
              </View>
              <View style={styles.leaderInfo}>
                <Text style={styles.leaderName}>Lider {item}</Text>
                <Text style={styles.leaderScore}>{1000 - item * 50} puan</Text>
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <View style={styles.fab}>
        <Button
          title="+"
          onPress={() => console.log('Navigation disabled')}
          style={styles.fabButton}
          textStyle={styles.fabText}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing[4],
  },
  guestContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[6],
  },
  guestTitle: {
    ...typography.h3,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  guestDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[6],
    lineHeight: 24,
  },
  loginButton: {
    paddingHorizontal: spacing[8],
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing[2],
  },
  actionCard: {
    flex: 1,
    marginHorizontal: spacing[1],
    alignItems: 'center',
    paddingVertical: spacing[4],
  },
  actionTitle: {
    ...typography.cardTitle,
    marginTop: spacing[2],
    marginBottom: spacing[1],
    textAlign: 'center',
  },
  actionSubtitle: {
    ...typography.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  section: {
    marginVertical: spacing[4],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[3],
  },
  activityCard: {
    marginBottom: spacing[3],
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  avatarText: {
    ...typography.body2,
    color: colors.white,
    fontWeight: '600',
  },
  activityInfo: {
    flex: 1,
  },
  activityUser: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  activityTime: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  activityContent: {
    ...typography.body1,
    marginBottom: spacing[3],
    lineHeight: 20,
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  activityAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionCount: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  suggestionCard: {
    marginBottom: spacing[2],
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionInfo: {
    flex: 1,
    marginLeft: spacing[3],
  },
  suggestionName: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  suggestionMutual: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  addButton: {
    paddingHorizontal: spacing[4],
  },
  leaderCard: {
    marginBottom: spacing[2],
  },
  leaderRank: {
    width: 30,
    alignItems: 'center',
    marginRight: spacing[3],
  },
  rankNumber: {
    ...typography.h6,
    color: colors.primary,
  },
  leaderInfo: {
    flex: 1,
    marginLeft: spacing[3],
  },
  leaderName: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  leaderScore: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  fab: {
    position: 'absolute',
    bottom: spacing[6],
    right: spacing[4],
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    paddingHorizontal: 0,
  },
  fabText: {
    fontSize: 24,
    lineHeight: 24,
  },
});
