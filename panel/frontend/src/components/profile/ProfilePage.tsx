import React, { useState, useEffect } from 'react';
import {
  User,
  Edit3,
  Camera,
  Mail,
  Calendar,
  MapPin,
  Trophy,
  Target,
  Clock,
  BookOpen,
  Award,
  Star,
  Save,
  X,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { authService } from '../../services/authService';
import { progressService, ProgressStats } from '../../services/progressService';

interface ProfileData {
  name: string;
  username: string;
  email: string;
  bio?: string;
  location?: string;
  avatar?: string;
  birth_date?: string;
}

const ProfilePage: React.FC = () => {
  const { user } = useAuth();
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [stats, setStats] = useState<ProgressStats | null>(null);

  const [profileData, setProfileData] = useState<ProfileData>({
    name: user?.name || '',
    username: user?.username || '',
    email: user?.email || '',
    bio: user?.bio || '',
    location: user?.location || '',
    avatar: user?.avatar || '',
    birth_date: user?.birth_date || ''
  });

  useEffect(() => {
    loadStats();
  }, []);

  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name || '',
        username: user.username || '',
        email: user.email || '',
        bio: user.bio || '',
        location: user.location || '',
        avatar: user.avatar || '',
        birth_date: user.birth_date || ''
      });
    }
  }, [user]);

  const loadStats = async () => {
    try {
      const response = await progressService.getProgressStats();
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  const handleInputChange = (field: keyof ProfileData, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      // Use actual API call to update profile
      await authService.updateProfile({
        name: profileData.name,
        bio: profileData.bio,
        location: profileData.location,
        birth_date: profileData.birth_date
      });

      setSuccess('Profile updated successfully');
      setEditing(false);
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message || 'An error occurred while updating profile');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (user) {
      setProfileData({
        name: user.name || '',
        username: user.username || '',
        email: user.email || '',
        bio: user.bio || '',
        location: user.location || '',
        avatar: user.avatar || '',
        birth_date: user.birth_date || ''
      });
    }
    setEditing(false);
    setError(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Profile</h1>
          <p className="text-dark-300 mt-1">Manage your profile information and view your progress</p>
        </div>

        {!editing ? (
          <button
            onClick={() => setEditing(true)}
            className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <Edit3 size={16} />
            Edit Profile
          </button>
        ) : (
          <div className="flex gap-2">
            <button
              onClick={handleCancel}
              className="flex items-center gap-2 px-4 py-2 bg-dark-700 hover:bg-dark-600 text-white rounded-lg transition-colors"
            >
              <X size={16} />
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save size={16} />
              )}
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        )}
      </div>

      {/* Alerts */}
      {error && (
        <div className="bg-red-600/20 border border-red-600/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="text-red-400" size={20} />
          <span className="text-red-400">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-600/20 border border-green-600/30 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="text-green-400" size={20} />
          <span className="text-green-400">{success}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <h3 className="text-lg font-semibold text-white mb-6">Basic Information</h3>
            
            {/* Avatar */}
            <div className="flex items-center gap-4 mb-6">
              <div className="relative">
                <div className="w-20 h-20 rounded-full bg-dark-700 flex items-center justify-center overflow-hidden">
                  {profileData.avatar ? (
                    <img src={profileData.avatar} alt="Profile" className="w-full h-full object-cover" />
                  ) : (
                    <User className="text-dark-400" size={32} />
                  )}
                </div>
                {editing && (
                  <button className="absolute -bottom-1 -right-1 w-8 h-8 bg-primary-600 hover:bg-primary-700 rounded-full flex items-center justify-center transition-colors">
                    <Camera size={16} className="text-white" />
                  </button>
                )}
              </div>
              <div>
                <h4 className="text-white font-semibold">{profileData.name || 'No name set'}</h4>
                <p className="text-dark-400">@{profileData.username}</p>
                {stats && (
                  <p className="text-primary-400 text-sm">Level {stats.level} • {stats.total_points} points</p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Name</label>
                {editing ? (
                  <input
                    type="text"
                    value={profileData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
                    placeholder="Enter your name"
                  />
                ) : (
                  <p className="text-dark-300">{profileData.name || 'Not set'}</p>
                )}
              </div>

              {/* Username */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Username</label>
                {editing ? (
                  <input
                    type="text"
                    value={profileData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
                    placeholder="Enter your username"
                  />
                ) : (
                  <p className="text-dark-300">@{profileData.username}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Email</label>
                <div className="flex items-center gap-2">
                  <Mail size={16} className="text-dark-400" />
                  {editing ? (
                    <input
                      type="email"
                      value={profileData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="flex-1 bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
                      placeholder="Enter your email"
                    />
                  ) : (
                    <p className="text-dark-300">{profileData.email}</p>
                  )}
                </div>
              </div>

              {/* Bio */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Bio</label>
                {editing ? (
                  <textarea
                    value={profileData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    rows={3}
                    className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
                    placeholder="Tell us about yourself..."
                  />
                ) : (
                  <p className="text-dark-300">{profileData.bio || 'No bio added yet'}</p>
                )}
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Location</label>
                <div className="flex items-center gap-2">
                  <MapPin size={16} className="text-dark-400" />
                  {editing ? (
                    <input
                      type="text"
                      value={profileData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      className="flex-1 bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
                      placeholder="Enter your location"
                    />
                  ) : (
                    <p className="text-dark-300">{profileData.location || 'Not set'}</p>
                  )}
                </div>
              </div>



              {/* Birth Date */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Birth Date</label>
                <div className="flex items-center gap-2">
                  <Calendar size={16} className="text-dark-400" />
                  {editing ? (
                    <input
                      type="date"
                      value={profileData.birth_date}
                      onChange={(e) => handleInputChange('birth_date', e.target.value)}
                      className="flex-1 bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
                    />
                  ) : (
                    <p className="text-dark-300">
                      {profileData.birth_date ? formatDate(profileData.birth_date) : 'Not set'}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Account Info */}
          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <h3 className="text-lg font-semibold text-white mb-4">Account Information</h3>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-dark-400">Member since</span>
                <div className="text-white">{user?.created_at ? formatDate(user.created_at) : 'N/A'}</div>
              </div>
              <div>
                <span className="text-dark-400">Last login</span>
                <div className="text-white">{user?.last_login ? formatDate(user.last_login) : 'N/A'}</div>
              </div>
              <div>
                <span className="text-dark-400">Account status</span>
                <div className="text-green-400">Active</div>
              </div>
              <div>
                <span className="text-dark-400">Email verified</span>
                <div className="text-green-400">Verified</div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Sidebar */}
        <div className="space-y-6">
          {/* Progress Stats */}
          {stats && (
            <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Trophy className="text-yellow-400" size={20} />
                Progress Stats
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Star className="text-primary-400" size={16} />
                    <span className="text-dark-300">Level</span>
                  </div>
                  <span className="text-white font-semibold">{stats.level}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Target className="text-green-400" size={16} />
                    <span className="text-dark-300">Total Points</span>
                  </div>
                  <span className="text-white font-semibold">{stats.total_points}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="text-blue-400" size={16} />
                    <span className="text-dark-300">Study Time</span>
                  </div>
                  <span className="text-white font-semibold">{formatTime(stats.total_study_time)}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <BookOpen className="text-purple-400" size={16} />
                    <span className="text-dark-300">Quizzes</span>
                  </div>
                  <span className="text-white font-semibold">{stats.quizzes_completed}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Award className="text-orange-400" size={16} />
                    <span className="text-dark-300">Avg Score</span>
                  </div>
                  <span className="text-white font-semibold">{stats.average_score}%</span>
                </div>
              </div>
            </div>
          )}

          {/* Level Progress */}
          {stats && (
            <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
              <h3 className="text-lg font-semibold text-white mb-4">Level Progress</h3>
              
              <div className="text-center mb-4">
                <div className="text-3xl font-bold text-primary-400 mb-1">Level {stats.level}</div>
                <div className="text-dark-400 text-sm">
                  {stats.experience_points} / {stats.next_level_points} XP
                </div>
              </div>
              
              <div className="w-full bg-dark-700 rounded-full h-3 mb-2">
                <div 
                  className="bg-gradient-to-r from-primary-500 to-secondary-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${(stats.experience_points / stats.next_level_points) * 100}%` }}
                ></div>
              </div>
              
              <div className="text-center text-dark-400 text-sm">
                {stats.next_level_points - stats.experience_points} XP to next level
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
            <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            
            <div className="space-y-3">
              <button className="w-full text-left px-4 py-3 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors">
                <div className="text-white font-medium">Change Password</div>
                <div className="text-dark-400 text-sm">Update your account password</div>
              </button>
              
              <button className="w-full text-left px-4 py-3 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors">
                <div className="text-white font-medium">Privacy Settings</div>
                <div className="text-dark-400 text-sm">Manage your privacy preferences</div>
              </button>
              
              <button className="w-full text-left px-4 py-3 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors">
                <div className="text-white font-medium">Download Data</div>
                <div className="text-dark-400 text-sm">Export your account data</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
