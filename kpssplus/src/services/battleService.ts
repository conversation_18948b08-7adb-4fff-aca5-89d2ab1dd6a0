import {apiClient} from './api';
import {
  GroupBattle,
  GroupBattleParticipant,
  GroupBattleResult,
  FriendChallenge,
  ChallengeResult,
  PaginatedResponse,
} from '../types';

export class BattleService {
  // Group Battle Methods
  async createGroupBattle(data: {
    name: string;
    description?: string;
    quizId: string;
    maxParticipants: number;
    duration: number;
    isPrivate: boolean;
  }): Promise<GroupBattle> {
    const response = await apiClient.post<GroupBattle>('/battles/group', data);
    return response.data;
  }

  async getGroupBattles(params?: {
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<GroupBattle>> {
    const response = await apiClient.get<PaginatedResponse<GroupBattle>>(
      '/battles/group',
      params,
    );
    return response.data;
  }

  async getGroupBattle(battleId: string): Promise<GroupBattle> {
    const response = await apiClient.get<GroupBattle>(
      `/battles/group/${battleId}`,
    );
    return response.data;
  }

  async joinGroupBattle(
    battleId: string,
    inviteCode?: string,
  ): Promise<GroupBattleParticipant> {
    const response = await apiClient.post<GroupBattleParticipant>(
      `/battles/group/${battleId}/join`,
      {inviteCode},
    );
    return response.data;
  }

  async leaveGroupBattle(battleId: string): Promise<void> {
    await apiClient.post(`/battles/group/${battleId}/leave`);
  }

  async startGroupBattle(battleId: string): Promise<GroupBattle> {
    const response = await apiClient.post<GroupBattle>(
      `/battles/group/${battleId}/start`,
    );
    return response.data;
  }

  async submitGroupBattleResult(
    battleId: string,
    result: {
      score: number;
      correctAnswers: number;
      totalQuestions: number;
      timeSpent: number;
    },
  ): Promise<GroupBattleResult> {
    const response = await apiClient.post<GroupBattleResult>(
      `/battles/group/${battleId}/result`,
      result,
    );
    return response.data;
  }

  async getGroupBattleResults(battleId: string): Promise<GroupBattleResult[]> {
    const response = await apiClient.get<GroupBattleResult[]>(
      `/battles/group/${battleId}/results`,
    );
    return response.data;
  }

  // Friend Challenge Methods
  async sendFriendChallenge(data: {
    challengedId: string;
    quizId: string;
    message?: string;
  }): Promise<FriendChallenge> {
    const response = await apiClient.post<FriendChallenge>(
      '/challenges/friend',
      data,
    );
    return response.data;
  }

  async getFriendChallenges(params?: {
    type?: 'sent' | 'received';
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<FriendChallenge>> {
    const response = await apiClient.get<PaginatedResponse<FriendChallenge>>(
      '/challenges/friend',
      params,
    );
    return response.data;
  }

  async getFriendChallenge(challengeId: string): Promise<FriendChallenge> {
    const response = await apiClient.get<FriendChallenge>(
      `/challenges/friend/${challengeId}`,
    );
    return response.data;
  }

  async respondToFriendChallenge(
    challengeId: string,
    action: 'accept' | 'decline',
  ): Promise<FriendChallenge> {
    const response = await apiClient.post<FriendChallenge>(
      `/challenges/friend/${challengeId}/respond`,
      {action},
    );
    return response.data;
  }

  async submitChallengeResult(
    challengeId: string,
    result: {
      score: number;
      correctAnswers: number;
      totalQuestions: number;
      timeSpent: number;
    },
  ): Promise<ChallengeResult> {
    const response = await apiClient.post<ChallengeResult>(
      `/challenges/friend/${challengeId}/result`,
      result,
    );
    return response.data;
  }

  async getChallengeResults(challengeId: string): Promise<{
    challengerResult?: ChallengeResult;
    challengedResult?: ChallengeResult;
  }> {
    const response = await apiClient.get<{
      challengerResult?: ChallengeResult;
      challengedResult?: ChallengeResult;
    }>(`/challenges/friend/${challengeId}/results`);
    return response.data;
  }

  // Battle Statistics
  async getBattleStats(): Promise<{
    totalGroupBattles: number;
    totalFriendChallenges: number;
    winRate: number;
    averageScore: number;
    bestRank: number;
  }> {
    const response = await apiClient.get<{
      totalGroupBattles: number;
      totalFriendChallenges: number;
      winRate: number;
      averageScore: number;
      bestRank: number;
    }>('/battles/stats');
    return response.data;
  }

  // Real-time updates (mock implementation)
  async subscribeToGroupBattle(
    battleId: string,
    callback: (battle: GroupBattle) => void,
  ): Promise<() => void> {
    // In a real app, this would use WebSocket or Server-Sent Events
    const interval = setInterval(async () => {
      try {
        const battle = await this.getGroupBattle(battleId);
        callback(battle);
      } catch (error) {
        console.error('Error fetching battle updates:', error);
      }
    }, 5000); // Poll every 5 seconds

    return () => clearInterval(interval);
  }

  // Utility methods
  async generateInviteCode(battleId: string): Promise<string> {
    const response = await apiClient.post<{inviteCode: string}>(
      `/battles/group/${battleId}/invite-code`,
    );
    return response.data.inviteCode;
  }

  async joinBattleByInviteCode(inviteCode: string): Promise<GroupBattle> {
    const response = await apiClient.post<GroupBattle>(
      '/battles/group/join-by-code',
      {inviteCode},
    );
    return response.data;
  }

  // Mock data for development
  async getMockGroupBattles(): Promise<GroupBattle[]> {
    // Mock data - in real app this would come from API
    return [
      {
        id: '1',
        name: 'Matematik Kapışması',
        description: 'Matematik konularında kim daha iyi?',
        creatorId: 'user1',
        quizId: 'quiz1',
        maxParticipants: 4,
        currentParticipants: 2,
        status: 'waiting',
        duration: 15,
        isPrivate: false,
        inviteCode: 'MATH123',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        quiz: {
          id: 'quiz1',
          title: 'Matematik Quiz',
          description: 'Temel matematik soruları',
          subject: 'Matematik',
          difficulty: 'medium',
          questionCount: 10,
          timeLimit: 15,
          thumbnailUrl: undefined,
          tags: ['matematik'],
          isPublished: true,
          averageScore: 75,
          attemptCount: 150,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        creator: {
          id: 'user1',
          username: 'ahmet123',
          email: '<EMAIL>',
          firstName: 'Ahmet',
          lastName: 'Yılmaz',
          isVerified: true,
          isActive: true,
          tokens: 50,
          totalScore: 1250,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        participants: [],
      },
    ];
  }

  async getMockFriendChallenges(): Promise<FriendChallenge[]> {
    // Mock data - in real app this would come from API
    return [
      {
        id: '1',
        challengerId: 'user1',
        challengedId: 'user2',
        quizId: 'quiz1',
        status: 'pending',
        message: 'Matematik konusunda beni yenebilir misin?',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        quiz: {
          id: 'quiz1',
          title: 'Matematik Quiz',
          description: 'Temel matematik soruları',
          subject: 'Matematik',
          difficulty: 'medium',
          questionCount: 10,
          timeLimit: 15,
          thumbnailUrl: undefined,
          tags: ['matematik'],
          isPublished: true,
          averageScore: 75,
          attemptCount: 150,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        challenger: {
          id: 'user1',
          username: 'ahmet123',
          email: '<EMAIL>',
          firstName: 'Ahmet',
          lastName: 'Yılmaz',
          isVerified: true,
          isActive: true,
          tokens: 50,
          totalScore: 1250,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        challenged: {
          id: 'user2',
          username: 'mehmet456',
          email: '<EMAIL>',
          firstName: 'Mehmet',
          lastName: 'Kaya',
          isVerified: true,
          isActive: true,
          tokens: 75,
          totalScore: 980,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      },
    ];
  }
}

export const battleService = new BattleService();
