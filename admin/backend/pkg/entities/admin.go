package entities

import "github.com/google/uuid"

type AdminUser struct {
    ID           uuid.UUID `gorm:"type:uuid;default:gen_random_uuid();primaryKey" json:"id"`
    Username     string    `gorm:"uniqueIndex;not null" json:"username"`
    PasswordHash string    `gorm:"not null" json:"-"`
    Role         string    `gorm:"type:varchar(20);not null;default:'admin'" json:"role"` // admin, editor, viewer
}

func (AdminUser) TableName() string { return "admin_users" }

