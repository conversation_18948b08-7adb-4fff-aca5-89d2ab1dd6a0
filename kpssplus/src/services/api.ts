import axios, {AxiosInstance, AxiosResponse, AxiosError} from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiResponse, ApiError, AuthTokens} from '../types';

// API Configuration
const API_BASE_URL = __DEV__
  ? 'http://localhost:8000/api'
  : 'https://your-production-api.com/api';

const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  GUEST_DATA: 'guest_data',
} as const;

class ApiClient {
  private client: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }> = [];

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async config => {
        const token = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      error => Promise.reject(error),
    );

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as any;

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
              this.failedQueue.push({resolve, reject});
            })
              .then(token => {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return this.client(originalRequest);
              })
              .catch(err => {
                return Promise.reject(err);
              });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const refreshToken = await AsyncStorage.getItem(
              STORAGE_KEYS.REFRESH_TOKEN,
            );
            if (!refreshToken) {
              throw new Error('No refresh token available');
            }

            const response = await this.client.post('/auth/refresh', {
              refreshToken,
            });

            const {accessToken} = response.data.data;
            await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);

            this.processQueue(null, accessToken);
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;

            return this.client(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            await this.clearAuthData();
            // Redirect to login or show auth modal
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(error);
      },
    );
  }

  private processQueue(error: any, token: string | null) {
    this.failedQueue.forEach(({resolve, reject}) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }

  private async clearAuthData() {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.ACCESS_TOKEN,
      STORAGE_KEYS.REFRESH_TOKEN,
      STORAGE_KEYS.USER_DATA,
    ]);
  }

  // Generic API methods
  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.get(url, {params});
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.post(url, data);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.put(url, data);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.delete(url);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private handleError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      return {
        error: error.response.data?.error || 'Server error occurred',
        status: error.response.status,
      };
    } else if (error.request) {
      // Network error
      return {
        error: 'Network error. Please check your connection.',
        status: 0,
      };
    } else {
      // Other error
      return {
        error: error.message || 'An unexpected error occurred',
        status: -1,
      };
    }
  }

  // Auth token management
  async setAuthTokens(tokens: AuthTokens) {
    await AsyncStorage.multiSet([
      [STORAGE_KEYS.ACCESS_TOKEN, tokens.accessToken],
      [STORAGE_KEYS.REFRESH_TOKEN, tokens.refreshToken],
    ]);
  }

  async getAuthTokens(): Promise<AuthTokens | null> {
    try {
      const [accessToken, refreshToken] = await AsyncStorage.multiGet([
        STORAGE_KEYS.ACCESS_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
      ]);

      if (accessToken[1] && refreshToken[1]) {
        return {
          accessToken: accessToken[1],
          refreshToken: refreshToken[1],
          expiresIn: 0, // Will be handled by interceptor
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting auth tokens:', error);
      return null;
    }
  }

  async clearAuthTokens() {
    await this.clearAuthData();
  }

  // Guest mode data management
  async getGuestData() {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.GUEST_DATA);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error getting guest data:', error);
      return null;
    }
  }

  async setGuestData(data: any) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.GUEST_DATA, JSON.stringify(data));
    } catch (error) {
      console.error('Error setting guest data:', error);
    }
  }

  async clearGuestData() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.GUEST_DATA);
    } catch (error) {
      console.error('Error clearing guest data:', error);
    }
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export {STORAGE_KEYS};
