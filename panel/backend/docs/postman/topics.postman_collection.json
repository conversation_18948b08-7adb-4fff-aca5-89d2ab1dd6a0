{"info": {"name": "KPSS Plus - Topics", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "6b0e8b8f-7f44-4d24-9a2e-19a9735deabc"}, "item": [{"name": "List Topics (tree)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/topics?tree=true", "host": ["{{base_url}}"], "path": ["api", "v1", "topics"], "query": [{"key": "tree", "value": "true"}]}}}, {"name": "Get Topic", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/topics/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "topics", ":id"]}}}, {"name": "Create Topic", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{bearer_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Vatandaşlı<PERSON>\",\n  \"subject\": \"GYGK\",\n  \"body\": \"<PERSON><PERSON> anlatımı...\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/topics", "host": ["{{base_url}}"], "path": ["api", "v1", "topics"]}}}, {"name": "Update Topic", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{bearer_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"<PERSON><PERSON> Başlık\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/topics/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "topics", ":id"]}}}, {"name": "Delete Topic", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{bearer_token}}"}], "url": {"raw": "{{base_url}}/api/v1/topics/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "topics", ":id"]}}}, {"name": "Update Topic Progress", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{bearer_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"percentage\": 100\n}"}, "url": {"raw": "{{base_url}}/api/v1/topics/:id/progress", "host": ["{{base_url}}"], "path": ["api", "v1", "topics", ":id", "progress"]}}}, {"name": "Get Topic Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{bearer_token}}"}], "url": {"raw": "{{base_url}}/api/v1/topics/:id/progress", "host": ["{{base_url}}"], "path": ["api", "v1", "topics", ":id", "progress"]}}}], "variable": [{"key": "base_url", "value": "http://localhost:8000"}, {"key": "bearer_token", "value": ""}]}