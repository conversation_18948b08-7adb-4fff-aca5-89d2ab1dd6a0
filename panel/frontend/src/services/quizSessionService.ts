import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';
import { QuizSessionResponse, SubmitAnswerRequest, AnswerResponse, QuizResultResponse } from '../types/quizSession';

export const quizSessionService = {
  async start(quizId: string): Promise<ApiResponse<QuizSessionResponse>> {
    return http.post(endpoints.quiz.start(quizId));
  },

  async startQuizSession(quizId: string): Promise<ApiResponse<QuizSessionResponse>> {
    return this.start(quizId);
  },

  async submit(sessionId: string, payload: SubmitAnswerRequest): Promise<ApiResponse<AnswerResponse>> {
    return http.post(endpoints.quiz.submitAnswer(sessionId), payload);
  },

  async submitAnswer(sessionId: string, payload: SubmitAnswerRequest): Promise<ApiResponse<AnswerResponse>> {
    return this.submit(sessionId, payload);
  },

  async finish(sessionId: string): Promise<ApiResponse<QuizResultResponse>> {
    return http.post(endpoints.quiz.finish(sessionId));
  },

  async finishQuizSession(sessionId: string): Promise<ApiResponse<QuizResultResponse>> {
    return this.finish(sessionId);
  },

  async getResult(resultId: string): Promise<ApiResponse<QuizResultResponse>> {
    return http.get(endpoints.quiz.results(resultId));
  },

  async listResults(params?: { page?: number; limit?: number; quiz_id?: string; sort_by?: string; sort_desc?: boolean; min_score?: number; max_score?: number }): Promise<ApiResponse<any>> {
    return http.get(endpoints.quiz.userResults, { params });
  },
};

export default quizSessionService;

