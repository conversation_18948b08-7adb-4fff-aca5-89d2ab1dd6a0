package database

import (
	"fmt"
	"log"
	"sync"

	"github.com/nocytech/kpss-plus-admin/pkg/config"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db         *gorm.DB
	err        error
	clientOnce sync.Once
)

func InitDB(dbc config.Database) {
	clientOnce.Do(func() {
		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul", dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name)
		db, err = gorm.Open(
			postgres.New(postgres.Config{DSN: dsn, PreferSimpleProtocol: true}),
		)
		if err != nil {
			panic(err)
		}
        // Auto-migrate admin-specific tables. Keep scope narrow to avoid
        // altering main app schema: create tables used exclusively by admin UI.
        _ = db.AutoMigrate(
            &entities.AdminUser{},
            &entities.Area{},
            &entities.Subject{},
        )
		db.Exec("SET TIME ZONE 'Europe/Istanbul';")
	})
}

func DB() *gorm.DB {
	if db == nil {
		log.Panic("database not initialized; call InitDB first")
	}
	return db
}
