import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import {
  BookOpen,
  Video,
  FileText,
  Monitor,
  Search,
  Star,
  Heart,
  Bookmark,
  Clock,
  Eye,
  TrendingUp,
  Play
} from 'lucide-react';
import { Content, ContentFilters, contentService } from '../../services/contentService';
import { useToast } from '../common/Toast';

const ContentPage: React.FC = () => {
  const { showError, showSuccess } = useToast();
  const [contents, setContents] = useState<Content[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ContentFilters>({});
  const [activeTab, setActiveTab] = useState<'all' | 'featured' | 'popular' | 'library'>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const contentTypes = [
    { key: 'article', label: 'Articles', icon: FileText },
    { key: 'video', label: 'Videos', icon: Video },
    { key: 'document', label: 'Documents', icon: BookOpen },
    { key: 'interactive', label: 'Interactive', icon: Monitor }
  ];

  const subjects = [
    'Mathematics', 'Physics', 'Chemistry', 'Biology', 'History', 
    'Geography', 'Literature', 'Philosophy', 'Economics', 'Law'
  ];

  const difficulties = [
    { key: 'easy', label: 'Easy', color: 'text-green-400 bg-green-400/10' },
    { key: 'medium', label: 'Medium', color: 'text-yellow-400 bg-yellow-400/10' },
    { key: 'hard', label: 'Hard', color: 'text-red-400 bg-red-400/10' }
  ];

  const loadContents = useCallback(async (reset = false) => {
    try {
      setLoading(true);
      setError(null);

      const currentPage = reset ? 1 : page;
      let response: any;

      // Use actual API call instead of mock data
      if (searchQuery.trim()) {
        response = await contentService.searchContents(searchQuery, {
          ...filters,
          page: currentPage,
          limit: 12
        });
      } else {
        // Call different endpoints based on active tab
        switch (activeTab) {
          case 'featured':
            response = await contentService.getFeaturedContents({
              page: currentPage,
              limit: 12
            });
            break;
          case 'popular':
            response = await contentService.getPopularContents({
              page: currentPage,
              limit: 12
            });
            break;
          case 'library':
            response = await contentService.getLibraryContents({
              page: currentPage,
              limit: 12
            });
            break;
          default:
            response = await contentService.getContents({
              ...filters,
              page: currentPage,
              limit: 12
            });
        }
      }

      if (response.success && response.data) {
        if (reset) {
          setContents(response.data.contents);
          setPage(1);
        } else {
          setContents(prev => [...prev, ...response.data.contents]);
        }
        setHasMore(response.data.has_more);

        if (response.data.contents.length === 0) {
          showError('No content found', 'Backend connection issue or no data available');
        }
      } else {
        const errorMsg = 'Failed to load contents - Backend not responding';
        setError(errorMsg);
        showError('Connection Error', errorMsg);
      }
    } catch (err) {
      const errorMsg = 'Backend service unavailable';
      setError(errorMsg);
      showError('Service Error', errorMsg);
    } finally {
      setLoading(false);
    }
  }, [activeTab, searchQuery, filters, showError, page]);

  useEffect(() => {
    loadContents(true);
  }, [loadContents]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadContents(true);
  };

  const handleTabChange = (tab: typeof activeTab) => {
    setActiveTab(tab);
    setPage(1);
    loadContents(true); // Reload content for the new tab
  };

  const handleFilterChange = (key: keyof ContentFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
    loadContents();
  };



  const handleBookmark = async (contentId: string, isBookmarked: boolean) => {
    try {
      if (isBookmarked) {
        await contentService.removeBookmark(contentId);
        showSuccess('Removed from bookmarks');
      } else {
        await contentService.bookmarkContent(contentId);
        showSuccess('Added to bookmarks');
      }

      // Update local state
      setContents(prev => prev.map(content =>
        content.id === contentId
          ? { ...content, is_bookmarked: !isBookmarked }
          : content
      ));
    } catch (err) {
      showError('Failed to update bookmark');
    }
  };

  const handleLike = async (contentId: string, isLiked: boolean) => {
    try {
      if (isLiked) {
        await contentService.unlikeContent(contentId);
      } else {
        await contentService.likeContent(contentId);
      }

      // Update local state
      setContents(prev => prev.map(content =>
        content.id === contentId
          ? {
              ...content,
              is_favorite: !isLiked,
              like_count: isLiked ? content.like_count - 1 : content.like_count + 1
            }
          : content
      ));
    } catch (err) {
      showError('Failed to update like');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Study Content</h1>
          <p className="text-dark-300 mt-1">Explore articles, videos, and interactive materials</p>
        </div>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex gap-2 max-w-md w-full lg:w-auto">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400" size={20} />
            <input
              type="text"
              placeholder="Search content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:border-primary-500"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <Search size={20} />
          </button>
        </form>
      </div>

      {/* Filters */}
      <div className="bg-dark-800 rounded-xl p-4 border border-dark-700">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Content Type Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Content Type</label>
            <select
              value={filters.content_type || ''}
              onChange={(e) => handleFilterChange('content_type', e.target.value || undefined)}
              className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
            >
              <option value="">All Types</option>
              {contentTypes.map(type => (
                <option key={type.key} value={type.key}>{type.label}</option>
              ))}
            </select>
          </div>

          {/* Subject Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Subject</label>
            <select
              value={filters.subject || ''}
              onChange={(e) => handleFilterChange('subject', e.target.value || undefined)}
              className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
            >
              <option value="">All Subjects</option>
              {subjects.map(subject => (
                <option key={subject} value={subject}>{subject}</option>
              ))}
            </select>
          </div>

          {/* Difficulty Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Difficulty</label>
            <select
              value={filters.difficulty || ''}
              onChange={(e) => handleFilterChange('difficulty', e.target.value || undefined)}
              className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
            >
              <option value="">All Levels</option>
              {difficulties.map(diff => (
                <option key={diff.key} value={diff.key}>{diff.label}</option>
              ))}
            </select>
          </div>

          {/* Premium Filter */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Access</label>
            <select
              value={filters.is_premium === undefined ? '' : filters.is_premium ? 'premium' : 'free'}
              onChange={(e) => handleFilterChange('is_premium', 
                e.target.value === '' ? undefined : e.target.value === 'premium'
              )}
              className="w-full bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
            >
              <option value="">All Content</option>
              <option value="free">Free</option>
              <option value="premium">Premium</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
        {[
          { key: 'all', label: 'All Content', icon: BookOpen },
          { key: 'featured', label: 'Featured', icon: Star },
          { key: 'popular', label: 'Popular', icon: TrendingUp },
          { key: 'library', label: 'My Library', icon: Bookmark }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => handleTabChange(key as typeof activeTab)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === key
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:text-white hover:bg-dark-700'
            }`}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* Content Grid */}
      {error ? (
        <div className="text-center py-12">
          <div className="text-red-400 mb-2">Error loading content</div>
          <p className="text-dark-400">{error}</p>
          <button
            onClick={() => loadContents(true)}
            className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {contents.map((content) => (
              <ContentCard
                key={content.id}
                content={content}
                onBookmark={handleBookmark}
                onLike={handleLike}
              />
            ))}
          </div>

          {loading && (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
            </div>
          )}

          {!loading && contents.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-dark-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No content found</h3>
              <p className="text-dark-400">Try adjusting your search or filters</p>
            </div>
          )}

          {!loading && hasMore && contents.length > 0 && (
            <div className="text-center">
              <button
                onClick={handleLoadMore}
                className="px-6 py-2 bg-dark-700 hover:bg-dark-600 text-white rounded-lg transition-colors"
              >
                Load More
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// Content Card Component
interface ContentCardProps {
  content: Content;
  onBookmark: (contentId: string, isBookmarked: boolean) => void;
  onLike: (contentId: string, isLiked: boolean) => void;
}

const ContentCard: React.FC<ContentCardProps> = ({ content, onBookmark, onLike }) => {
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return Video;
      case 'document': return BookOpen;
      case 'interactive': return Monitor;
      default: return FileText;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 bg-green-400/10';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10';
      case 'hard': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const Icon = getContentTypeIcon(content.content_type);

  return (
    <div className="bg-dark-800 rounded-xl border border-dark-700 hover:border-primary-500/50 transition-all duration-200 group overflow-hidden">
      {/* Thumbnail */}
      {content.thumbnail_url && (
        <div className="relative h-48 overflow-hidden">
          <img
            src={content.thumbnail_url}
            alt={content.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
          <div className="absolute top-3 left-3">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(content.difficulty)}`}>
              {content.difficulty}
            </span>
          </div>
          <div className="absolute top-3 right-3 flex gap-2">
            {content.is_featured && (
              <Star className="text-yellow-400" size={16} />
            )}
            {content.is_premium && (
              <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full text-xs font-bold">
                PRO
              </div>
            )}
          </div>
          <div className="absolute bottom-3 left-3 flex items-center gap-2 text-white text-sm">
            <Icon size={16} />
            <span>{content.content_type}</span>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-lg font-semibold text-white group-hover:text-primary-400 transition-colors line-clamp-2">
            {content.title}
          </h3>
        </div>

        <p className="text-dark-300 text-sm mb-4 line-clamp-2">{content.description}</p>

        {/* Stats */}
        <div className="flex items-center gap-4 text-sm text-dark-400 mb-4">
          <div className="flex items-center gap-1">
            <Clock size={16} />
            <span>{formatDuration(content.duration)}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye size={16} />
            <span>{content.view_count}</span>
          </div>
          <div className="flex items-center gap-1">
            <Heart size={16} />
            <span>{content.like_count}</span>
          </div>
        </div>

        {/* Tags */}
        <div className="flex items-center gap-2 mb-4">
          <span className="px-2 py-1 bg-dark-700 text-dark-300 rounded-full text-xs">
            {content.subject}
          </span>
          {content.tags.slice(0, 2).map((tag, index) => (
            <span key={index} className="px-2 py-1 bg-dark-700 text-dark-300 rounded-full text-xs">
              {tag}
            </span>
          ))}
        </div>

        {/* Progress */}
        {content.progress_percentage !== undefined && content.progress_percentage > 0 && (
          <div className="mb-4">
            <div className="flex items-center justify-between text-sm mb-1">
              <span className="text-dark-400">Progress</span>
              <span className="text-white">{content.progress_percentage}%</span>
            </div>
            <div className="w-full bg-dark-700 rounded-full h-2">
              <div 
                className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${content.progress_percentage}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {content.author_avatar && (
              <img
                src={content.author_avatar}
                alt={content.author}
                className="w-6 h-6 rounded-full"
              />
            )}
            <span className="text-sm text-dark-400">{content.author}</span>
          </div>

          <div className="flex items-center gap-2">
            {/* Bookmark Button */}
            <button
              onClick={(e) => {
                e.preventDefault();
                onBookmark(content.id, content.is_bookmarked || false);
              }}
              className={`p-2 rounded-lg transition-colors ${
                content.is_bookmarked
                  ? 'bg-primary-600 text-white'
                  : 'bg-dark-700 text-dark-400 hover:text-white hover:bg-dark-600'
              }`}
            >
              <Bookmark size={16} />
            </button>

            {/* Like Button */}
            <button
              onClick={(e) => {
                e.preventDefault();
                onLike(content.id, content.is_favorite || false);
              }}
              className={`p-2 rounded-lg transition-colors ${
                content.is_favorite
                  ? 'bg-red-600 text-white'
                  : 'bg-dark-700 text-dark-400 hover:text-white hover:bg-dark-600'
              }`}
            >
              <Heart size={16} />
            </button>

            {/* View Content Button */}
            <Link
              to={`/content/${content.id}`}
              className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors text-sm font-medium"
            >
              <Play size={16} />
              {content.content_type === 'video' ? 'Watch' : 'Read'}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentPage;
