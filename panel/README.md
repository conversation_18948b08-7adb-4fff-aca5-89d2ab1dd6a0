# KPSS Plus

KPSS sınav hazırlık platformu - Taksonomi tabanlı içerik yönetim sistemi

## Proje <PERSON>

```
kpss-plus/
├── admin/                  # Admin Panel
│   ├── backend/           # Go Backend API
│   └── frontend/          # React Frontend
├── backend/               # Ana Backend API (Gelecek)
├── frontend/              # Ana Frontend (Gelecek)
├── database/              # Database init scripts
└── docker-compose.yml     # Docker orchestration
```

## Teknolojiler

### Admin Backend
- **Go 1.21** - Backend dili
- **Gin** - Web framework
- **GORM** - ORM
- **PostgreSQL** - Veritabanı
- **JWT** - Authentication

### Admin Frontend
- **React 18** - Frontend framework
- **TypeScript** - Type safety
- **React Router** - Routing
- **Axios** - HTTP client
- **Headless UI** - UI components

## Hızlı Başlangıç

### Docker ile Çalıştırma

```bash
# Tüm servisleri başlat
docker-compose up -d

# Logları izle
docker-compose logs -f

# Servisleri durdur
docker-compose down
```

### Servisler

- **Admin Frontend**: http://localhost:8300
- **Admin Backend**: http://localhost:8100
- **PostgreSQL**: localhost:5432

### Giriş Bilgileri

- **Kullanıcı**: admin
- **Şifre**: admin123

## Geliştirme

### Admin Backend

```bash
cd admin/backend
go mod download
go run cmd/admin/main.go
```

### Admin Frontend

```bash
cd admin/frontend
npm install
npm start
```

## API Endpoints

### Authentication
- `POST /admin/api/v1/auth/login` - Giriş

### Areas (Alanlar)
- `GET /admin/api/v1/areas` - Tüm alanlar
- `POST /admin/api/v1/areas` - Alan oluştur
- `PUT /admin/api/v1/areas/:id` - Alan güncelle
- `DELETE /admin/api/v1/areas/:id` - Alan sil

### Subjects (Dersler)
- `GET /admin/api/v1/subjects` - Tüm dersler
- `POST /admin/api/v1/subjects` - Ders oluştur
- `PUT /admin/api/v1/subjects/:id` - Ders güncelle
- `DELETE /admin/api/v1/subjects/:id` - Ders sil

### Topics (Konular)
- `GET /admin/api/v1/topics` - Tüm konular
- `POST /admin/api/v1/topics` - Konu oluştur
- `PUT /admin/api/v1/topics/:id` - Konu güncelle
- `DELETE /admin/api/v1/topics/:id` - Konu sil

### Contents (İçerikler)
- `GET /admin/api/v1/contents` - Tüm içerikler
- `POST /admin/api/v1/contents` - İçerik oluştur
- `PUT /admin/api/v1/contents/:id` - İçerik güncelle
- `DELETE /admin/api/v1/contents/:id` - İçerik sil

### Questions (Sorular)
- `GET /admin/api/v1/questions` - Tüm sorular
- `POST /admin/api/v1/questions` - Soru oluştur
- `PUT /admin/api/v1/questions/:id` - Soru güncelle
- `DELETE /admin/api/v1/questions/:id` - Soru sil

## Taksonomi Yapısı

```
Area (Alan)
├── Subject (Ders)
    ├── Topic (Konu)
        ├── Content (İçerik)
            └── Question (Soru)
        └── Sub-Topic (Alt Konu)
```

### Örnek Taksonomi

```
Genel Yetenek
├── Türkçe
│   ├── Sözcük Bilgisi
│   ├── Cümle Bilgisi
│   └── Paragraf
├── Matematik
│   ├── Temel Matematik
│   ├── Geometri
│   └── Veri Analizi

Genel Kültür
├── Tarih
│   ├── Türk Tarihi
│   └── Dünya Tarihi
├── Coğrafya
│   ├── Fiziki Coğrafya
│   └── Beşeri Coğrafya
```

## Environment Variables

### Backend
- `APP_NAME` - Uygulama adı
- `APP_PORT` - Port (default: 8100)
- `APP_HOST` - Host (default: 0.0.0.0)
- `APP_JWT_SECRET` - JWT secret key
- `APP_ADMIN_USER` - Admin kullanıcı adı
- `APP_ADMIN_PASS` - Admin şifresi
- `DB_HOST` - Database host
- `DB_PORT` - Database port
- `DB_USER` - Database kullanıcı
- `DB_PASS` - Database şifre
- `DB_NAME` - Database adı
- `CORS_ORIGINS` - CORS allowed origins

### Frontend
- `REACT_APP_API_URL` - Backend API URL

## Lisans

MIT License
