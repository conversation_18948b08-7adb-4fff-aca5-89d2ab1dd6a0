package server

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path"
	"strings"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/app/api/routes"
	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/database"
	"github.com/kpss-plus-backend/pkg/domains/analytics"
	"github.com/kpss-plus-backend/pkg/domains/auth"
	"github.com/kpss-plus-backend/pkg/domains/badge"
	"github.com/kpss-plus-backend/pkg/domains/content"
	"github.com/kpss-plus-backend/pkg/domains/library"
	"github.com/kpss-plus-backend/pkg/domains/notification"
	"github.com/kpss-plus-backend/pkg/domains/preferences"
	"github.com/kpss-plus-backend/pkg/domains/progress"
	"github.com/kpss-plus-backend/pkg/domains/quiz"
	"github.com/kpss-plus-backend/pkg/domains/social"
	"github.com/kpss-plus-backend/pkg/domains/timeline"
	"github.com/kpss-plus-backend/pkg/domains/topic"
	"github.com/kpss-plus-backend/pkg/domains/version"
	"github.com/kpss-plus-backend/pkg/middleware"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/swaggo/swag/example/basic/docs"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())

	//app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()

	// -----> Routes Start
	api := app.Group("/api/v1")

	// Auth routes
	auth_repo := auth.NewRepo(db)
	auth_service := auth.NewService(auth_repo)
	routes.AuthRoutes(api, auth_service)
	routes.UserRoutes(api, auth_service)

	// Content routes
	content_repo := content.NewRepo(db)
	content_service := content.NewService(content_repo)
	routes.ContentRoutes(api, content_service)

	// Topic routes
	topic_repo := topic.NewRepo(db)
	topic_service := topic.NewService(topic_repo)
	routes.TopicRoutes(api, topic_service)

	// Progress routes
	progress_repo := progress.NewRepo(db)
	progress_service := progress.NewService(progress_repo)
	routes.ProgressRoutes(api, progress_service)

	// Notification routes
	notification_repo := notification.NewRepo(db)
	notification_service := notification.NewService(notification_repo)
	routes.NotificationRoutes(api, notification_service)

	// Quiz routes
	quiz_repo := quiz.NewRepo(db)
	quiz_service := quiz.NewService(quiz_repo)
	routes.QuizRoutes(api, quiz_service)

	// Library routes
	library_repo := library.NewRepo(db)
	library_service := library.NewService(library_repo)
	routes.LibraryRoutes(api, library_service)

	// Badge routes
	badge_repo := badge.NewRepo(db)
	badge_service := badge.NewService(badge_repo)
	routes.BadgeRoutes(api, badge_service)

	// Social routes
	social_repo := social.NewRepo(db)
	social_service := social.NewService(social_repo)
	routes.SocialRoutes(api, social_service, badge_service)

	// Timeline routes
	timeline_repo := timeline.NewRepo(db)
	timeline_service := timeline.NewService(timeline_repo)
	routes.TimelineRoutes(api, timeline_service)

	version_repo := version.NewRepo(db)
	version_service := version.NewService(version_repo)
	routes.VersionRoutes(api, version_service)

	// Analytics routes
	analytics_repo := analytics.NewRepo(db)
	analytics_service := analytics.NewService(analytics_repo)
	routes.AnalyticsRoutes(api, analytics_service)

	// Preferences routes
	pref_repo := preferences.NewRepo(db)
	pref_service := preferences.NewService(pref_repo)
	routes.PreferencesRoutes(api, pref_service)

	// Routes End <-----

	// Static file routes (must come after API routes)
	// Map common CRA/Vite asset paths explicitly
	app.Static("/static", "./dist/static")
	app.Static("/assets", "./dist/assets")
	app.StaticFile("/robots.txt", "./dist/robots.txt")
	app.StaticFile("/favicon.ico", "./dist/favicon.ico")
	app.StaticFile("/manifest.json", "./dist/manifest.json")
	app.StaticFile("/asset-manifest.json", "./dist/asset-manifest.json")
	app.StaticFile("/logo192.png", "./dist/logo192.png")
	app.StaticFile("/logo512.png", "./dist/logo512.png")
	app.StaticFile("/service-worker.js", "./dist/service-worker.js")

	// Documentation routes
	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "docs/index.html")
	})

	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "kpss-plus-dev"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "kpss-plus-dev"
	}

	docs.SwaggerInfo.Host = config.InitConfig().App.BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")
	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	// Serve SPA index for root
	app.GET("/", func(c *gin.Context) {
		c.File("./dist/index.html")
	})
	// SPA fallback for client-side routes
	app.NoRoute(func(c *gin.Context) {
		p := c.Request.URL.Path
		// let API/docs/metrics 404 as usual
		if strings.HasPrefix(p, "/api/") || strings.HasPrefix(p, "/docs") || strings.HasPrefix(p, "/metrics") {
			c.Status(http.StatusNotFound)
			return
		}
		// If request looks like an asset (has extension), only serve if it exists
		if ext := path.Ext(p); ext != "" {
			full := path.Join("./dist", p)
			if _, err := os.Stat(full); err == nil {
				c.File(full)
				return
			}
			c.Status(http.StatusNotFound)
			return
		}
		// For SPA routing, serve index.html
		c.File("./dist/index.html")
	})
	app.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "Test route works"})
	})

	fmt.Println("Server is running on port " + appc.Port)
	app.Run(net.JoinHostPort(appc.Host, appc.Port))
}
