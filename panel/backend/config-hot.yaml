app:
  name: kpss-plus
  port: 8000
  host:
  jwt_issuer: "kpss-plus"
  jwt_secret: "secret"
  client_id: xxx
  onesignal_api_key: 123456
  force_update_key: xxx
redis:
  host: xxx
  port: 6379
  pass: xxx
database:
  host: kpss-plus-db
  port: 5432
  user: kpss-plus-user
  pass: kpss-plus-pass
  name: kpss-plus
cloudinary:
  name: xxx
  api_key: xxx
  api_secret: xxx
  api_folder: xxx
allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000