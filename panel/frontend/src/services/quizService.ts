import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';
import { 
  Quiz, 
  QuizListResponse, 
  QuizFilters, 
  CreateQuizRequest,
  Question,
  QuizLeaderboard,
  QuizStatistics
} from '../types/quiz';

export const quizService = {
  // Get quiz list with filters
  async getQuizzes(params?: QuizFilters & { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.list, { params });
  },

  // Get quiz by ID
  async getQuiz(id: string): Promise<ApiResponse<Quiz>> {
    return http.get(endpoints.quiz.detail(id));
  },

  // Search quizzes
  async searchQuizzes(query: string, filters?: QuizFilters & { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.search, {
      params: { search: query, ...filters }
    });
  },

  // Get quizzes by subject
  async getQuizzesBySubject(subject: string, params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.bySubject(subject), { params });
  },

  // Get popular quizzes
  async getPopularQuizzes(params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.popular, { params });
  },

  // Get user's quizzes
  async getUserQuizzes(params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get('/quizzes/user', { params });
  },

  // Get featured quizzes
  async getFeaturedQuizzes(params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.featured, { params });
  },

  // Get quiz questions
  async getQuizQuestions(id: string): Promise<ApiResponse<Question[]>> {
    return http.get(endpoints.quiz.questions(id));
  },

  // Get quiz statistics
  async getQuizStatistics(id: string): Promise<ApiResponse<QuizStatistics>> {
    return http.get(endpoints.quiz.statistics(id));
  },

  // Get quiz leaderboard
  async getQuizLeaderboard(id: string, params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizLeaderboard>> {
    return http.get(endpoints.quiz.leaderboard(id), { params });
  },

  // Create quiz
  async createQuiz(data: CreateQuizRequest): Promise<ApiResponse<Quiz>> {
    return http.post(endpoints.quiz.create, data);
  },

  // Update quiz
  async updateQuiz(id: string, data: Partial<CreateQuizRequest>): Promise<ApiResponse<Quiz>> {
    return http.put(endpoints.quiz.update(id), data);
  },

  // Delete quiz
  async deleteQuiz(id: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.quiz.delete(id));
  },

  // Add to favorites
  async addToFavorites(id: string): Promise<ApiResponse<void>> {
    return http.post(endpoints.quiz.addToFavorites(id));
  },

  // Remove from favorites
  async removeFromFavorites(id: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.quiz.removeFromFavorites(id));
  },

  // Get user's favorite quizzes
  async getFavoriteQuizzes(params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.favorites, { params });
  },

  // Share quiz
  async shareQuiz(id: string, data: { emails?: string[]; message?: string }): Promise<ApiResponse<void>> {
    return http.post(endpoints.quiz.share(id), data);
  },

  // Get shared quizzes
  async getSharedQuizzes(params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.shared, { params });
  },

  // Get quizzes by type
  async getQuizzesByType(type: string, params?: { page?: number; limit?: number }): Promise<ApiResponse<QuizListResponse>> {
    return http.get(endpoints.quiz.byType(type), { params });
  },

  // Get quiz stats
  async getQuizStats(id: string): Promise<ApiResponse<any>> {
    return http.get(endpoints.quiz.stats(id));
  }
};

export default quizService;
