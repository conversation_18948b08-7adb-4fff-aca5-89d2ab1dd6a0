package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/kpss-plus-admin/pkg/database"
)

func AnalyticsRoutes(r *gin.RouterGroup) {
	analytics := r.Group("/analytics")
	{
		analytics.GET("", GetAnalytics())
		analytics.GET("/users", GetUserAnalytics())
		analytics.GET("/content", GetContentAnalytics())
	}
}

func GetAnalytics() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.DB()
		
		var totalUsers int64
		var totalContent int64
		
		// Count total users
		db.Table("admin_users").Count(&totalUsers)
		
		// Count total content
		db.Table("contents").Count(&totalContent)
		
		analytics := map[string]interface{}{
			"total_users":   totalUsers,
			"total_content": totalContent,
		}
		
		c.<PERSON>(http.StatusOK, analytics)
	}
}

func GetUserAnalytics() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.DB()
		
		var activeUsers int64
		var newUsersToday int64
		
		// Count active users (last 7 days)
		db.Table("admin_users").Where("updated_at > NOW() - INTERVAL 7 DAY").Count(&activeUsers)
		
		// Count new users today
		db.Table("admin_users").Where("created_at >= CURDATE()").Count(&newUsersToday)
		
		userAnalytics := map[string]interface{}{
			"active_users":      activeUsers,
			"new_users_today":   newUsersToday,
		}
		
		c.JSON(http.StatusOK, userAnalytics)
	}
}

func GetContentAnalytics() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.DB()
		
		var publishedContent int64
		var draftContent int64
		
		// Count published content
		db.Table("contents").Where("status = ?", "published").Count(&publishedContent)
		
		// Count draft content
		db.Table("contents").Where("status = ?", "draft").Count(&draftContent)
		
		contentAnalytics := map[string]interface{}{
			"published_content": publishedContent,
			"draft_content":     draftContent,
		}
		
		c.JSON(http.StatusOK, contentAnalytics)
	}
}
