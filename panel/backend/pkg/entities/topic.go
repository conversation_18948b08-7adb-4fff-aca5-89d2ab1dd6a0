package entities

import (
	"time"

	"github.com/google/uuid"
)

// Topic represents a hierarchical study topic (ana başl<PERSON>k / alt başlık)
type Topic struct {
	Base
	Title       string     `json:"title" gorm:"not null"`
	Description *string    `json:"description"`
	Body        *string    `json:"body" gorm:"type:text"` // Konu anlatımı metni
	Subject     *string    `json:"subject"`
	ParentID    *uuid.UUID `json:"parent_id" gorm:"type:uuid;index"`
	Order       int        `json:"order" gorm:"default:0"`
	IsPublished bool       `json:"is_published" gorm:"default:true"`
}

func (Topic) TableName() string { return "topics" }

// UserTopicProgress tracks a user's completion/progress on a topic
type UserTopicProgress struct {
	Base
	UserID      *uuid.UUID `json:"user_id"`  // Optional user reference
	TopicID     *uuid.UUID `json:"topic_id"` // Optional topic reference
	Percentage  float64    `json:"percentage"`
	IsCompleted bool       `json:"is_completed"`
	CompletedAt *time.Time `json:"completed_at"`
}

func (UserTopicProgress) TableName() string { return "user_topic_progress" }
