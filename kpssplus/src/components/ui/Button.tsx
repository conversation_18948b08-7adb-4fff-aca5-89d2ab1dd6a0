import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import {colors, typography, layout} from '../../theme';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false,
}) => {
  const buttonStyle = [
    styles.base,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textStyleCombined = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}>
      {loading ? (
        <ActivityIndicator
          color={variant === 'primary' ? colors.white : colors.primary}
          size="small"
        />
      ) : (
        <Text style={textStyleCombined}>{title}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: layout.buttonRadius,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  fullWidth: {
    width: '100%',
  },

  // Variants
  primary: {
    backgroundColor: colors.button.primary,
  },
  secondary: {
    backgroundColor: colors.button.secondary,
    borderWidth: 1,
    borderColor: colors.border,
  },
  ghost: {
    backgroundColor: colors.button.ghost,
  },
  outline: {
    backgroundColor: colors.button.ghost,
    borderWidth: 1,
    borderColor: colors.primary,
  },

  // Sizes
  small: {
    paddingHorizontal: layout.buttonPaddingSmall,
    paddingVertical: layout.buttonPaddingSmall,
    minHeight: 36,
  },
  medium: {
    paddingHorizontal: layout.buttonPadding,
    paddingVertical: layout.buttonPadding,
    minHeight: 48,
  },
  large: {
    paddingHorizontal: layout.buttonPaddingLarge,
    paddingVertical: layout.buttonPaddingLarge,
    minHeight: 56,
  },

  // Text styles
  text: {
    ...typography.button,
  },
  primaryText: {
    color: colors.white,
  },
  secondaryText: {
    color: colors.text.primary,
  },
  ghostText: {
    color: colors.primary,
  },
  outlineText: {
    color: colors.primary,
  },

  // Size text styles
  smallText: {
    ...typography.buttonSmall,
  },
  mediumText: {
    ...typography.button,
  },
  largeText: {
    ...typography.buttonLarge,
  },

  // Disabled styles
  disabled: {
    backgroundColor: colors.button.primaryDisabled,
    borderColor: colors.button.primaryDisabled,
  },
  disabledText: {
    color: colors.text.tertiary,
  },
});
