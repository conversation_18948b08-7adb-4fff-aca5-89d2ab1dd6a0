export interface QuizSessionResponse {
  id: string;
  user_id: string;
  quiz_id: string;
  start_time: string;
  end_time?: string | null;
  time_limit?: number | null; // seconds
  is_completed: boolean;
  current_question: number;
  total_questions: number;
}

export interface SubmitAnswerRequest {
  question_id: string;
  answer?: string | string[];
  time_taken?: number; // seconds
  time_spent?: number; // seconds
}

export interface AnswerResponse {
  id: string;
  question_id: string;
  user_answer?: 'A' | 'B' | 'C' | 'D' | 'E' | null;
  is_correct: boolean;
  time_spent?: number | null;
}

export interface QuizResultResponse {
  id: string;
  user_id: string;
  quiz_id: string;
  score: number;
  total_questions: number;
  percentage: number;
  time_spent?: number | null;
  completed_at: string;
  correct_answers: number;
  wrong_answers: number;
  skipped_answers: number;
  average_time_per_question: number;
  rank?: number | null;
  total_users: number;
}

