package topic

import (
    "github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
    CreateTopic(payload *dtos.CreateTopicRequest) (*dtos.TopicResponse, error)
    UpdateTopic(topicID string, payload *dtos.UpdateTopicRequest) (*dtos.TopicResponse, error)
    DeleteTopic(topicID string) error
    GetTopic(topicID string, userID *string) (*dtos.TopicResponse, error)
    GetTopics(payload *dtos.TopicListRequest, userID *string) (*dtos.TopicListResponse, error)
    UpdateProgress(userID string, payload *dtos.UpdateTopicProgressRequest) (*dtos.TopicProgress, error)
    GetProgress(userID string, topicID string) (*dtos.TopicProgress, error)
}

type service struct {
    repository Repository
}

func NewService(r Repository) Service { return &service{repository: r} }

func (s *service) CreateTopic(payload *dtos.CreateTopicRequest) (*dtos.TopicResponse, error) {
    return s.repository.CreateTopic(payload)
}

func (s *service) UpdateTopic(topicID string, payload *dtos.UpdateTopicRequest) (*dtos.TopicResponse, error) {
    return s.repository.UpdateTopic(topicID, payload)
}

func (s *service) DeleteTopic(topicID string) error { return s.repository.DeleteTopic(topicID) }

func (s *service) GetTopic(topicID string, userID *string) (*dtos.TopicResponse, error) {
    return s.repository.GetTopic(topicID, userID)
}

func (s *service) GetTopics(payload *dtos.TopicListRequest, userID *string) (*dtos.TopicListResponse, error) {
    return s.repository.GetTopics(payload, userID)
}

func (s *service) UpdateProgress(userID string, payload *dtos.UpdateTopicProgressRequest) (*dtos.TopicProgress, error) {
    return s.repository.UpdateProgress(userID, payload)
}

func (s *service) GetProgress(userID string, topicID string) (*dtos.TopicProgress, error) {
    return s.repository.GetProgress(userID, topicID)
}

