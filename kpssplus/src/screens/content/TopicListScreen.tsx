import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {ContentStackParamList} from '../../navigation/types';
import {colors, typography, spacing} from '../../theme';
import {Button} from '../../components/ui';
import {useAuth, useTokens} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';

type Props = StackScreenProps<ContentStackParamList, 'TopicList'>;

interface QuizOption {
  id: string;
  title: string;
  questionCount: number;
  timeLimit: number; // in minutes
  difficulty: 'easy' | 'medium' | 'hard';
  tokenCost: number;
}

export const TopicListScreen: React.FC<Props> = () => {
  // const {subjectId} = route.params || {};
  const {isGuest} = useAuth();
  const {balance: tokenBalance, spendTokens, checkTokens} = useTokens();

  // Mock data - in real app this would come from API based on subjectId
  const [quizOptions] = useState<QuizOption[]>([
    {
      id: '1',
      title: 'Türkçe Konu Çalış',
      questionCount: 10,
      timeLimit: 15,
      difficulty: 'easy',
      tokenCost: 1,
    },
    {
      id: '2',
      title: 'Kelimeler',
      questionCount: 15,
      timeLimit: 20,
      difficulty: 'medium',
      tokenCost: 1,
    },
    {
      id: '3',
      title: 'İnteraktif',
      questionCount: 20,
      timeLimit: 25,
      difficulty: 'hard',
      tokenCost: 1,
    },
  ]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return colors.success;
      case 'medium':
        return colors.warning;
      case 'hard':
        return colors.error;
      default:
        return colors.text.secondary;
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'Kolay';
      case 'medium':
        return 'Orta';
      case 'hard':
        return 'Zor';
      default:
        return 'Bilinmiyor';
    }
  };

  const handleStartQuiz = async (option: QuizOption) => {
    if (isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Quiz çözmek için giriş yapmanız gerekiyor.',
        [
          {text: 'İptal', style: 'cancel'},
          {
            text: 'Giriş Yap',
            onPress: () => {
              // Navigate to auth - this would need proper navigation setup
              console.log('Navigate to login');
            },
          },
        ],
      );
      return;
    }

    const hasEnoughTokens = await checkTokens(option.tokenCost);
    if (!hasEnoughTokens) {
      Alert.alert(
        'Yetersiz Jeton',
        `Bu quiz için ${option.tokenCost} jetona ihtiyacınız var. Mevcut bakiyeniz: ${tokenBalance}`,
        [
          {text: 'İptal', style: 'cancel'},
          {
            text: 'Jeton Al',
            onPress: () => {
              /* Navigate to token purchase */
            },
          },
        ],
      );
      return;
    }

    const success = await spendTokens(
      option.tokenCost,
      'Quiz başlatma',
      option.id,
    );
    if (success) {
      // Navigate to quiz play screen
      // Navigate to quiz - this would need proper navigation setup
      console.log('Navigate to quiz:', option.id);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => console.log('Go back')}>
            <Icon name="chevron-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Konu Çalış</Text>
          <View style={styles.headerRight} />
        </View>

        {/* Quiz Options */}
        <View style={styles.content}>
          {quizOptions.map(option => (
            <View key={option.id} style={styles.quizCard}>
              <View style={styles.quizHeader}>
                <Text style={styles.quizTitle}>{option.title}</Text>
                <View style={styles.tokenCost}>
                  <Icon name="diamond" size={16} color={colors.primary} />
                  <Text style={styles.tokenText}>{option.tokenCost}</Text>
                </View>
              </View>

              <View style={styles.quizDetails}>
                <View style={styles.detailItem}>
                  <Icon
                    name="help-circle-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.detailText}>
                    {option.questionCount} Soru
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <Icon
                    name="time-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.detailText}>{option.timeLimit} dk</Text>
                </View>
                <View style={styles.detailItem}>
                  <View
                    style={[
                      styles.difficultyDot,
                      {backgroundColor: getDifficultyColor(option.difficulty)},
                    ]}
                  />
                  <Text style={styles.detailText}>
                    {getDifficultyText(option.difficulty)}
                  </Text>
                </View>
              </View>

              <Button
                title="BAŞLAT"
                onPress={() => handleStartQuiz(option)}
                style={styles.startButton}
                disabled={isGuest}
              />
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  backButton: {
    padding: spacing[2],
  },
  headerTitle: {
    ...typography.h5,
    color: colors.text.primary,
  },
  headerRight: {
    width: 40, // Same width as back button for centering
  },
  content: {
    padding: spacing[4],
  },
  quizCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing[4],
    marginBottom: spacing[3],
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quizHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  quizTitle: {
    ...typography.h6,
    color: colors.text.primary,
    flex: 1,
  },
  tokenCost: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryMuted,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 6,
  },
  tokenText: {
    ...typography.caption,
    color: colors.primary,
    marginLeft: spacing[1],
    fontWeight: '600',
  },
  quizDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing[4],
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  startButton: {
    marginTop: spacing[2],
  },
});
