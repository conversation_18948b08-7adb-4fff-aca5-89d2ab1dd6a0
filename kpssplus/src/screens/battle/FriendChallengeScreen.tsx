import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import {Card} from '../../components/ui/Card';
import {Button} from '../../components/ui/Button';
import {Icon} from '../../components/ui';
import {colors} from '../../theme/colors';
import {typography} from '../../theme/typography';
import {spacing} from '../../theme/spacing';
import {useBattle, useAuth} from '../../hooks';
import {FriendChallenge} from '../../types';
import {StyleSheet} from 'react-native';

type Props = {};

export const FriendChallengeScreen: React.FC<Props> = () => {
  const {isAuthenticated, isGuest, user} = useAuth();
  const {friendChallenges, loadFriendChallenges, respondToFriendChallenge} =
    useBattle();

  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'received' | 'sent'>('received');

  useEffect(() => {
    if (isAuthenticated) {
      loadFriendChallenges();
    }
  }, [isAuthenticated, loadFriendChallenges]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadFriendChallenges();
    setRefreshing(false);
  };

  const handleSendChallenge = () => {
    if (isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Arkadaş meydan okuması göndermek için giriş yapmalısınız.',
        [
          {text: 'İptal', style: 'cancel'},
          {
            text: 'Giriş Yap',
            onPress: () => console.log('Navigation disabled'),
          },
        ],
      );
      return;
    }
    console.log('Navigation disabled');
  };

  const handleAcceptChallenge = async (challengeId: string) => {
    const success = await respondToFriendChallenge(challengeId, 'accept');
    if (success) {
      // Navigate to quiz or challenge detail
      console.log('Navigation disabled');
    }
  };

  const handleDeclineChallenge = async (challengeId: string) => {
    Alert.alert(
      'Meydan Okumayı Reddet',
      'Bu meydan okumayı reddetmek istediğinizden emin misiniz?',
      [
        {text: 'İptal', style: 'cancel'},
        {
          text: 'Reddet',
          style: 'destructive',
          onPress: () => respondToFriendChallenge(challengeId, 'decline'),
        },
      ],
    );
  };

  const handleChallengePress = (_challenge: FriendChallenge) => {
    console.log('Navigation disabled');
  };

  const getReceivedChallenges = () => {
    return friendChallenges.filter(c => c.challengedId === user?.id);
  };

  const getSentChallenges = () => {
    return friendChallenges.filter(c => c.challengerId === user?.id);
  };

  const renderChallengeCard = (challenge: FriendChallenge) => {
    const isReceived = challenge.challengedId === user?.id;
    const isPending = challenge.status === 'pending';
    const isCompleted = challenge.status === 'completed';
    // const isAccepted = challenge.status === 'accepted';

    const otherUser = isReceived ? challenge.challenger : challenge.challenged;

    return (
      <Card
        key={challenge.id}
        style={styles.challengeCard}
        variant="elevated"
        onPress={() => handleChallengePress(challenge)}>
        <View style={styles.challengeHeader}>
          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {otherUser.firstName?.charAt(0) || 'U'}
                {otherUser.lastName?.charAt(0) || 'U'}
              </Text>
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>
                {otherUser.firstName} {otherUser.lastName}
              </Text>
              <Text style={styles.userHandle}>@{otherUser.username}</Text>
            </View>
          </View>
          <View
            style={[styles.statusBadge, styles[`status${challenge.status}`]]}>
            <Text
              style={[
                styles.statusText,
                styles[`statusText${challenge.status}`],
              ]}>
              {challenge.status === 'pending' && 'Bekliyor'}
              {challenge.status === 'accepted' && 'Kabul Edildi'}
              {challenge.status === 'declined' && 'Reddedildi'}
              {challenge.status === 'completed' && 'Tamamlandı'}
              {challenge.status === 'expired' && 'Süresi Doldu'}
            </Text>
          </View>
        </View>

        <View style={styles.challengeContent}>
          <Text style={styles.quizTitle}>{challenge.quiz.title}</Text>
          <Text style={styles.quizSubject}>{challenge.quiz.subject}</Text>
          {challenge.message && (
            <Text style={styles.challengeMessage}>"{challenge.message}"</Text>
          )}
        </View>

        <View style={styles.challengeDetails}>
          <View style={styles.detailItem}>
            <Icon
              name="help-circle-outline"
              size={16}
              color={colors.text.secondary}
            />
            <Text style={styles.detailText}>
              {challenge.quiz.questionCount} soru
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Icon name="time-outline" size={16} color={colors.text.secondary} />
            <Text style={styles.detailText}>{challenge.quiz.timeLimit} dk</Text>
          </View>
          <View style={styles.detailItem}>
            <Icon
              name="trophy-outline"
              size={16}
              color={colors.text.secondary}
            />
            <Text style={styles.detailText}>{challenge.quiz.difficulty}</Text>
          </View>
        </View>

        {isReceived && isPending && (
          <View style={styles.challengeActions}>
            <Button
              title="Reddet"
              variant="outline"
              size="small"
              onPress={() => handleDeclineChallenge(challenge.id)}
              style={styles.declineButton}
            />
            <Button
              title="Kabul Et"
              size="small"
              onPress={() => handleAcceptChallenge(challenge.id)}
              style={styles.acceptButton}
            />
          </View>
        )}

        {isCompleted && (
          <View style={styles.resultsPreview}>
            <Text style={styles.resultsTitle}>Sonuçlar</Text>
            <View style={styles.resultsRow}>
              <View style={styles.resultItem}>
                <Text style={styles.resultLabel}>
                  {challenge.challenger.firstName}
                </Text>
                <Text style={styles.resultScore}>
                  {challenge.challengerResult?.score || 0}
                </Text>
              </View>
              <Text style={styles.resultVs}>VS</Text>
              <View style={styles.resultItem}>
                <Text style={styles.resultLabel}>
                  {challenge.challenged.firstName}
                </Text>
                <Text style={styles.resultScore}>
                  {challenge.challengedResult?.score || 0}
                </Text>
              </View>
            </View>
          </View>
        )}

        <View style={styles.challengeFooter}>
          <Text style={styles.timeText}>
            {new Date(challenge.createdAt).toLocaleDateString('tr-TR')}
          </Text>
          {isPending && (
            <Text style={styles.expiryText}>
              Süre: {new Date(challenge.expiresAt).toLocaleDateString('tr-TR')}
            </Text>
          )}
        </View>
      </Card>
    );
  };

  if (isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.guestContainer}>
          <Icon name="people-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.guestTitle}>Arkadaş Kapışması</Text>
          <Text style={styles.guestDescription}>
            Arkadaşlarınızla 1v1 meydan okuma yapmak için giriş yapın.
          </Text>
          <Button
            title="Giriş Yap"
            onPress={() => console.log('Navigation disabled')}
            style={styles.loginButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  const receivedChallenges = getReceivedChallenges();
  const sentChallenges = getSentChallenges();
  const displayChallenges =
    activeTab === 'received' ? receivedChallenges : sentChallenges;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Arkadaş Kapışması</Text>
          <Text style={styles.headerSubtitle}>
            Arkadaşlarınızla 1v1 meydan okuma yapın!
          </Text>
        </View>

        {/* Send Challenge Button */}
        <Card style={styles.sendCard} variant="outlined">
          <View style={styles.sendContent}>
            <Icon name="flash-outline" size={32} color={colors.secondary} />
            <Text style={styles.sendTitle}>Meydan Okuma Gönder</Text>
            <Text style={styles.sendDescription}>
              Arkadaşlarınıza meydan okuma gönderin ve kim daha iyi gösterin!
            </Text>
            <Button
              title="Meydan Oku"
              onPress={handleSendChallenge}
              style={styles.sendButton}
            />
          </View>
        </Card>

        {/* Tabs */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'received' && styles.activeTab]}
            onPress={() => setActiveTab('received')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'received' && styles.activeTabText,
              ]}>
              Gelen ({receivedChallenges.length})
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'sent' && styles.activeTab]}
            onPress={() => setActiveTab('sent')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'sent' && styles.activeTabText,
              ]}>
              Gönderilen ({sentChallenges.length})
            </Text>
          </TouchableOpacity>
        </View>

        {/* Challenges List */}
        <View style={styles.section}>
          {displayChallenges.length === 0 ? (
            <Card style={styles.emptyCard} variant="outlined">
              <Icon name="trophy-outline" size={48} color={colors.gray[400]} />
              <Text style={styles.emptyTitle}>
                {activeTab === 'received'
                  ? 'Gelen meydan okuma yok'
                  : 'Gönderilen meydan okuma yok'}
              </Text>
              <Text style={styles.emptyDescription}>
                {activeTab === 'received'
                  ? 'Arkadaşlarınızdan meydan okuma bekliyorsunuz'
                  : 'İlk meydan okumayı siz gönderin!'}
              </Text>
            </Card>
          ) : (
            displayChallenges.map(renderChallengeCard)
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    padding: spacing[4],
  },
  guestContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing[6],
  },
  guestTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  guestDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[6],
  },
  loginButton: {
    minWidth: 120,
  },
  header: {
    marginBottom: spacing[6],
  },
  headerTitle: {
    ...typography.h3,
    marginBottom: spacing[1],
  },
  headerSubtitle: {
    ...typography.body1,
    color: colors.text.secondary,
  },
  sendCard: {
    marginBottom: spacing[6],
  },
  sendContent: {
    alignItems: 'center',
    padding: spacing[4],
  },
  sendTitle: {
    ...typography.h5,
    marginTop: spacing[2],
    marginBottom: spacing[1],
  },
  sendDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[4],
  },
  sendButton: {
    minWidth: 150,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: spacing[4],
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: spacing[1],
  },
  tab: {
    flex: 1,
    paddingVertical: spacing[2],
    paddingHorizontal: spacing[3],
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    ...typography.body2,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  section: {
    marginBottom: spacing[6],
  },
  challengeCard: {
    marginBottom: spacing[3],
    padding: spacing[4],
  },
  challengeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  avatarText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...typography.body1,
    fontWeight: '600',
    marginBottom: spacing[0.5],
  },
  userHandle: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  statusBadge: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 12,
  },
  statuspending: {
    backgroundColor: colors.warning + '20',
  },
  statusaccepted: {
    backgroundColor: colors.success + '20',
  },
  statusdeclined: {
    backgroundColor: colors.error + '20',
  },
  statuscompleted: {
    backgroundColor: colors.primary + '20',
  },
  statusexpired: {
    backgroundColor: colors.gray[200],
  },
  statusText: {
    ...typography.caption,
    fontWeight: '600',
  },
  statusTextpending: {
    color: colors.warning,
  },
  statusTextaccepted: {
    color: colors.success,
  },
  statusTextdeclined: {
    color: colors.error,
  },
  statusTextcompleted: {
    color: colors.primary,
  },
  statusTextexpired: {
    color: colors.text.secondary,
  },
  challengeContent: {
    marginBottom: spacing[3],
  },
  quizTitle: {
    ...typography.h6,
    marginBottom: spacing[1],
  },
  quizSubject: {
    ...typography.body2,
    color: colors.primary,
    marginBottom: spacing[2],
  },
  challengeMessage: {
    ...typography.body2,
    color: colors.text.secondary,
    fontStyle: 'italic',
  },
  challengeDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing[3],
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing[4],
    marginBottom: spacing[1],
  },
  detailText: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  challengeActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: spacing[2],
    marginBottom: spacing[3],
  },
  declineButton: {
    minWidth: 80,
  },
  acceptButton: {
    minWidth: 80,
  },
  resultsPreview: {
    backgroundColor: colors.gray[50],
    padding: spacing[3],
    borderRadius: 8,
    marginBottom: spacing[3],
  },
  resultsTitle: {
    ...typography.body2,
    fontWeight: '600',
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  resultsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  resultItem: {
    alignItems: 'center',
    flex: 1,
  },
  resultLabel: {
    ...typography.caption,
    color: colors.text.secondary,
    marginBottom: spacing[1],
  },
  resultScore: {
    ...typography.h5,
    color: colors.primary,
    fontWeight: 'bold',
  },
  resultVs: {
    ...typography.body2,
    color: colors.text.secondary,
    fontWeight: '600',
    marginHorizontal: spacing[2],
  },
  challengeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeText: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  expiryText: {
    ...typography.caption,
    color: colors.warning,
  },
  emptyCard: {
    alignItems: 'center',
    padding: spacing[6],
  },
  emptyTitle: {
    ...typography.h6,
    marginTop: spacing[3],
    marginBottom: spacing[1],
  },
  emptyDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});
