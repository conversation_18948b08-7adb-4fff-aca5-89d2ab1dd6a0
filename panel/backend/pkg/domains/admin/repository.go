package admin

import (
	"time"

	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	GetUsers(page, limit int) ([]entities.User, error)
	GetUser(userID string) (entities.User, error)
	UpdateUser(userID string, user entities.User) error
	DeleteUser(userID string) error
	// Content management (for Content.jsx)
	AdminGetContent(page, limit int) ([]entities.Content, error)
	AdminCreateContent(content entities.Content) error
	AdminUpdateContent(contentID string, content entities.Content) error
	AdminDeleteContent(contentID string) error
	// Study content management (for StudyContent.jsx)
	AdminGetStudyContent(page, limit int) ([]entities.Content, error)
	AdminCreateStudyContent(content entities.Content) error
	AdminUpdateStudyContent(contentID string, content entities.Content) error
	AdminDeleteStudyContent(contentID string) error
	// Quiz management (for Quizzes.jsx)
	AdminGetQuizzes(page, limit int) ([]entities.Quiz, error)
	AdminCreateQuiz(quiz entities.Quiz) error
	AdminUpdateQuiz(quizID string, quiz entities.Quiz) error
	AdminDeleteQuiz(quizID string) error
	GetAnalytics() (int64, error)
	GetUserAnalytics() (int64, int64, error)
	GetContentAnalytics() (int64, int64, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetUsers(page, limit int) ([]entities.User, error) {
	var users []entities.User
	if err := r.db.Limit(limit).Offset((page - 1) * limit).Find(&users).Error; err != nil {
		return nil, err
	}

	return users, nil
}

func (r *repository) GetUser(userID string) (entities.User, error) {
	var user entities.User
	if err := r.db.First(&user, "id = ?", userID).Error; err != nil {
		return user, err
	}
	return user, nil
}

func (r *repository) UpdateUser(userID string, user entities.User) error {
	if err := r.db.Model(&entities.User{}).Where("id = ?", userID).Updates(user).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) DeleteUser(userID string) error {
	if err := r.db.Delete(&entities.User{}, "id = ?", userID).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminGetContent(page, limit int) ([]entities.Content, error) {
	var content []entities.Content
	if err := r.db.Limit(limit).Offset((page - 1) * limit).Find(&content).Error; err != nil {
		return content, err
	}
	return content, nil
}

func (r *repository) AdminCreateContent(content entities.Content) error {
	if err := r.db.Create(&content).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminUpdateContent(contentID string, content entities.Content) error {
	if err := r.db.Model(&entities.Content{}).Where("id = ?", contentID).Updates(content).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminDeleteContent(contentID string) error {
	if err := r.db.Delete(&entities.Content{}, "id = ?", contentID).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) GetAnalytics() (int64, error) {
	var totalUsers int64
	r.db.Model(&entities.User{}).Count(&totalUsers)
	return totalUsers, nil
}

func (r *repository) GetUserAnalytics() (int64, int64, error) {
	var activeUsers int64
	var newUsersToday int64

	r.db.Model(&entities.User{}).Where("last_login_at > ?", time.Now().AddDate(0, 0, -7)).Count(&activeUsers)
	r.db.Model(&entities.User{}).Where("created_at > ?", time.Now().Truncate(24*time.Hour)).Count(&newUsersToday)

	return activeUsers, newUsersToday, nil
}

func (r *repository) GetContentAnalytics() (int64, int64, error) {
	var publishedContent int64
	var draftContent int64

	r.db.Model(&entities.Content{}).Where("status = ?", "published").Count(&publishedContent)
	r.db.Model(&entities.Content{}).Where("status = ?", "draft").Count(&draftContent)

	return publishedContent, draftContent, nil
}

func (r *repository) AdminGetStudyContent(page, limit int) ([]entities.Content, error) {
	var content []entities.Content
	if err := r.db.Limit(limit).Offset((page - 1) * limit).Find(&content).Error; err != nil {
		return content, err
	}
	return content, nil
}

func (r *repository) AdminCreateStudyContent(content entities.Content) error {
	if err := r.db.Create(&content).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminUpdateStudyContent(contentID string, content entities.Content) error {
	if err := r.db.Model(&entities.Content{}).Where("id = ?", contentID).Updates(content).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminDeleteStudyContent(contentID string) error {
	if err := r.db.Delete(&entities.Content{}, "id = ?", contentID).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminGetQuizzes(page, limit int) ([]entities.Quiz, error) {
	var quizzes []entities.Quiz
	offset := (page - 1) * limit

	if err := r.db.Offset(offset).Limit(limit).Find(&quizzes).Error; err != nil {
		return nil, err
	}
	return quizzes, nil
}

func (r *repository) AdminCreateQuiz(quiz entities.Quiz) error {
	if err := r.db.Create(&quiz).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminUpdateQuiz(quizID string, quiz entities.Quiz) error {
	if err := r.db.Model(&entities.Quiz{}).Where("id = ?", quizID).Updates(quiz).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) AdminDeleteQuiz(quizID string) error {
	if err := r.db.Delete(&entities.Quiz{}, "id = ?", quizID).Error; err != nil {
		return err
	}
	return nil
}
