package dtos

import (
	"time"

	"github.com/google/uuid"
)

type CreateTopicRequest struct {
	Title       string     `json:"title" validate:"required,min=1,max=200"`
	Description *string    `json:"description" validate:"omitempty,max=1000"`
	Body        *string    `json:"body" validate:"omitempty"`
	Subject     *string    `json:"subject" validate:"omitempty,max=100"`
	ParentID    *uuid.UUID `json:"parent_id" validate:"omitempty"`
	Order       *int       `json:"order" validate:"omitempty,min=0"`
	IsPublished *bool      `json:"is_published"`
}

type UpdateTopicRequest struct {
	Title       *string    `json:"title" validate:"omitempty,min=1,max=200"`
	Description *string    `json:"description" validate:"omitempty,max=1000"`
	Body        *string    `json:"body" validate:"omitempty"`
	Subject     *string    `json:"subject" validate:"omitempty,max=100"`
	ParentID    *uuid.UUID `json:"parent_id" validate:"omitempty"`
	Order       *int       `json:"order" validate:"omitempty,min=0"`
	IsPublished *bool      `json:"is_published"`
}

type TopicResponse struct {
	ID          uuid.UUID       `json:"id"`
	Title       string          `json:"title"`
	Description *string         `json:"description"`
	Body        *string         `json:"body"`
	Subject     *string         `json:"subject"`
	ParentID    *uuid.UUID      `json:"parent_id"`
	Order       int             `json:"order"`
	IsPublished bool            `json:"is_published"`
	Progress    *TopicProgress  `json:"progress"`
	Children    []TopicResponse `json:"children"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

type TopicListRequest struct {
	Page     int     `json:"page" validate:"omitempty,min=1"`
	Limit    int     `json:"limit" validate:"omitempty,min=1,max=100"`
	ParentID *string `json:"parent_id"`
	Subject  *string `json:"subject"`
	Tree     bool    `json:"tree"`
}

type TopicListResponse struct {
	Topics     []TopicResponse `json:"topics"`
	Total      int             `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

type UpdateTopicProgressRequest struct {
	TopicID     uuid.UUID `json:"topic_id" validate:"required"`
	Percentage  *float64  `json:"percentage" validate:"omitempty,min=0,max=100"`
	IsCompleted *bool     `json:"is_completed"`
}

type TopicProgress struct {
	TopicID     *uuid.UUID `json:"topic_id"`
	Percentage  float64    `json:"percentage"`
	IsCompleted bool       `json:"is_completed"`
	CompletedAt *time.Time `json:"completed_at"`
}
