package quiz

import (
    "errors"
    "math"
    "time"

    "github.com/google/uuid"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/entities"
    "gorm.io/gorm"
)

type Repository interface {
	// Quiz management
	CreateQuiz(userID string, payload *dtos.CreateQuizRequest) (*dtos.QuizResponse, error)
	GetQuiz(quizID string, userID *string) (*dtos.QuizDetailResponse, error)
	GetQuizList(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	UpdateQuiz(quizID string, payload *dtos.UpdateQuizRequest) (*dtos.QuizResponse, error)
	DeleteQuiz(quizID string) error

	// Quiz search and filtering
	SearchQuizzes(payload *dtos.SearchQuizzesRequest) (*dtos.QuizListResponse, error)
	GetQuizzesBySubject(subject string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetPopularQuizzes(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetUserQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Question management
	AddQuestionToQuiz(quizID string, payload *dtos.AddQuestionRequest) (*dtos.QuestionResponse, error)
	UpdateQuestion(questionID string, payload *dtos.UpdateQuestionRequest) (*dtos.QuestionResponse, error)
	DeleteQuestion(questionID string) error
	GetQuestion(questionID string) (*dtos.QuestionResponse, error)
	GetQuizQuestions(quizID string) (*dtos.QuizQuestionsResponse, error)

	// Quiz taking
    StartQuiz(userID string, quizID string) (*dtos.QuizSessionResponse, error)
    SubmitAnswer(userID string, sessionID string, payload *dtos.SubmitAnswerRequest) (*dtos.AnswerResponse, error)
    FinishQuiz(userID string, sessionID string) (*dtos.QuizResultResponse, error)
    GetQuizResult(userID string, resultID string) (*dtos.QuizResultResponse, error)

	// Quiz results and statistics
	GetUserQuizResults(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error)
	GetQuizStatistics(quizID string) (*dtos.QuizStatisticsResponse, error)
	GetQuizLeaderboard(quizID string, payload *dtos.GetLeaderboardRequest) (*dtos.QuizLeaderboardResponse, error)
	GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error)

	// Quiz sharing and social features
	ShareQuiz(userID string, quizID string, payload *dtos.ShareQuizRequest) error
	InviteToQuiz(userID string, quizID string, payload *dtos.InviteToQuizRequest) error
	GetSharedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Quiz recommendations
	GetRecommendedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetSimilarQuizzes(quizID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Helper methods
	CreateQuizTimelineEntry(userID string, resultID string) error
	UpdateUserStatsFromQuiz(userID string, result *dtos.QuizResultResponse) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Quiz management
func (r *repository) CreateQuiz(userID string, payload *dtos.CreateQuizRequest) (*dtos.QuizResponse, error) {
	quiz := entities.Quiz{
		Title:       payload.Title,
		Description: payload.Description,
		Type:        entities.QuizType(payload.Type),
		Subject:     payload.Subject,
		Difficulty:  payload.Difficulty,
		TimeLimit:   payload.TimeLimit,
		IsPublic:    payload.IsPublic,
		CreatorID:   &[]uuid.UUID{uuid.MustParse(userID)}[0],
	}

	err := r.db.Create(&quiz).Error
	if err != nil {
		return nil, errors.New("Failed to create quiz")
	}

	return r.getQuizResponse(&quiz, nil)
}

func (r *repository) GetQuiz(quizID string, userID *string) (*dtos.QuizDetailResponse, error) {
	var quiz entities.Quiz
	err := r.db.Preload("Creator").Preload("Questions").Where("id = ?", quizID).First(&quiz).Error
	if err != nil {
		return nil, errors.New("Quiz not found")
	}

	quizResponse, err := r.getQuizResponse(&quiz, userID)
	if err != nil {
		return nil, err
	}

	// Get questions separately
	var questionEntities []entities.Question
	r.db.Where("quiz_id = ?", quiz.ID).Order("order_index ASC").Find(&questionEntities)

	questions := make([]dtos.QuestionResponse, len(questionEntities))
	for i, question := range questionEntities {
		questions[i] = dtos.QuestionResponse{
			ID:         question.ID,
			QuizID:     question.QuizID,
			Text:       question.Text,
			OptionA:    question.OptionA,
			OptionB:    question.OptionB,
			OptionC:    question.OptionC,
			OptionD:    question.OptionD,
			OptionE:    question.OptionE,
			Subject:    question.Subject,
			Year:       question.Year,
			OrderIndex: question.OrderIndex,
			CreatedAt:  question.CreatedAt,
			UpdatedAt:  question.UpdatedAt,
		}

		// Only show correct answer to quiz creator
		if userID != nil && *userID == quiz.CreatorID.String() {
			questions[i].CorrectAnswer = question.CorrectAnswer
			questions[i].Explanation = question.Explanation
		}
	}

	return &dtos.QuizDetailResponse{
		QuizResponse: *quizResponse,
		Questions:    questions,
	}, nil
}

func (r *repository) GetQuizList(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	page := payload.Page
	if page < 1 {
		page = 1
	}

	limit := payload.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	query := r.db.Model(&entities.Quiz{}).Preload("Creator")

	// Apply filters
	if payload.Type != nil {
		query = query.Where("type = ?", *payload.Type)
	}
	if payload.Subject != nil {
		query = query.Where("subject = ?", *payload.Subject)
	}
	if payload.Difficulty != nil {
		query = query.Where("difficulty = ?", *payload.Difficulty)
	}
	if payload.IsPublic != nil {
		query = query.Where("is_public = ?", *payload.IsPublic)
	}
	if payload.CreatorID != nil {
		query = query.Where("creator_id = ?", *payload.CreatorID)
	}

	// Apply sorting
	sortBy := payload.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := "ASC"
	if payload.SortDesc {
		sortOrder = "DESC"
	}

	query = query.Order(sortBy + " " + sortOrder)

	// Get total count
	var total int64
	query.Count(&total)

	// Get quizzes
	var quizzes []entities.Quiz
	err := query.Offset(offset).Limit(limit).Find(&quizzes).Error
	if err != nil {
		return nil, errors.New("Failed to get quiz list")
	}

	// Convert to response format
	quizResponses := make([]dtos.QuizResponse, len(quizzes))
	for i, quiz := range quizzes {
		response, err := r.getQuizResponse(&quiz, nil)
		if err != nil {
			continue
		}
		quizResponses[i] = *response
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &dtos.QuizListResponse{
		Quizzes:    quizResponses,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (r *repository) UpdateQuiz(quizID string, payload *dtos.UpdateQuizRequest) (*dtos.QuizResponse, error) {
	var quiz entities.Quiz
	err := r.db.Where("id = ?", quizID).First(&quiz).Error
	if err != nil {
		return nil, errors.New("Quiz not found")
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if payload.Title != nil {
		updates["title"] = *payload.Title
	}
	if payload.Description != nil {
		updates["description"] = *payload.Description
	}
	if payload.Subject != nil {
		updates["subject"] = *payload.Subject
	}
	if payload.Difficulty != nil {
		updates["difficulty"] = *payload.Difficulty
	}
	if payload.TimeLimit != nil {
		updates["time_limit"] = *payload.TimeLimit
	}
	if payload.IsPublic != nil {
		updates["is_public"] = *payload.IsPublic
	}
	if payload.IsActive != nil {
		updates["is_active"] = *payload.IsActive
	}

	if len(updates) > 0 {
		err = r.db.Model(&quiz).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update quiz")
		}
	}

	// Reload quiz to get updated data
	r.db.Preload("Creator").Where("id = ?", quizID).First(&quiz)

	return r.getQuizResponse(&quiz, nil)
}

func (r *repository) DeleteQuiz(quizID string) error {
	err := r.db.Where("id = ?", quizID).Delete(&entities.Quiz{}).Error
	if err != nil {
		return errors.New("Failed to delete quiz")
	}
	return nil
}

// Helper method to convert entity to response
func (r *repository) getQuizResponse(quiz *entities.Quiz, userID *string) (*dtos.QuizResponse, error) {
	// Get question count
	var questionCount int64
	r.db.Model(&entities.Question{}).Where("quiz_id = ?", quiz.ID).Count(&questionCount)

	// Get attempt count
	var attemptCount int64
	r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quiz.ID).Count(&attemptCount)

	// Get average score
	var avgScore float64
	r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quiz.ID).Select("AVG(percentage)").Scan(&avgScore)

	// Get completion rate (simplified)
	completionRate := float64(100) // Placeholder

	// Get creator info separately
	var creator entities.User
	r.db.Where("id = ?", quiz.CreatorID).First(&creator)

	creatorInfo := dtos.UserInfo{
		ID:         creator.ID,
		Username:   creator.Username,
		Name:       creator.Name,
		Email:      creator.Email,
		IsVerified: creator.IsVerified,
		CreatedAt:  creator.CreatedAt,
	}

	response := &dtos.QuizResponse{
		ID:             quiz.ID,
		Title:          quiz.Title,
		Description:    quiz.Description,
		Type:           string(quiz.Type),
		Subject:        quiz.Subject,
		Difficulty:     quiz.Difficulty,
		TimeLimit:      quiz.TimeLimit,
		IsPublic:       quiz.IsPublic,
		IsActive:       quiz.IsActive,
		CreatorID:      quiz.CreatorID.String(),
		QuestionCount:  int(questionCount),
		AttemptCount:   int(attemptCount),
		AverageScore:   avgScore,
		CompletionRate: completionRate,
		Creator:        creatorInfo,
		CreatedAt:      quiz.CreatedAt,
		UpdatedAt:      quiz.UpdatedAt,
	}

	// Add user-specific data if userID is provided
	if userID != nil {
		// Get user's best score and attempts
		var userBestScore float64
		var userAttempts int64
		r.db.Model(&entities.QuizResult{}).Where("quiz_id = ? AND user_id = ?", quiz.ID, *userID).Count(&userAttempts)
		r.db.Model(&entities.QuizResult{}).Where("quiz_id = ? AND user_id = ?", quiz.ID, *userID).Select("MAX(percentage)").Scan(&userBestScore)

		if userAttempts > 0 {
			response.UserBestScore = &userBestScore
		}
		response.UserAttempts = int(userAttempts)

		// Get last attempt time
		var lastResult entities.QuizResult
		err := r.db.Where("quiz_id = ? AND user_id = ?", quiz.ID, *userID).Order("completed_at DESC").First(&lastResult).Error
		if err == nil {
			response.LastAttemptAt = &lastResult.CompletedAt
		}
	}

	return response, nil
}

// Quiz search and filtering (placeholder implementations)
func (r *repository) SearchQuizzes(payload *dtos.SearchQuizzesRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement quiz search functionality
	listRequest := &dtos.GetQuizListRequest{
		Page:       payload.Page,
		Limit:      payload.Limit,
		Type:       payload.Type,
		Subject:    payload.Subject,
		Difficulty: payload.Difficulty,
		IsPublic:   payload.IsPublic,
	}
	return r.GetQuizList(listRequest)
}

func (r *repository) GetQuizzesBySubject(subject string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	newPayload := *payload
	newPayload.Subject = &subject
	return r.GetQuizList(&newPayload)
}

func (r *repository) GetPopularQuizzes(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement popularity-based sorting
	newPayload := *payload
	newPayload.SortBy = "created_at"
	newPayload.SortDesc = true
	return r.GetQuizList(&newPayload)
}

func (r *repository) GetUserQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	newPayload := *payload
	newPayload.CreatorID = &userID
	return r.GetQuizList(&newPayload)
}

// Question management (placeholder implementations)
func (r *repository) AddQuestionToQuiz(quizID string, payload *dtos.AddQuestionRequest) (*dtos.QuestionResponse, error) {
	question := entities.Question{
		QuizID:        &[]uuid.UUID{uuid.MustParse(quizID)}[0],
		Text:          payload.Text,
		OptionA:       payload.OptionA,
		OptionB:       payload.OptionB,
		OptionC:       payload.OptionC,
		OptionD:       payload.OptionD,
		OptionE:       payload.OptionE,
		CorrectAnswer: payload.CorrectAnswer,
		Explanation:   payload.Explanation,
		Subject:       payload.Subject,
		Year:          payload.Year,
		OrderIndex:    0, // Default
	}

	if payload.OrderIndex != nil {
		question.OrderIndex = *payload.OrderIndex
	}

	err := r.db.Create(&question).Error
	if err != nil {
		return nil, errors.New("Failed to add question to quiz")
	}

	return &dtos.QuestionResponse{
		ID:            question.ID,
		QuizID:        question.QuizID,
		Text:          question.Text,
		OptionA:       question.OptionA,
		OptionB:       question.OptionB,
		OptionC:       question.OptionC,
		OptionD:       question.OptionD,
		OptionE:       question.OptionE,
		CorrectAnswer: question.CorrectAnswer,
		Explanation:   question.Explanation,
		Subject:       question.Subject,
		Year:          question.Year,
		OrderIndex:    question.OrderIndex,
		CreatedAt:     question.CreatedAt,
		UpdatedAt:     question.UpdatedAt,
	}, nil
}

func (r *repository) UpdateQuestion(questionID string, payload *dtos.UpdateQuestionRequest) (*dtos.QuestionResponse, error) {
	var question entities.Question
	err := r.db.Where("id = ?", questionID).First(&question).Error
	if err != nil {
		return nil, errors.New("Question not found")
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if payload.Text != nil {
		updates["text"] = *payload.Text
	}
	if payload.OptionA != nil {
		updates["option_a"] = *payload.OptionA
	}
	if payload.OptionB != nil {
		updates["option_b"] = *payload.OptionB
	}
	if payload.OptionC != nil {
		updates["option_c"] = *payload.OptionC
	}
	if payload.OptionD != nil {
		updates["option_d"] = *payload.OptionD
	}
	if payload.OptionE != nil {
		updates["option_e"] = *payload.OptionE
	}
	if payload.CorrectAnswer != nil {
		updates["correct_answer"] = *payload.CorrectAnswer
	}
	if payload.Explanation != nil {
		updates["explanation"] = *payload.Explanation
	}
	if payload.Subject != nil {
		updates["subject"] = *payload.Subject
	}
	if payload.Year != nil {
		updates["year"] = *payload.Year
	}
	if payload.OrderIndex != nil {
		updates["order_index"] = *payload.OrderIndex
	}

	if len(updates) > 0 {
		err = r.db.Model(&question).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update question")
		}
	}

	// Reload question
	r.db.Where("id = ?", questionID).First(&question)

	return &dtos.QuestionResponse{
		ID:            question.ID,
		QuizID:        question.QuizID,
		Text:          question.Text,
		OptionA:       question.OptionA,
		OptionB:       question.OptionB,
		OptionC:       question.OptionC,
		OptionD:       question.OptionD,
		OptionE:       question.OptionE,
		CorrectAnswer: question.CorrectAnswer,
		Explanation:   question.Explanation,
		Subject:       question.Subject,
		Year:          question.Year,
		OrderIndex:    question.OrderIndex,
		CreatedAt:     question.CreatedAt,
		UpdatedAt:     question.UpdatedAt,
	}, nil
}

func (r *repository) DeleteQuestion(questionID string) error {
	err := r.db.Where("id = ?", questionID).Delete(&entities.Question{}).Error
	if err != nil {
		return errors.New("Failed to delete question")
	}
	return nil
}

func (r *repository) GetQuestion(questionID string) (*dtos.QuestionResponse, error) {
	var question entities.Question
	err := r.db.Where("id = ?", questionID).First(&question).Error
	if err != nil {
		return nil, errors.New("Question not found")
	}

	return &dtos.QuestionResponse{
		ID:            question.ID,
		QuizID:        question.QuizID,
		Text:          question.Text,
		OptionA:       question.OptionA,
		OptionB:       question.OptionB,
		OptionC:       question.OptionC,
		OptionD:       question.OptionD,
		OptionE:       question.OptionE,
		CorrectAnswer: question.CorrectAnswer,
		Explanation:   question.Explanation,
		Subject:       question.Subject,
		Year:          question.Year,
		OrderIndex:    question.OrderIndex,
		CreatedAt:     question.CreatedAt,
		UpdatedAt:     question.UpdatedAt,
	}, nil
}

func (r *repository) GetQuizQuestions(quizID string) (*dtos.QuizQuestionsResponse, error) {
	var questions []entities.Question
	err := r.db.Where("quiz_id = ?", quizID).Order("order_index ASC").Find(&questions).Error
	if err != nil {
		return nil, errors.New("Failed to get quiz questions")
	}

	questionResponses := make([]dtos.QuestionResponse, len(questions))
	for i, question := range questions {
		questionResponses[i] = dtos.QuestionResponse{
			ID:         question.ID,
			QuizID:     question.QuizID,
			Text:       question.Text,
			OptionA:    question.OptionA,
			OptionB:    question.OptionB,
			OptionC:    question.OptionC,
			OptionD:    question.OptionD,
			OptionE:    question.OptionE,
			Subject:    question.Subject,
			Year:       question.Year,
			OrderIndex: question.OrderIndex,
			CreatedAt:  question.CreatedAt,
			UpdatedAt:  question.UpdatedAt,
			// Don't include correct answer and explanation in list view
		}
	}

	return &dtos.QuizQuestionsResponse{
		QuizID:    uuid.MustParse(quizID),
		Questions: questionResponses,
		Total:     len(questionResponses),
	}, nil
}

// Placeholder implementations for remaining methods
func (r *repository) StartQuiz(userID string, quizID string) (*dtos.QuizSessionResponse, error) {
    // validate quiz exists and active/public or creator
    var quiz entities.Quiz
    if err := r.db.Where("id = ? AND is_active = ?", quizID, true).First(&quiz).Error; err != nil {
        return nil, errors.New("Quiz not found or inactive")
    }
    // create session
    uid := uuid.MustParse(userID)
    qid := uuid.MustParse(quizID)
    sess := entities.QuizSession{UserID: &uid, QuizID: &qid, StartedAt: time.Now()}
    if err := r.db.Create(&sess).Error; err != nil { return nil, errors.New("Failed to start session") }
    // count questions
    var count int64
    r.db.Model(&entities.Question{}).Where("quiz_id = ?", quiz.ID).Count(&count)
    // build quiz response
    qresp, _ := r.getQuizResponse(&quiz, &userID)
    return &dtos.QuizSessionResponse{ID: sess.ID, UserID: uid, QuizID: qid, StartTime: sess.StartedAt, TimeLimit: quiz.TimeLimit, IsCompleted: false, CurrentQuestion: 0, TotalQuestions: int(count), Quiz: *qresp, CreatedAt: sess.CreatedAt, UpdatedAt: sess.UpdatedAt}, nil
}

func (r *repository) SubmitAnswer(userID string, sessionID string, payload *dtos.SubmitAnswerRequest) (*dtos.AnswerResponse, error) {
    // validate session belongs to user
    var sess entities.QuizSession
    if err := r.db.Where("id = ? AND user_id = ? AND is_finished = ?", sessionID, userID, false).First(&sess).Error; err != nil {
        return nil, errors.New("Invalid session")
    }
    // enforce time limit if set
    var quiz entities.Quiz
    if err := r.db.Where("id = ?", sess.QuizID).First(&quiz).Error; err == nil && quiz.TimeLimit != nil {
        elapsed := time.Since(sess.StartedAt)
        if elapsed > time.Duration(*quiz.TimeLimit)*time.Second {
            now := time.Now()
            _ = r.db.Model(&sess).Updates(map[string]interface{}{"is_finished": true, "ended_at": now}).Error
            return nil, errors.New("time limit exceeded")
        }
    }
    // fetch question
    var q entities.Question
    if err := r.db.Where("id = ?", payload.QuestionID).First(&q).Error; err != nil {
        return nil, errors.New("Question not found")
    }
    // upsert session answer
    sid := sess.ID
    var ans entities.QuizSessionAnswer
    err := r.db.Where("session_id = ? AND question_id = ?", sid, payload.QuestionID).First(&ans).Error
    if errors.Is(err, gorm.ErrRecordNotFound) {
        ans = entities.QuizSessionAnswer{SessionID: &sid, QuestionID: &payload.QuestionID, UserAnswer: payload.Answer, TimeSpent: payload.TimeSpent}
        if err := r.db.Create(&ans).Error; err != nil { return nil, errors.New("Failed to save answer") }
    } else if err == nil {
        updates := map[string]interface{}{"user_answer": payload.Answer, "time_spent": payload.TimeSpent}
        if err := r.db.Model(&ans).Updates(updates).Error; err != nil { return nil, errors.New("Failed to update answer") }
    } else { return nil, err }
    // compute correctness immediate
    isCorrect := false
    if payload.Answer != nil && *payload.Answer == q.CorrectAnswer { isCorrect = true }
    qresp := dtos.QuestionResponse{ID: q.ID, QuizID: q.QuizID, Text: q.Text, OptionA: q.OptionA, OptionB: q.OptionB, OptionC: q.OptionC, OptionD: q.OptionD, OptionE: q.OptionE, Subject: q.Subject, Year: q.Year, OrderIndex: q.OrderIndex, CreatedAt: q.CreatedAt, UpdatedAt: q.UpdatedAt}
    return &dtos.AnswerResponse{ID: ans.ID, QuestionID: q.ID, UserAnswer: ans.UserAnswer, IsCorrect: isCorrect, TimeSpent: ans.TimeSpent, Question: qresp, CreatedAt: ans.CreatedAt}, nil
}

func (r *repository) FinishQuiz(userID string, sessionID string) (*dtos.QuizResultResponse, error) {
    var sess entities.QuizSession
    if err := r.db.Where("id = ? AND user_id = ? AND is_finished = ?", sessionID, userID, false).First(&sess).Error; err != nil { return nil, errors.New("Invalid session") }
    // load quiz and questions
    var quiz entities.Quiz
    if err := r.db.Where("id = ?", sess.QuizID).First(&quiz).Error; err != nil { return nil, errors.New("Quiz not found") }
    var questions []entities.Question
    r.db.Where("quiz_id = ?", quiz.ID).Find(&questions)
    // load session answers
    var sanswers []entities.QuizSessionAnswer
    r.db.Where("session_id = ?", sess.ID).Find(&sanswers)
    // map answers
    ansByQ := map[string]entities.QuizSessionAnswer{}
    totalTime := 0
    for _, a := range sanswers { ansByQ[a.QuestionID.String()] = a; if a.TimeSpent != nil { totalTime += *a.TimeSpent } }
    // compute score
    correct := 0; wrong := 0; skipped := 0
    answersOut := make([]dtos.AnswerResponse, 0, len(questions))
    for _, q := range questions {
        a, ok := ansByQ[q.ID.String()]
        var userAns *string
        var isCorr bool
        var tSpent *int
        if ok {
            userAns = a.UserAnswer; tSpent = a.TimeSpent
            if a.UserAnswer != nil { if *a.UserAnswer == q.CorrectAnswer { isCorr = true; correct++ } else { wrong++ } } else { skipped++ }
        } else { skipped++ }
        qresp := dtos.QuestionResponse{ID: q.ID, QuizID: q.QuizID, Text: q.Text, OptionA: q.OptionA, OptionB: q.OptionB, OptionC: q.OptionC, OptionD: q.OptionD, OptionE: q.OptionE, CorrectAnswer: q.CorrectAnswer, Explanation: q.Explanation, Subject: q.Subject, Year: q.Year, OrderIndex: q.OrderIndex, CreatedAt: q.CreatedAt, UpdatedAt: q.UpdatedAt}
        answersOut = append(answersOut, dtos.AnswerResponse{ID: uuid.New(), QuestionID: q.ID, UserAnswer: userAns, IsCorrect: isCorr, TimeSpent: tSpent, Question: qresp})
    }
    totalQ := len(questions)
    perc := 0.0
    if totalQ > 0 { perc = (float64(correct) / float64(totalQ)) * 100.0 }
    // create result
    uid := uuid.MustParse(userID)
    rid := uuid.New()
    now := time.Now()
    result := entities.QuizResult{Base: entities.Base{ID: rid}, UserID: &uid, QuizID: sess.QuizID, Score: correct, TotalQuestions: totalQ, Percentage: perc, TimeSpent: &totalTime, CompletedAt: now}
    if err := r.db.Create(&result).Error; err != nil { return nil, errors.New("Failed to save result") }
    // create answers
    for _, a := range answersOut {
        ar := entities.QuizAnswer{QuizResultID: &result.ID, QuestionID: &a.QuestionID, UserAnswer: a.UserAnswer, IsCorrect: a.IsCorrect, TimeSpent: a.TimeSpent}
        _ = r.db.Create(&ar).Error
    }
    // mark session finished and cleanup session answers
    _ = r.db.Model(&sess).Updates(map[string]interface{}{"is_finished": true, "ended_at": now}).Error
    _ = r.db.Where("session_id = ?", sess.ID).Delete(&entities.QuizSessionAnswer{}).Error
    // build response
    qresp, _ := r.getQuizResponse(&quiz, &userID)
    avgPerQ := 0.0; if totalQ > 0 { avgPerQ = float64(totalTime)/float64(totalQ) }
    // rank/total users (simplified)
    var totalUsers int64; r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quiz.ID).Distinct("user_id").Count(&totalUsers)
    resp := &dtos.QuizResultResponse{ID: result.ID, UserID: uid, QuizID: *sess.QuizID, Score: correct, TotalQuestions: totalQ, Percentage: perc, TimeSpent: &totalTime, CompletedAt: now, Answers: answersOut, Quiz: *qresp, CorrectAnswers: correct, WrongAnswers: wrong, SkippedAnswers: skipped, AverageTimePerQ: avgPerQ, TotalUsers: int(totalUsers), CreatedAt: result.CreatedAt}
    return resp, nil
}

func (r *repository) GetQuizResult(userID string, resultID string) (*dtos.QuizResultResponse, error) {
    var res entities.QuizResult
    if err := r.db.Where("id = ? AND user_id = ?", resultID, userID).First(&res).Error; err != nil { return nil, errors.New("Result not found") }
    // load quiz and quiz response
    var quiz entities.Quiz; _ = r.db.Where("id = ?", res.QuizID).First(&quiz).Error
    qresp, _ := r.getQuizResponse(&quiz, &userID)
    // load answers and questions
    var rows []entities.QuizAnswer
    r.db.Where("quiz_result_id = ?", res.ID).Find(&rows)
    out := make([]dtos.AnswerResponse, len(rows))
    totalTime := 0
    correct := 0; wrong := 0; skipped := 0
    for i, a := range rows {
        var q entities.Question; _ = r.db.Where("id = ?", a.QuestionID).First(&q).Error
        if a.TimeSpent != nil { totalTime += *a.TimeSpent }
        if a.UserAnswer == nil { skipped++ } else if *a.UserAnswer == q.CorrectAnswer { correct++ } else { wrong++ }
        qd := dtos.QuestionResponse{ID: q.ID, QuizID: q.QuizID, Text: q.Text, OptionA: q.OptionA, OptionB: q.OptionB, OptionC: q.OptionC, OptionD: q.OptionD, OptionE: q.OptionE, CorrectAnswer: q.CorrectAnswer, Explanation: q.Explanation, Subject: q.Subject, Year: q.Year, OrderIndex: q.OrderIndex, CreatedAt: q.CreatedAt, UpdatedAt: q.UpdatedAt}
        out[i] = dtos.AnswerResponse{ID: a.ID, QuestionID: q.ID, UserAnswer: a.UserAnswer, IsCorrect: a.IsCorrect, TimeSpent: a.TimeSpent, Question: qd, CreatedAt: a.CreatedAt}
    }
    avg := 0.0; if res.TotalQuestions > 0 { avg = float64(totalTime)/float64(res.TotalQuestions) }
    var totalUsers int64; r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", res.QuizID).Distinct("user_id").Count(&totalUsers)
    return &dtos.QuizResultResponse{ID: res.ID, UserID: *res.UserID, QuizID: *res.QuizID, Score: res.Score, TotalQuestions: res.TotalQuestions, Percentage: res.Percentage, TimeSpent: res.TimeSpent, CompletedAt: res.CompletedAt, Answers: out, Quiz: *qresp, CorrectAnswers: correct, WrongAnswers: wrong, SkippedAnswers: skipped, AverageTimePerQ: avg, TotalUsers: int(totalUsers), CreatedAt: res.CreatedAt}, nil
}

func (r *repository) GetUserQuizResults(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error) {
    page := payload.Page; if page < 1 { page = 1 }
    limit := payload.Limit; if limit < 1 || limit > 100 { limit = 20 }
    offset := (page-1)*limit
    q := r.db.Model(&entities.QuizResult{}).Where("user_id = ?", userID)
    if payload.QuizID != nil { q = q.Where("quiz_id = ?", *payload.QuizID) }
    if payload.MinScore != nil { q = q.Where("percentage >= ?", *payload.MinScore) }
    if payload.MaxScore != nil { q = q.Where("percentage <= ?", *payload.MaxScore) }
    sortBy := payload.SortBy; if sortBy == "" { sortBy = "completed_at" }
    order := "ASC"; if payload.SortDesc { order = "DESC" }
    q = q.Order(sortBy+" "+order)
    var total int64; q.Count(&total)
    var rows []entities.QuizResult
    if err := q.Offset(offset).Limit(limit).Find(&rows).Error; err != nil { return nil, errors.New("Failed to get results") }
    out := make([]dtos.QuizResultResponse, len(rows))
    var avgSum float64
    var best float64
    for i, rrow := range rows {
        resp, _ := r.GetQuizResult(userID, rrow.ID.String())
        out[i] = *resp
        avgSum += rrow.Percentage
        if rrow.Percentage > best { best = rrow.Percentage }
    }
    tp := int(math.Ceil(float64(total)/float64(limit)))
    avg := 0.0; if total > 0 { avg = avgSum/float64(total) }
    // total time spent sum
    var timeSum int
    for _, rrow := range rows { if rrow.TimeSpent != nil { timeSum += *rrow.TimeSpent } }
    return &dtos.QuizResultsResponse{Results: out, Total: int(total), Page: page, Limit: limit, TotalPages: tp, AverageScore: avg, BestScore: best, TotalQuizzes: len(out), TotalTimeSpent: timeSum}, nil
}

func (r *repository) GetQuizStatistics(quizID string) (*dtos.QuizStatisticsResponse, error) {
    var totalAttempts int64
    r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quizID).Count(&totalAttempts)
    var uniqueUsers int64
    r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quizID).Distinct("user_id").Count(&uniqueUsers)
    var avgScore float64
    r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quizID).Select("AVG(percentage)").Scan(&avgScore)
    return &dtos.QuizStatisticsResponse{QuizID: uuid.MustParse(quizID), TotalAttempts: int(totalAttempts), UniqueUsers: int(uniqueUsers), AverageScore: avgScore, CompletionRate: 0, QuestionStats: []dtos.QuestionStatistics{}}, nil
}

func (r *repository) GetQuizLeaderboard(quizID string, payload *dtos.GetLeaderboardRequest) (*dtos.QuizLeaderboardResponse, error) {
	// TODO: Implement quiz leaderboard
	return &dtos.QuizLeaderboardResponse{
		QuizID:  uuid.MustParse(quizID),
		Entries: []dtos.LeaderboardEntry{},
		Total:   0,
		Page:    1,
		Limit:   20,
	}, nil
}

func (r *repository) GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error) {
	// TODO: Implement quiz analytics
	return &dtos.QuizAnalyticsResponse{
		QuizID:             uuid.MustParse(quizID),
		Period:             "custom",
		StartDate:          payload.StartDate,
		EndDate:            payload.EndDate,
		TotalAttempts:      0,
		UniqueUsers:        0,
		AverageScore:       0.0,
		CompletionRate:     0.0,
		AverageTimeSpent:   0.0,
		QuestionAnalysis:   []dtos.QuestionAnalysisDetail{},
		ScoreDistribution:  []dtos.ScoreDistributionStat{},
		AttemptTimeline:    []dtos.AttemptTimelineStat{},
		DifficultyAnalysis: dtos.DifficultyAnalysisDetail{},
	}, nil
}

func (r *repository) ShareQuiz(userID string, quizID string, payload *dtos.ShareQuizRequest) error {
	// TODO: Implement quiz sharing
	return errors.New("Quiz sharing not implemented yet")
}

func (r *repository) InviteToQuiz(userID string, quizID string, payload *dtos.InviteToQuizRequest) error {
	// TODO: Implement quiz invitations
	return errors.New("Quiz invitations not implemented yet")
}

func (r *repository) GetSharedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement shared quiz retrieval
	return r.GetQuizList(payload)
}

func (r *repository) GetRecommendedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement quiz recommendations
	return r.GetQuizList(payload)
}

func (r *repository) GetSimilarQuizzes(quizID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement similar quiz recommendations
	return r.GetQuizList(payload)
}

func (r *repository) CreateQuizTimelineEntry(userID string, resultID string) error {
	// TODO: Create timeline entry for quiz completion
	return nil
}

func (r *repository) UpdateUserStatsFromQuiz(userID string, result *dtos.QuizResultResponse) error {
	// TODO: Update user statistics from quiz result
	return nil
}
