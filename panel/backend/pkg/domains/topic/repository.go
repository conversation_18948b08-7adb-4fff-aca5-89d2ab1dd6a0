package topic

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	CreateTopic(payload *dtos.CreateTopicRequest) (*dtos.TopicResponse, error)
	UpdateTopic(topicID string, payload *dtos.UpdateTopicRequest) (*dtos.TopicResponse, error)
	DeleteTopic(topicID string) error
	GetTopic(topicID string, userID *string) (*dtos.TopicResponse, error)
	GetTopics(payload *dtos.TopicListRequest, userID *string) (*dtos.TopicListResponse, error)
	UpdateProgress(userID string, payload *dtos.UpdateTopicProgressRequest) (*dtos.TopicProgress, error)
	GetProgress(userID string, topicID string) (*dtos.TopicProgress, error)
}

type repository struct{ db *gorm.DB }

func NewRepo(db *gorm.DB) Repository { return &repository{db: db} }

func (r *repository) CreateTopic(payload *dtos.CreateTopicRequest) (*dtos.TopicResponse, error) {
	topic := entities.Topic{
		Title:       payload.Title,
		Description: payload.Description,
		Body:        payload.Body,
		Subject:     payload.Subject,
		ParentID:    payload.ParentID,
		IsPublished: true,
	}
	if payload.IsPublished != nil {
		topic.IsPublished = *payload.IsPublished
	}
	if payload.Order != nil {
		topic.Order = *payload.Order
	}

	if err := r.db.Create(&topic).Error; err != nil {
		return nil, err
	}
	return toTopicResponse(r.db, &topic, nil)
}

func (r *repository) UpdateTopic(topicID string, payload *dtos.UpdateTopicRequest) (*dtos.TopicResponse, error) {
	var topic entities.Topic
	if err := r.db.First(&topic, "id = ?", topicID).Error; err != nil {
		return nil, err
	}
	updates := map[string]any{}
	if payload.Title != nil {
		updates["title"] = *payload.Title
	}
	if payload.Description != nil {
		updates["description"] = payload.Description
	}
	if payload.Body != nil {
		updates["body"] = payload.Body
	}
	if payload.Subject != nil {
		updates["subject"] = payload.Subject
	}
	if payload.ParentID != nil {
		updates["parent_id"] = payload.ParentID
	}
	if payload.Order != nil {
		updates["order"] = *payload.Order
	}
	if payload.IsPublished != nil {
		updates["is_published"] = *payload.IsPublished
	}
	if len(updates) > 0 {
		if err := r.db.Model(&topic).Updates(updates).Error; err != nil {
			return nil, err
		}
	}
	return toTopicResponse(r.db, &topic, nil)
}

func (r *repository) DeleteTopic(topicID string) error {
	// soft delete will cascade only if configured; we prevent delete when children exist
	var cnt int64
	if err := r.db.Model(&entities.Topic{}).Where("parent_id = ?", topicID).Count(&cnt).Error; err != nil {
		return err
	}
	if cnt > 0 {
		return errors.New("cannot delete topic with children")
	}
	return r.db.Delete(&entities.Topic{}, "id = ?", topicID).Error
}

func (r *repository) GetTopic(topicID string, userID *string) (*dtos.TopicResponse, error) {
	var topic entities.Topic
	if err := r.db.First(&topic, "id = ?", topicID).Error; err != nil {
		return nil, err
	}
	return toTopicResponse(r.db, &topic, userID)
}

func (r *repository) GetTopics(payload *dtos.TopicListRequest, userID *string) (*dtos.TopicListResponse, error) {
	page := payload.Page
	limit := payload.Limit
	if page == 0 {
		page = 1
	}
	if limit == 0 {
		limit = 20
	}
	offset := (page - 1) * limit

	query := r.db.Model(&entities.Topic{})
	if payload.ParentID != nil && *payload.ParentID != "" {
		query = query.Where("parent_id = ?", *payload.ParentID)
	} else if !payload.Tree {
		// default to roots when not tree
		query = query.Where("parent_id IS NULL")
	}
	if payload.Subject != nil {
		query = query.Where("subject = ?", *payload.Subject)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	var topics []entities.Topic
	if err := query.Order("order ASC, created_at ASC").Limit(limit).Offset(offset).Find(&topics).Error; err != nil {
		return nil, err
	}

	// Build response list or tree
	var resp []dtos.TopicResponse
	if payload.Tree {
		// Load all topics matching subject to build full tree under roots
		var all []entities.Topic
		tq := r.db.Model(&entities.Topic{})
		if payload.Subject != nil {
			tq = tq.Where("subject = ?", *payload.Subject)
		}
		if err := tq.Order("parent_id NULLS FIRST, order ASC, created_at ASC").Find(&all).Error; err != nil {
			return nil, err
		}
		resp = buildTopicTree(r.db, all, userID)
		total = int64(len(resp))
		limit = len(resp)
		page = 1
	} else {
		for i := range topics {
			tr, err := toTopicResponse(r.db, &topics[i], userID)
			if err != nil {
				return nil, err
			}
			resp = append(resp, *tr)
		}
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	return &dtos.TopicListResponse{
		Topics:     resp,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (r *repository) UpdateProgress(userID string, payload *dtos.UpdateTopicProgressRequest) (*dtos.TopicProgress, error) {
	uid, err := uuid.Parse(userID)
	if err != nil {
		return nil, err
	}
	// upsert user's progress
	var prog entities.UserTopicProgress
	err = r.db.Where("user_id = ? AND topic_id = ?", uid, payload.TopicID).First(&prog).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		prog = entities.UserTopicProgress{UserID: &uid, TopicID: &payload.TopicID}
		if err := r.db.Create(&prog).Error; err != nil {
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}

	updates := map[string]any{}
	completedAt := (*time.Time)(nil)
	if payload.Percentage != nil {
		pct := *payload.Percentage
		if pct < 0 {
			pct = 0
		}
		if pct > 100 {
			pct = 100
		}
		updates["percentage"] = pct
		if pct >= 100 {
			updates["is_completed"] = true
			t := time.Now()
			completedAt = &t
			updates["completed_at"] = completedAt
		}
	}
	if payload.IsCompleted != nil {
		updates["is_completed"] = *payload.IsCompleted
		if *payload.IsCompleted {
			t := time.Now()
			completedAt = &t
			updates["completed_at"] = completedAt
			updates["percentage"] = 100.0
		}
	}
	if len(updates) > 0 {
		if err := r.db.Model(&prog).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Recompute ancestors' aggregated progress
	if err := r.updateAncestorsProgress(uid, payload.TopicID); err != nil {
		return nil, err
	}

	return &dtos.TopicProgress{
		TopicID:     prog.TopicID,
		Percentage:  prog.Percentage,
		IsCompleted: prog.IsCompleted,
		CompletedAt: prog.CompletedAt,
	}, nil
}

func (r *repository) GetProgress(userID string, topicID string) (*dtos.TopicProgress, error) {
	uid, err := uuid.Parse(userID)
	if err != nil {
		return nil, err
	}
	tid, err := uuid.Parse(topicID)
	if err != nil {
		return nil, err
	}
	var prog entities.UserTopicProgress
	if err := r.db.Where("user_id = ? AND topic_id = ?", uid, tid).First(&prog).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// return empty progress
			return &dtos.TopicProgress{TopicID: &tid, Percentage: 0, IsCompleted: false, CompletedAt: nil}, nil
		}
		return nil, err
	}
	return &dtos.TopicProgress{
		TopicID:     prog.TopicID,
		Percentage:  prog.Percentage,
		IsCompleted: prog.IsCompleted,
		CompletedAt: prog.CompletedAt,
	}, nil
}

// Helpers
func toTopicResponse(db *gorm.DB, topic *entities.Topic, userID *string) (*dtos.TopicResponse, error) {
	var prog *dtos.TopicProgress
	if userID != nil {
		uid := *userID
		p, err := getTopicProgressForUser(db, uid, topic.ID)
		if err != nil {
			return nil, err
		}
		prog = p
	}
	// load children count and maybe children list minimal for single topic
	children, err := getChildren(db, topic.ID, userID)
	if err != nil {
		return nil, err
	}

	return &dtos.TopicResponse{
		ID:          topic.ID,
		Title:       topic.Title,
		Description: topic.Description,
		Body:        topic.Body,
		Subject:     topic.Subject,
		ParentID:    topic.ParentID,
		Order:       topic.Order,
		IsPublished: topic.IsPublished,
		Progress:    prog,
		Children:    children,
		CreatedAt:   topic.CreatedAt,
		UpdatedAt:   topic.UpdatedAt,
	}, nil
}

func getChildren(db *gorm.DB, parentID uuid.UUID, userID *string) ([]dtos.TopicResponse, error) {
	var items []entities.Topic
	if err := db.Where("parent_id = ?", parentID).Order("order ASC, created_at ASC").Find(&items).Error; err != nil {
		return nil, err
	}
	res := make([]dtos.TopicResponse, 0, len(items))
	for i := range items {
		tr, err := toTopicResponse(db, &items[i], userID)
		if err != nil {
			return nil, err
		}
		res = append(res, *tr)
	}
	return res, nil
}

func buildTopicTree(db *gorm.DB, items []entities.Topic, userID *string) []dtos.TopicResponse {
	byParent := map[string][]entities.Topic{}
	roots := []entities.Topic{}
	for _, t := range items {
		if t.ParentID == nil {
			roots = append(roots, t)
			continue
		}
		byParent[t.ParentID.String()] = append(byParent[t.ParentID.String()], t)
	}
	var makeNode func(entities.Topic) dtos.TopicResponse
	makeNode = func(t entities.Topic) dtos.TopicResponse {
		var prog *dtos.TopicProgress
		if userID != nil {
			p, _ := getTopicProgressForUser(db, *userID, t.ID)
			prog = p
		}
		children := []dtos.TopicResponse{}
		for _, c := range byParent[t.ID.String()] {
			children = append(children, makeNode(c))
		}
		return dtos.TopicResponse{
			ID:          t.ID,
			Title:       t.Title,
			Description: t.Description,
			Body:        t.Body,
			Subject:     t.Subject,
			ParentID:    t.ParentID,
			Order:       t.Order,
			IsPublished: t.IsPublished,
			Progress:    prog,
			Children:    children,
			CreatedAt:   t.CreatedAt,
			UpdatedAt:   t.UpdatedAt,
		}
	}
	res := make([]dtos.TopicResponse, 0, len(roots))
	for _, r := range roots {
		res = append(res, makeNode(r))
	}
	return res
}

func getTopicProgressForUser(db *gorm.DB, userID string, topicID uuid.UUID) (*dtos.TopicProgress, error) {
	uid, err := uuid.Parse(userID)
	if err != nil {
		return nil, err
	}
	var p entities.UserTopicProgress
	err = db.Where("user_id = ? AND topic_id = ?", uid, topicID).First(&p).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// derive from children if exist
		pct, done, cerr := aggregateFromChildren(db, uid, topicID)
		if cerr != nil {
			return nil, cerr
		}
		// Optionally store computed progress for faster next time
		return &dtos.TopicProgress{TopicID: &topicID, Percentage: pct, IsCompleted: done, CompletedAt: nil}, nil
	} else if err != nil {
		return nil, err
	}

	return &dtos.TopicProgress{TopicID: p.TopicID, Percentage: p.Percentage, IsCompleted: p.IsCompleted, CompletedAt: p.CompletedAt}, nil
}

// aggregateFromChildren computes progress as average of immediate children's progress
func aggregateFromChildren(db *gorm.DB, uid uuid.UUID, topicID uuid.UUID) (float64, bool, error) {
	var children []entities.Topic
	if err := db.Where("parent_id = ?", topicID).Find(&children).Error; err != nil {
		return 0, false, err
	}
	if len(children) == 0 {
		return 0, false, nil
	}
	total := 0.0
	completedCount := 0
	for _, ch := range children {
		var p entities.UserTopicProgress
		err := db.Where("user_id = ? AND topic_id = ?", uid, ch.ID).First(&p).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// try recursive aggregate of grandchildren
			pct, done, err2 := aggregateFromChildren(db, uid, ch.ID)
			if err2 != nil {
				return 0, false, err2
			}
			total += pct
			if done {
				completedCount++
			}
			continue
		} else if err != nil {
			return 0, false, err
		}
		total += p.Percentage
		if p.IsCompleted {
			completedCount++
		}
	}
	avg := total / float64(len(children))
	return avg, completedCount == len(children) && len(children) > 0, nil
}

// updateAncestorsProgress recomputes progress for all ancestors and upserts rows
func (r *repository) updateAncestorsProgress(uid uuid.UUID, topicID uuid.UUID) error {
	// find ancestors by traversing parent links
	current := &entities.Topic{}
	if err := r.db.First(current, "id = ?", topicID).Error; err != nil {
		return err
	}
	// walk up
	for current.ParentID != nil {
		parent := entities.Topic{}
		if err := r.db.First(&parent, "id = ?", *current.ParentID).Error; err != nil {
			return err
		}
		// aggregate from parent's children
		pct, done, err := aggregateFromChildren(r.db, uid, parent.ID)
		if err != nil {
			return err
		}
		// upsert progress row for parent
		var up entities.UserTopicProgress
		err = r.db.Where("user_id = ? AND topic_id = ?", uid, parent.ID).First(&up).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			up = entities.UserTopicProgress{UserID: &uid, TopicID: &parent.ID}
			if err := r.db.Create(&up).Error; err != nil {
				return err
			}
		} else if err != nil {
			return err
		}
		updates := map[string]any{"percentage": pct, "is_completed": done}
		if done {
			t := time.Now()
			updates["completed_at"] = &t
		}
		if err := r.db.Model(&up).Updates(updates).Error; err != nil {
			return err
		}

		current = &parent
	}
	return nil
}
