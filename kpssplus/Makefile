hot:
	npm install && cd ios && pod install && cd .. && npx react-native run-ios --simulator="iPhone 16 Pro"

hot-device:
	npm install && cd ios && pod install && cd .. && npx react-native run-ios --device

simulator:
	npx react-native run-ios --simulator="iPhone 15"

clean:
	cd ios && xcodebuild clean && cd .. && npx react-native start --reset-cache


<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Exam Prep Login</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
    :root {
      --primary-color: #1172d4;
      --secondary-color: #e7edf3;
      --text-primary: #0d141b;
      --text-secondary: #4c739a;
    }
    body {
      font-family: 'Inter', sans-serif;
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-white">
<div class="flex flex-col min-h-screen justify-center items-center px-4">
<div class="w-full max-w-sm">
<header class="text-center mb-10">
<div class="inline-flex items-center justify-center bg-[var(--primary-color)] rounded-full p-3 mb-4">
<span class="material-symbols-outlined text-white text-4xl">school</span>
</div>
<h1 class="text-[var(--text-primary)] text-3xl font-bold">Exam Prep</h1>
</header>
<main class="space-y-4">
<div class="space-y-3">
<input class="form-input w-full rounded-xl bg-[var(--secondary-color)] border-transparent focus:border-[var(--primary-color)] focus:ring-2 focus:ring-[var(--primary-color)] h-14 px-4 placeholder:text-[var(--text-secondary)] text-[var(--text-primary)] transition-all duration-300" placeholder="Username or Email" type="email"/>
<input class="form-input w-full rounded-xl bg-[var(--secondary-color)] border-transparent focus:border-[var(--primary-color)] focus:ring-2 focus:ring-[var(--primary-color)] h-14 px-4 placeholder:text-[var(--text-secondary)] text-[var(--text-primary)] transition-all duration-300" placeholder="Password" type="password"/>
</div>
<button class="w-full h-14 bg-[var(--primary-color)] text-white font-bold rounded-xl text-lg hover:bg-blue-700 transition-colors duration-300 shadow-lg shadow-blue-500/30">
          Login
        </button>
<div class="flex justify-between items-center text-sm">
<a class="text-[var(--text-secondary)] hover:text-[var(--primary-color)] font-medium transition-colors" href="#">Forgot password?</a>
<a class="text-[var(--text-secondary)] hover:text-[var(--primary-color)] font-medium transition-colors" href="#">Create a new account</a>
</div>
</main>
<footer class="mt-16">
<div class="relative flex py-5 items-center">
<div class="flex-grow border-t border-gray-300"></div>
<span class="flex-shrink mx-4 text-[var(--text-secondary)] text-sm">Or continue with</span>
<div class="flex-grow border-t border-gray-300"></div>
</div>
<div class="flex justify-center gap-4">
<button class="flex items-center justify-center w-full h-12 bg-white border border-gray-300 rounded-xl text-[var(--text-primary)] font-semibold hover:bg-gray-100 transition-colors duration-300">
<svg class="w-6 h-6 mr-2" viewBox="0 0 48 48"><path d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z" fill="#EA4335"></path><path d="M46.98 24.55c0-1.57-.15-3.09-.42-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z" fill="#4285F4"></path><path d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z" fill="#FBBC05"></path><path d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z" fill="#34A853"></path><path d="M0 0h48v48H0z" fill="none"></path></svg>
                Google
            </button>
<button class="flex items-center justify-center w-full h-12 bg-black border border-transparent rounded-xl text-white font-semibold hover:bg-gray-800 transition-colors duration-300">
<svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24"><path d="M17.22,3.84a4.43,4.43,0,0,0-3.32-1.53,4.41,4.41,0,0,0-3.41,1.72A10.82,10.82,0,0,0,8.73,8.4,4.3,4.3,0,0,0,7.31,12.2a4.43,4.43,0,0,0,3.33,1.52,4.42,4.42,0,0,0,3.41-1.72,10.71,10.71,0,0,0,1.75-4.43A4.33,4.33,0,0,0,17.22,3.84Zm-1,7.24a3.42,3.42,0,0,1-2.22,1.21A3.42,3.42,0,0,1,11.75,11a7.86,7.86,0,0,1-.58-3.32,8.44,8.44,0,0,1,.58-3.35,3.41,3.41,0,0,1,4.44,0,3.39,3.39,0,0,1,1.11,2.44A3.88,3.88,0,0,1,16.23,11.08Z"></path><path d="M12,0A12,12,0,1,0,24,12,12,12,0,0,0,12,0Zm7.69,18.35a12.82,12.82,0,0,1-1.39,2.24,1,1,0,0,1-1.39.29,10.51,10.51,0,0,1-6.19-2.73,10.84,10.84,0,0,1-2.91-5.23,1,1,0,0,1,.19-1,10.6,10.6,0,0,1,4.44-4.54,1,1,0,0,1,1.11.19A10.42,10.42,0,0,1,16,8.9a.87.87,0,0,1-.1.78,4.5,4.5,0,0,0-.87,3,4.32,4.32,0,0,0,3.13,4.31,1,1,0,0,1,.67,1.15A10.46,10.46,0,0,1,19.69,18.35Z"></path></svg>
                Apple
            </button>
</div>
</footer>
</div>
</div>

</body></html>