package entities

import (
	"time"

	"github.com/google/uuid"
)

// StudySession represents individual study sessions
type StudySession struct {
	Base
	UserID      *uuid.UUID `json:"user_id"`    // Optional user reference
	ContentID   *uuid.UUID `json:"content_id"` // Optional content reference
	QuizID      *uuid.UUID `json:"quiz_id"`    // Optional quiz reference
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`
	Duration    *int       `json:"duration"`     // Duration in seconds
	SessionType string     `json:"session_type"` // reading, video, quiz, practice

	// Progress made during this session
	ProgressBefore float64 `json:"progress_before"`
	ProgressAfter  float64 `json:"progress_after"`

	// Additional metadata
	DeviceType string         `json:"device_type"` // mobile, tablet, desktop
	Platform   string         `json:"platform"`    // ios, android, web
	Metadata   map[string]any `json:"metadata" gorm:"type:jsonb"`
}

// UserStats represents aggregated user statistics
type UserStats struct {
	Base
	UserID *uuid.UUID `json:"user_id"` // Optional user reference

	// Study statistics
	TotalStudyTime     int `json:"total_study_time"`     // Total study time in seconds
	StudyStreak        int `json:"study_streak"`         // Current study streak in days
	LongestStudyStreak int `json:"longest_study_streak"` // Longest study streak ever
	TotalStudySessions int `json:"total_study_sessions"` // Total number of study sessions

	// Content statistics
	BooksCompleted    int `json:"books_completed"`
	VideosWatched     int `json:"videos_watched"`
	QuestionsAnswered int `json:"questions_answered"`
	QuizzesCompleted  int `json:"quizzes_completed"`

	// Performance statistics
	AverageQuizScore    float64 `json:"average_quiz_score"`
	BestQuizScore       int     `json:"best_quiz_score"`
	TotalCorrectAnswers int     `json:"total_correct_answers"`
	TotalWrongAnswers   int     `json:"total_wrong_answers"`

	// Social statistics
	FriendsCount   int `json:"friends_count"`
	FollowersCount int `json:"followers_count"`
	FollowingCount int `json:"following_count"`
	BadgesEarned   int `json:"badges_earned"`

	// Ranking statistics
	GlobalRank  *int `json:"global_rank"`
	FriendsRank *int `json:"friends_rank"`
	WeeklyRank  *int `json:"weekly_rank"`
	MonthlyRank *int `json:"monthly_rank"`

	// Time-based statistics
	LastStudyDate    *time.Time `json:"last_study_date"`
	LastActiveDate   *time.Time `json:"last_active_date"`
	WeeklyStudyTime  int        `json:"weekly_study_time"`
	MonthlyStudyTime int        `json:"monthly_study_time"`
}

// DailyStats represents daily aggregated statistics
type DailyStats struct {
	Base
	UserID *uuid.UUID `json:"user_id"` // Optional user reference
	Date   time.Time  `json:"date"`

	// Daily study metrics
	StudyTime      int `json:"study_time"`      // Study time in seconds
	SessionsCount  int `json:"sessions_count"`  // Number of study sessions
	QuestionsCount int `json:"questions_count"` // Questions answered
	CorrectAnswers int `json:"correct_answers"` // Correct answers
	QuizzesCount   int `json:"quizzes_count"`   // Quizzes completed

	// Content progress
	BooksProgress  float64 `json:"books_progress"`  // Progress made on books
	VideosProgress float64 `json:"videos_progress"` // Progress made on videos

	// Social activity
	TimelineEntries  int `json:"timeline_entries"`  // Timeline entries created
	LikesGiven       int `json:"likes_given"`       // Likes given to others
	LikesReceived    int `json:"likes_received"`    // Likes received
	CommentsGiven    int `json:"comments_given"`    // Comments made
	CommentsReceived int `json:"comments_received"` // Comments received

}

// WeeklyReport represents weekly progress reports
type WeeklyReport struct {
	Base
	UserID    *uuid.UUID `json:"user_id"` // Optional user reference
	WeekStart time.Time  `json:"week_start"`
	WeekEnd   time.Time  `json:"week_end"`

	// Weekly summary
	TotalStudyTime   int     `json:"total_study_time"`
	TotalSessions    int     `json:"total_sessions"`
	TotalQuestions   int     `json:"total_questions"`
	AccuracyRate     float64 `json:"accuracy_rate"`
	CompletedContent int     `json:"completed_content"`

	// Goals and achievements
	WeeklyGoalMet   bool `json:"weekly_goal_met"`
	StudyStreakDays int  `json:"study_streak_days"`
	NewBadgesEarned int  `json:"new_badges_earned"`

	// Comparison with previous week
	StudyTimeChange int     `json:"study_time_change"` // Difference from previous week
	AccuracyChange  float64 `json:"accuracy_change"`   // Difference from previous week
	RankChange      int     `json:"rank_change"`       // Rank improvement/decline

	// Report data
	ReportData map[string]any `json:"report_data" gorm:"type:jsonb"`
}

func (StudySession) TableName() string {
	return "study_sessions"
}

func (UserStats) TableName() string {
	return "user_stats"
}

func (DailyStats) TableName() string {
	return "daily_stats"
}

func (WeeklyReport) TableName() string {
	return "weekly_reports"
}
