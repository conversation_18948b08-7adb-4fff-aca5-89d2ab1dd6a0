import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';

// Analytics types
export interface UserAnalyticsRequest {
  days?: number;
  metrics?: string[];
  granularity?: 'hour' | 'day' | 'week' | 'month';
}

export interface UserAnalyticsData {
  total_quizzes: number;
  average_score: number;
  current_streak: number;
  total_study_time: number;
  weekly_quizzes: number;
  score_change: number;
  weekly_study_time: number;
  daily_stats?: {
    date: string;
    quizzes_completed: number;
    average_score: number;
    study_time: number;
  }[];
}

export interface ContentAnalyticsRequest {
  content_id?: string;
  days?: number;
}

export interface ContentAnalyticsData {
  views: number;
  likes: number;
  bookmarks: number;
  completion_rate: number;
  average_rating: number;
  engagement_score: number;
}

export interface QuizAnalyticsRequest {
  quiz_id?: string;
  days?: number;
}

export interface QuizAnalyticsData {
  total_attempts: number;
  average_score: number;
  completion_rate: number;
  average_time: number;
  difficulty_rating: number;
  popularity_score: number;
}

export interface SystemAnalyticsData {
  active_users: number;
  total_sessions: number;
  average_session_duration: number;
  popular_content: any[];
  user_growth: number;
}

export const analyticsService = {
  // User analytics
  async getUserAnalytics(params?: UserAnalyticsRequest): Promise<ApiResponse<UserAnalyticsData>> {
    return http.get(endpoints.analytics.users, { params });
  },

  async getUserActivity(params?: { days?: number }): Promise<ApiResponse<any>> {
    return http.get(endpoints.analytics.userActivity, { params });
  },

  async getUserProgress(params?: { days?: number }): Promise<ApiResponse<any>> {
    return http.get(endpoints.analytics.userProgress, { params });
  },

  // Content analytics
  async getContentAnalytics(contentId: string, params?: ContentAnalyticsRequest): Promise<ApiResponse<ContentAnalyticsData>> {
    return http.get(endpoints.analytics.content, { 
      params: { content_id: contentId, ...params } 
    });
  },

  async getPopularContent(params?: { days?: number; limit?: number }): Promise<ApiResponse<any[]>> {
    return http.get(endpoints.analytics.contentPopular, { params });
  },

  async getContentEngagement(params?: { content_id?: string; days?: number }): Promise<ApiResponse<any>> {
    return http.get(endpoints.analytics.contentEngagement, { params });
  },

  // Quiz analytics
  async getQuizAnalytics(quizId: string, params?: QuizAnalyticsRequest): Promise<ApiResponse<QuizAnalyticsData>> {
    return http.get(endpoints.analytics.quizzes, { 
      params: { quiz_id: quizId, ...params } 
    });
  },

  async getQuizPerformance(params?: { quiz_id?: string; days?: number }): Promise<ApiResponse<any>> {
    return http.get(endpoints.analytics.quizPerformance, { params });
  },

  async getQuizCompletion(params?: { quiz_id?: string; days?: number }): Promise<ApiResponse<any>> {
    return http.get(endpoints.analytics.quizCompletion, { params });
  },

  // System analytics
  async getSystemAnalytics(params?: { days?: number }): Promise<ApiResponse<SystemAnalyticsData>> {
    return http.get(endpoints.analytics.system, { params });
  },

  async getSystemUsage(params?: { days?: number }): Promise<ApiResponse<any>> {
    return http.get(endpoints.analytics.systemUsage, { params });
  }
};

export default analyticsService;
