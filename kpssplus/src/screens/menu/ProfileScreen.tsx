import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  // Alert,
  Switch,
  Image,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
// import {Button, Input, Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth, useTokens} from '../../hooks';

export const ProfileScreen: React.FC = () => {
  const {user, isAuthenticated} = useAuth();
  const {balance: tokenBalance} = useTokens();
  const [,] = useState(false);
  const [settings, setSettings] = useState({
    soundEnabled: true,
    vibrationEnabled: true,
    notificationsEnabled: true,
  });
  const [,] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    username: user?.username || '',
    email: user?.email || '',
  });

  // const handleSave = async () => {
  //   try {
  //     // Here you would typically call an API to update user profile
  //     Alert.alert('Başarılı', 'Profil bilgileri güncellendi', [
  //       {text: 'Tamam', onPress: () => setIsEditing(false)},
  //     ]);
  //     await refreshUser();
  //   } catch (error) {
  //     Alert.alert('Hata', 'Profil güncellenirken bir hata oluştu');
  //   }
  // };

  // const handleCancel = () => {
  //   setEditForm({
  //     firstName: user?.firstName || '',
  //     lastName: user?.lastName || '',
  //     username: user?.username || '',
  //     email: user?.email || '',
  //   });
  //   setIsEditing(false);
  // };

  if (!isAuthenticated || !user) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <View style={styles.emptyState}>
          <Icon name="person-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.emptyTitle}>Giriş Yapın</Text>
          <Text style={styles.emptyDescription}>
            Profil bilgilerinizi görmek için giriş yapmanız gerekiyor
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.profileInfo}>
            <View style={styles.avatarContainer}>
              {user.avatar ? (
                <Image source={{uri: user.avatar}} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Icon name="person" size={32} color={colors.white} />
                </View>
              )}
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>
                {user.firstName || user.username || 'Kullanıcı'}
              </Text>
              <Text style={styles.userEmail}>{user.email}</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.editProfileButton}>
            <Icon name="pencil" size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Token Balance */}
        <View style={styles.tokenCard}>
          <View style={styles.tokenInfo}>
            <Icon name="diamond" size={24} color={colors.primary} />
            <Text style={styles.tokenBalance}>{tokenBalance}</Text>
            <Text style={styles.tokenLabel}>Jeton</Text>
          </View>
          <TouchableOpacity style={styles.buyTokenButton}>
            <Text style={styles.buyTokenText}>Jeton Al</Text>
          </TouchableOpacity>
        </View>

        {/* Settings Menu */}
        <View style={styles.settingsContainer}>
          {/* Rozetler */}
          <TouchableOpacity style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Icon name="ribbon" size={24} color={colors.primary} />
              <Text style={styles.menuItemText}>Rozetler</Text>
            </View>
            <Icon
              name="chevron-forward"
              size={20}
              color={colors.text.secondary}
            />
          </TouchableOpacity>

          {/* Kasa Kazan */}
          <TouchableOpacity style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Icon name="wallet" size={24} color={colors.primary} />
              <Text style={styles.menuItemText}>Kasa Kazan</Text>
            </View>
            <Icon
              name="chevron-forward"
              size={20}
              color={colors.text.secondary}
            />
          </TouchableOpacity>

          {/* İstatistik */}
          <TouchableOpacity style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Icon name="bar-chart" size={24} color={colors.primary} />
              <Text style={styles.menuItemText}>İstatistik</Text>
            </View>
            <Icon
              name="chevron-forward"
              size={20}
              color={colors.text.secondary}
            />
          </TouchableOpacity>

          {/* Ses */}
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Icon name="volume-high" size={24} color={colors.primary} />
              <Text style={styles.menuItemText}>Ses</Text>
            </View>
            <Switch
              value={settings.soundEnabled}
              onValueChange={value =>
                setSettings(prev => ({...prev, soundEnabled: value}))
              }
              trackColor={{false: colors.gray[300], true: colors.primary}}
              thumbColor={colors.white}
            />
          </View>

          {/* Titreşim */}
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Icon name="phone-portrait" size={24} color={colors.primary} />
              <Text style={styles.menuItemText}>Titreşim</Text>
            </View>
            <Switch
              value={settings.vibrationEnabled}
              onValueChange={value =>
                setSettings(prev => ({...prev, vibrationEnabled: value}))
              }
              trackColor={{false: colors.gray[300], true: colors.primary}}
              thumbColor={colors.white}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
  },
  emptyTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  // Profile Header
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    padding: spacing[4],
    borderRadius: 12,
    marginBottom: spacing[4],
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: spacing[3],
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...typography.h6,
    color: colors.text.primary,
    marginBottom: spacing[1],
  },
  userEmail: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  editProfileButton: {
    padding: spacing[2],
    backgroundColor: colors.primaryMuted,
    borderRadius: 8,
  },
  // Token Card
  tokenCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    padding: spacing[4],
    borderRadius: 12,
    marginBottom: spacing[4],
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tokenInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenBalance: {
    ...typography.h5,
    color: colors.primary,
    marginLeft: spacing[2],
    marginRight: spacing[1],
    fontWeight: 'bold',
  },
  tokenLabel: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  buyTokenButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: 8,
  },
  buyTokenText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  // Settings Container
  settingsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: spacing[4],
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemText: {
    ...typography.body1,
    color: colors.text.primary,
    marginLeft: spacing[3],
  },
});
