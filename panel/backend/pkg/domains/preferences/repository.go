package preferences

import (
    "encoding/json"
    "errors"
    "strings"

    "github.com/google/uuid"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/entities"
    "gorm.io/gorm"
)

type Repository interface {
    Get(userID string) (*dtos.PreferencesResponse, error)
    Upsert(userID string, req *dtos.UpdatePreferencesRequest) (*dtos.PreferencesResponse, error)
}

type repository struct{ db *gorm.DB }

func NewRepo(db *gorm.DB) Repository { return &repository{db: db} }

func (r *repository) Get(userID string) (*dtos.PreferencesResponse, error) {
    var rec entities.UserPreferences
    // if userID empty (guest), return defaults
    if strings.TrimSpace(userID) == "" {
        any := "Any"
        return &dtos.PreferencesResponse{Interests: []string{}, SubjectWeights: map[string]float64{}, DifficultyPref: &any}, nil
    }
    err := r.db.Where("user_id = ?", userID).First(&rec).Error
    if errors.Is(err, gorm.ErrRecordNotFound) {
        any := "Any"
        return &dtos.PreferencesResponse{Interests: []string{}, SubjectWeights: map[string]float64{}, DifficultyPref: &any}, nil
    } else if err != nil {
        return nil, err
    }
    var interests []string
    if rec.Interests != nil {
        // store as comma-separated
        if *rec.Interests == "" { interests = []string{} } else { interests = strings.Split(*rec.Interests, ",") }
    }
    weights := map[string]float64{}
    if rec.SubjectWeights != nil && *rec.SubjectWeights != "" {
        _ = json.Unmarshal([]byte(*rec.SubjectWeights), &weights)
    }
    return &dtos.PreferencesResponse{Interests: interests, SubjectWeights: weights, DifficultyPref: rec.DifficultyPref}, nil
}

func (r *repository) Upsert(userID string, req *dtos.UpdatePreferencesRequest) (*dtos.PreferencesResponse, error) {
    if strings.TrimSpace(userID) == "" {
        return nil, errors.New("authentication required")
    }
    uid := uuid.MustParse(userID)
    interestsStr := strings.Join(req.Interests, ",")
    weightsBytes, _ := json.Marshal(req.SubjectWeights)
    weightsStr := string(weightsBytes)
    var rec entities.UserPreferences
    err := r.db.Where("user_id = ?", uid).First(&rec).Error
    if errors.Is(err, gorm.ErrRecordNotFound) {
        rec = entities.UserPreferences{UserID: &uid, Interests: &interestsStr, SubjectWeights: &weightsStr, DifficultyPref: req.DifficultyPref}
        if err := r.db.Create(&rec).Error; err != nil { return nil, err }
    } else if err == nil {
        updates := map[string]interface{}{"interests": interestsStr, "subject_weights": weightsStr}
        if req.DifficultyPref != nil { updates["difficulty_pref"] = *req.DifficultyPref }
        if err := r.db.Model(&rec).Updates(updates).Error; err != nil { return nil, err }
    } else {
        return nil, err
    }
    return r.Get(userID)
}

