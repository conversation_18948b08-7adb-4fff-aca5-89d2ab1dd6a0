package entities

import (
	"time"

	"github.com/google/uuid"
)

// QuizType represents different types of quizzes
type QuizType string

const (
	QuizTypePersonal QuizType = "personal"
	QuizTypeShared   QuizType = "shared"
	QuizTypeOfficial QuizType = "official"
)

// Quiz represents a quiz created by users or admins
type Quiz struct {
	Base
	Title       string     `json:"title" gorm:"not null"`
	Description *string    `json:"description"`
	Type        QuizType   `json:"type" gorm:"type:varchar(20);not null;default:'personal'"`
	Subject     *string    `json:"subject"`    // KPSS subject area
	Difficulty  *string    `json:"difficulty"` // Easy, Medium, Hard
	TimeLimit   *int       `json:"time_limit"` // Time limit in seconds
	IsPublic    bool       `json:"is_public" gorm:"default:false"`
	IsActive    bool       `json:"is_active" gorm:"default:true"`
	CreatorID   *uuid.UUID `json:"creator_id"` // Optional creator reference
}

// Question represents individual questions in quizzes
type Question struct {
	Base
	QuizID        *uuid.UUID `json:"quiz_id"` // Optional quiz reference
	Text          string     `json:"text"`
	OptionA       string     `json:"option_a"`
	OptionB       string     `json:"option_b"`
	OptionC       string     `json:"option_c"`
	OptionD       string     `json:"option_d"`
	OptionE       *string    `json:"option_e"`       // Optional 5th option
	CorrectAnswer string     `json:"correct_answer"` // A, B, C, D, E
	Explanation   *string    `json:"explanation"`
	Subject       *string    `json:"subject"`
	Year          *int       `json:"year"` // KPSS year if it's from past exams
	OrderIndex    int        `json:"order_index"`
}

// QuizResult represents user results for quizzes
type QuizResult struct {
	Base
	UserID         *uuid.UUID `json:"user_id"` // Optional user reference
	QuizID         *uuid.UUID `json:"quiz_id"` // Optional quiz reference
	Score          int        `json:"score"`   // Number of correct answers
	TotalQuestions int        `json:"total_questions"`
	Percentage     float64    `json:"percentage"`
	TimeSpent      *int       `json:"time_spent"` // Time spent in seconds
	CompletedAt    time.Time  `json:"completed_at"`
}

// QuizAnswer represents individual answers in a quiz result
type QuizAnswer struct {
	Base
	QuizResultID *uuid.UUID `json:"quiz_result_id"` // Optional quiz result reference
	QuestionID   *uuid.UUID `json:"question_id"`    // Optional question reference
	UserAnswer   *string    `json:"user_answer"`    // A, B, C, D, E or null if not answered
	IsCorrect    bool       `json:"is_correct"`
	TimeSpent    *int       `json:"time_spent"` // Time spent on this question in seconds
}

func (Quiz) TableName() string {
	return "quizzes"
}

func (Question) TableName() string {
	return "questions"
}

func (QuizResult) TableName() string {
	return "quiz_results"
}

func (QuizAnswer) TableName() string {
	return "quiz_answers"
}
