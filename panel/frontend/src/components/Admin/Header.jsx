
import { useNavigate } from 'react-router-dom';

const Header = () => {
  const navigate = useNavigate();
  
  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    navigate('/admin');
  };

  const adminUser = localStorage.getItem('adminUser') ? JSON.parse(localStorage.getItem('adminUser')) : {};

  return (
    <div className="header">
      <h1>Welcome, {adminUser.name || 'Admin'}</h1>
      <button className="logout-btn" onClick={handleLogout}>
        Logout
      </button>
    </div>
  );
};

export { Header };