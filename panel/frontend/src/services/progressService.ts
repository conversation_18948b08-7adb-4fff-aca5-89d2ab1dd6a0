import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';

export interface ProgressStats {
  total_study_time: number; // seconds
  quizzes_completed: number;
  average_score: number;
  current_streak: number;
  longest_streak: number;
  total_points: number;
  level: number;
  experience_points: number;
  next_level_points: number;
}

export interface StudySession {
  id: string;
  content_id?: string;
  content_type: 'quiz' | 'content' | 'topic';
  started_at: string;
  ended_at?: string;
  duration: number; // seconds
  points_earned: number;
  activities: string[];
}

export interface ProgressGoal {
  id: string;
  type: 'daily' | 'weekly' | 'monthly';
  target_type: 'study_time' | 'quizzes' | 'points' | 'streak';
  target_value: number;
  current_value: number;
  deadline: string;
  is_achieved: boolean;
  created_at: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'quiz' | 'study' | 'social' | 'milestone';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
  unlocked_at?: string;
  progress?: number;
  max_progress?: number;
}

export interface ProgressHistory {
  date: string;
  study_time: number;
  quizzes_completed: number;
  points_earned: number;
  activities: string[];
}

export interface ContentProgress {
  content_id: string;
  content_type: 'quiz' | 'content' | 'topic';
  progress_percentage: number;
  time_spent: number;
  last_accessed: string;
  is_completed: boolean;
  score?: number;
}

export interface UpdateProgressRequest {
  content_id: string;
  content_type: 'quiz' | 'content' | 'topic';
  progress_percentage: number;
  time_spent: number;
  is_completed?: boolean;
  score?: number;
}

export interface CreateGoalRequest {
  type: 'daily' | 'weekly' | 'monthly';
  target_type: 'study_time' | 'quizzes' | 'points' | 'streak';
  target_value: number;
  deadline: string;
}

export interface StartSessionRequest {
  content_id?: string;
  content_type: 'quiz' | 'content' | 'topic';
}

export const progressService = {
  // Get user progress stats
  async getProgressStats(): Promise<ApiResponse<ProgressStats>> {
    return http.get(endpoints.progress.stats);
  },

  // Get progress history
  async getProgressHistory(params?: { 
    start_date?: string; 
    end_date?: string; 
    limit?: number 
  }): Promise<ApiResponse<ProgressHistory[]>> {
    return http.get(endpoints.progress.history, { params });
  },

  // Get study streak
  async getStudyStreak(): Promise<ApiResponse<{ current_streak: number; longest_streak: number }>> {
    return http.get(endpoints.progress.streak);
  },

  // Get all user progress
  async getAllProgress(params?: { 
    content_type?: string; 
    page?: number; 
    limit?: number 
  }): Promise<ApiResponse<ContentProgress[]>> {
    return http.get(endpoints.progress.all, { params });
  },

  // Get content progress
  async getContentProgress(contentId: string): Promise<ApiResponse<ContentProgress>> {
    return http.get(endpoints.progress.content(contentId));
  },

  // Update progress
  async updateProgress(data: UpdateProgressRequest): Promise<ApiResponse<ContentProgress>> {
    return http.put(endpoints.progress.update, data);
  },

  // Study sessions
  async getStudySessions(params?: { 
    page?: number; 
    limit?: number; 
    start_date?: string; 
    end_date?: string 
  }): Promise<ApiResponse<StudySession[]>> {
    return http.get(endpoints.progress.sessions, { params });
  },

  async startStudySession(data: StartSessionRequest): Promise<ApiResponse<StudySession>> {
    return http.post(endpoints.progress.startSession, data);
  },

  async endStudySession(sessionId: string): Promise<ApiResponse<StudySession>> {
    return http.put(endpoints.progress.endSession(sessionId));
  },

  // Goals
  async getProgressGoals(): Promise<ApiResponse<ProgressGoal[]>> {
    return http.get(endpoints.progress.goals);
  },

  async createGoal(data: CreateGoalRequest): Promise<ApiResponse<ProgressGoal>> {
    return http.post(endpoints.progress.createGoal, data);
  },

  async updateGoal(goalId: string, data: Partial<CreateGoalRequest>): Promise<ApiResponse<ProgressGoal>> {
    return http.put(endpoints.progress.updateGoal(goalId), data);
  },

  async deleteGoal(goalId: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.progress.deleteGoal(goalId));
  },

  // Achievements
  async getAchievements(): Promise<ApiResponse<Achievement[]>> {
    return http.get(endpoints.progress.achievements);
  }
};

export default progressService;
