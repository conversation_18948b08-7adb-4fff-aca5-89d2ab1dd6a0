import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Trophy, 
  Clock, 
  Target, 
  TrendingUp,
  CheckCircle,
  XCircle,
  RotateCcw,
  Share2,
  Download,
  ArrowLeft,
  Award,
  Star
} from 'lucide-react';
import { QuizResult, Achievement } from '../../types/quiz';
import { quizSessionService } from '../../services/quizSessionService';

const QuizResults: React.FC = () => {
  const { quizId, resultId } = useParams<{ quizId: string; resultId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  
  const [result, setResult] = useState<QuizResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAnswers, setShowAnswers] = useState(false);

  useEffect(() => {
    if (resultId) {
      loadResult();
    }
  }, [resultId]);

  const loadResult = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock data for demonstration
      const mockResult: QuizResult = {
        id: resultId!,
        session_id: resultId!,
        quiz_id: '1',
        user_id: '1',
        score: 85,
        percentage: 85,
        total_questions: 20,
        correct_answers: 17,
        time_taken: 1200,
        completed_at: '2024-01-01',
        answers: [
          {
            question_id: '1',
            selected_answer: 'A',
            is_correct: true,
            time_taken: 30,
            answered_at: '2024-01-01'
          }
        ],
        performance_analysis: {
          strengths: [
            'Good accuracy in mathematics',
            'Consistent performance'
          ],
          weaknesses: [
            'Time management needs improvement',
            'Some conceptual gaps'
          ],
          subject_breakdown: [
            {
              subject: 'Mathematics',
              correct: 8,
              total: 10,
              percentage: 80
            }
          ],
          difficulty_breakdown: [
            {
              difficulty: 'medium',
              correct: 17,
              total: 20,
              percentage: 85
            }
          ],
          time_analysis: {
            average_time_per_question: 60,
            fastest_question: 15,
            slowest_question: 120,
            time_efficiency: 85
          },
          improvement_suggestions: [
            'Focus on time management',
            'Review basic concepts'
          ]
        }
      };

      setResult(mockResult);
    } catch (err) {
      setError('An error occurred while loading results');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getScoreColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-400';
    if (percentage >= 70) return 'text-yellow-400';
    if (percentage >= 50) return 'text-orange-400';
    return 'text-red-400';
  };

  const getScoreMessage = (percentage: number) => {
    if (percentage >= 90) return 'Excellent! Outstanding performance!';
    if (percentage >= 70) return 'Great job! Well done!';
    if (percentage >= 50) return 'Good effort! Keep practicing!';
    return 'Keep studying and try again!';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-2">Error loading results</div>
        <p className="text-dark-400">{error}</p>
        <button
          onClick={loadResult}
          className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Back Button */}
      <button
        onClick={() => navigate(`/quizzes/${quizId}`)}
        className="flex items-center gap-2 text-dark-300 hover:text-white transition-colors"
      >
        <ArrowLeft size={20} />
        Back to Quiz
      </button>

      {/* Results Header */}
      <div className="bg-dark-800 rounded-xl p-8 border border-dark-700 text-center">
        <div className="mb-6">
          <Trophy className={`mx-auto mb-4 ${getScoreColor(result.percentage)}`} size={64} />
          <h1 className="text-3xl font-bold text-white mb-2">{t('quiz.quizCompleted')}</h1>
          <p className="text-dark-300">{getScoreMessage(result.percentage)}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-dark-700 rounded-lg p-4">
            <div className={`text-3xl font-bold mb-1 ${getScoreColor(result.percentage)}`}>
              {result.percentage}%
            </div>
            <div className="text-dark-400 text-sm">Final Score</div>
          </div>
          
          <div className="bg-dark-700 rounded-lg p-4">
            <div className="text-3xl font-bold text-white mb-1">
              {result.correct_answers}/{result.total_questions}
            </div>
            <div className="text-dark-400 text-sm">Correct Answers</div>
          </div>
          
          <div className="bg-dark-700 rounded-lg p-4">
            <div className="text-3xl font-bold text-white mb-1">
              {formatTime(result.time_taken)}
            </div>
            <div className="text-dark-400 text-sm">Time Taken</div>
          </div>
          
          <div className="bg-dark-700 rounded-lg p-4">
            <div className="text-3xl font-bold text-primary-400 mb-1">
              {result.score}
            </div>
            <div className="text-dark-400 text-sm">Points Earned</div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-wrap justify-center gap-3 mt-6">
          <Link
            to={`/quizzes/${quizId}`}
            className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <RotateCcw size={16} />
            Retake Quiz
          </Link>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-dark-700 hover:bg-dark-600 text-white rounded-lg transition-colors">
            <Share2 size={16} />
            Share Results
          </button>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-dark-700 hover:bg-dark-600 text-white rounded-lg transition-colors">
            <Download size={16} />
            Download Report
          </button>
        </div>
      </div>

      {/* Achievements */}
      {result.achievements && result.achievements.length > 0 && (
        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            <Award className="text-yellow-400" size={24} />
            New Achievements
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {result.achievements.map((achievement) => (
              <div key={achievement.id} className="bg-dark-700 rounded-lg p-4 border border-yellow-400/20">
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div>
                    <h3 className="text-white font-semibold">{achievement.name}</h3>
                    <p className="text-dark-300 text-sm">{achievement.description}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        achievement.rarity === 'legendary' ? 'bg-purple-500/20 text-purple-400' :
                        achievement.rarity === 'epic' ? 'bg-orange-500/20 text-orange-400' :
                        achievement.rarity === 'rare' ? 'bg-blue-500/20 text-blue-400' :
                        'bg-gray-500/20 text-gray-400'
                      }`}>
                        {achievement.rarity}
                      </span>
                      <span className="text-yellow-400 text-sm">+{achievement.points} points</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Analysis */}
      {result.performance_analysis && (
        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
            <TrendingUp className="text-primary-400" size={24} />
            Performance Analysis
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Subject Breakdown */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Subject Performance</h3>
              <div className="space-y-3">
                {result.performance_analysis.subject_breakdown.map((subject, index) => (
                  <div key={index} className="bg-dark-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">{subject.subject}</span>
                      <span className={`font-bold ${getScoreColor(subject.percentage)}`}>
                        {subject.percentage}%
                      </span>
                    </div>
                    <div className="w-full bg-dark-600 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          subject.percentage >= 70 ? 'bg-green-500' :
                          subject.percentage >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${subject.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-dark-400 text-sm mt-1">
                      {subject.correct}/{subject.total} correct
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Difficulty Breakdown */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Difficulty Performance</h3>
              <div className="space-y-3">
                {result.performance_analysis.difficulty_breakdown.map((difficulty, index) => (
                  <div key={index} className="bg-dark-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium capitalize">{difficulty.difficulty}</span>
                      <span className={`font-bold ${getScoreColor(difficulty.percentage)}`}>
                        {difficulty.percentage}%
                      </span>
                    </div>
                    <div className="w-full bg-dark-600 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          difficulty.percentage >= 70 ? 'bg-green-500' :
                          difficulty.percentage >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${difficulty.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-dark-400 text-sm mt-1">
                      {difficulty.correct}/{difficulty.total} correct
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Strengths and Weaknesses */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <CheckCircle className="text-green-400" size={20} />
                Strengths
              </h3>
              <div className="space-y-2">
                {result.performance_analysis.strengths.map((strength, index) => (
                  <div key={index} className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                    <span className="text-green-400">{strength}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Target className="text-red-400" size={20} />
                Areas to Improve
              </h3>
              <div className="space-y-2">
                {result.performance_analysis.weaknesses.map((weakness, index) => (
                  <div key={index} className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                    <span className="text-red-400">{weakness}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Improvement Suggestions */}
          {result.performance_analysis.improvement_suggestions.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-white mb-4">Improvement Suggestions</h3>
              <div className="bg-dark-700 rounded-lg p-4">
                <ul className="space-y-2">
                  {result.performance_analysis.improvement_suggestions.map((suggestion, index) => (
                    <li key={index} className="text-dark-300 flex items-start gap-2">
                      <Star className="text-primary-400 mt-0.5 flex-shrink-0" size={16} />
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Answer Review */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">Answer Review</h2>
          <button
            onClick={() => setShowAnswers(!showAnswers)}
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            {showAnswers ? 'Hide Answers' : 'Show Answers'}
          </button>
        </div>

        {showAnswers && (
          <div className="space-y-4">
            {result.answers.map((answer, index) => (
              <div key={answer.question_id} className="bg-dark-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-white font-medium">Question {index + 1}</span>
                  {answer.is_correct ? (
                    <CheckCircle className="text-green-400" size={20} />
                  ) : (
                    <XCircle className="text-red-400" size={20} />
                  )}
                  <span className="text-dark-400 text-sm">
                    {formatTime(answer.time_taken)}
                  </span>
                </div>
                
                <div className="text-dark-300 mb-2">
                  Your answer: <span className={answer.is_correct ? 'text-green-400' : 'text-red-400'}>
                    {Array.isArray(answer.selected_answer) ? answer.selected_answer.join(', ') : answer.selected_answer}
                  </span>
                </div>
                
                {!answer.is_correct && (
                  <div className="text-dark-300">
                    Correct answer: <span className="text-green-400">
                      {/* This would need to be fetched from the question data */}
                      [Correct answer would be shown here]
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default QuizResults;
