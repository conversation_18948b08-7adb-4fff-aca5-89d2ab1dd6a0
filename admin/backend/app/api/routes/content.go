package routes

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "github.com/google/uuid"
    "github.com/nocytech/kpss-plus-admin/pkg/database"
    "github.com/nocytech/kpss-plus-admin/pkg/domains/content"
    "github.com/nocytech/kpss-plus-admin/pkg/entities"
)

func ContentRoutes(r *gin.RouterGroup, contentService content.Service) {
	contents := r.Group("/contents")
	{
		contents.GET("", GetContents(contentService))
		contents.GET("/:id", GetContent(contentService))
		contents.POST("", CreateContent(contentService))
		contents.PUT("/:id", UpdateContent(contentService))
		contents.DELETE("/:id", DeleteContent(contentService))
		contents.PATCH("/:id/publish", PublishContent(contentService))
		contents.PATCH("/:id/unpublish", UnpublishContent(contentService))
	}
}

func GetContents(s content.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		limitStr := c.DefaultQuery("limit", "20")
		offsetStr := c.DefaultQuery("offset", "0")
		search := c.Query("q")
		topicIDStr := c.Query("topic_id")
		contentType := c.Query("type")

		limit, _ := strconv.Atoi(limitStr)
		offset, _ := strconv.Atoi(offsetStr)

		if topicIDStr != "" {
			topicID, err := uuid.Parse(topicIDStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid topic_id"})
				return
			}
			contents, err := s.GetByTopicID(topicID)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"data": contents, "total": len(contents)})
			return
		}

		if contentType != "" {
			contents, err := s.GetByType(contentType)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"data": contents, "total": len(contents)})
			return
		}

		contents, total, err := s.GetAll(limit, offset, search)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": contents, "total": total})
	}
}

func GetContent(s content.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		content, err := s.GetByID(id)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Content not found"})
			return
		}
		c.JSON(http.StatusOK, content)
	}
}

func CreateContent(s content.Service) gin.HandlerFunc {
    return func(c *gin.Context) {
        var body entities.Content
        if err := c.ShouldBindJSON(&body); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
            return
        }
        // Safe insert: only include commonly available columns
        db := database.DB()
        data := map[string]any{
            "id":          uuid.New(),
            "title":       body.Title,
            "type":        body.Type,
            "description": body.Description,
        }
        if err := db.Model(&entities.Content{}).Create(data).Error; err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
        c.JSON(http.StatusCreated, data)
    }
}

func UpdateContent(s content.Service) gin.HandlerFunc {
    return func(c *gin.Context) {
        id, err := uuid.Parse(c.Param("id"))
        if err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
            return
        }

        var body entities.Content
        if err := c.ShouldBindJSON(&body); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
            return
        }
        updates := map[string]any{}
        if body.Title != "" { updates["title"] = body.Title }
        if body.Description != nil { updates["description"] = body.Description }
        if body.Type != "" { updates["type"] = body.Type }

        if len(updates) == 0 { c.JSON(http.StatusOK, gin.H{"message":"no changes"}); return }
        if err := database.DB().Model(&entities.Content{}).Where("id = ?", id).Updates(updates).Error; err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
        c.JSON(http.StatusOK, gin.H{"id": id, "updated": true})
    }
}

func DeleteContent(s content.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		if err := s.Delete(id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Content deleted successfully"})
	}
}

func PublishContent(s content.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		if err := s.Publish(id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Content published successfully"})
	}
}

func UnpublishContent(s content.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
			return
		}

		if err := s.Unpublish(id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Content unpublished successfully"})
	}
}
