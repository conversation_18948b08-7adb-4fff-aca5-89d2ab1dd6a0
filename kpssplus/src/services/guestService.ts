// import {apiClient} from './api';
import {Content, Quiz, Subject} from '../types';

export class GuestService {
  // Get demo subjects for guest users
  async getDemoSubjects(): Promise<Subject[]> {
    // Mock demo subjects - in real app this would come from API
    return [
      {
        id: '1',
        name: 'Türk<PERSON>e',
        icon: '📚',
        color: '#FF6B6B',
        topicCount: 2, // Limited for guests
        questionCount: 20, // Limited for guests
        isActive: true,
        order: 1,
      },
      {
        id: '2',
        name: '<PERSON><PERSON><PERSON>',
        icon: '🏛️',
        color: '#4ECDC4',
        topicCount: 2, // Limited for guests
        questionCount: 15, // Limited for guests
        isActive: true,
        order: 2,
      },
    ];
  }

  // Get demo content for a specific subject
  async getDemoContent(subjectId: string): Promise<Content[]> {
    // Mock demo content - in real app this would come from API
    const demoContent: Record<string, Content[]> = {
      '1': [
        // Türkçe
        {
          id: 'demo-content-1',
          title: 'Türkçe Te<PERSON> Kurallar',
          description: 'Türkçe dilbilgisi temel kuralları ve örnekleri',
          type: 'article',
          subject: 'Türkçe',
          difficulty: 'beginner',
          duration: 15,
          thumbnailUrl: undefined,
          contentUrl: '/demo/turkce-temel',
          tags: ['dilbilgisi', 'temel', 'demo'],
          isPublished: true,
          viewCount: 1250,
          likeCount: 89,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'demo-content-2',
          title: 'Kelime Türleri',
          description: 'İsim, fiil, sıfat ve diğer kelime türleri',
          type: 'video',
          subject: 'Türkçe',
          difficulty: 'intermediate',
          duration: 20,
          thumbnailUrl: undefined,
          contentUrl: '/demo/kelime-turleri',
          tags: ['kelime', 'türler', 'demo'],
          isPublished: true,
          viewCount: 980,
          likeCount: 67,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
      '2': [
        // Tarih
        {
          id: 'demo-content-3',
          title: 'Osmanlı İmparatorluğu',
          description: "Osmanlı İmparatorluğu'nun kuruluşu ve gelişimi",
          type: 'article',
          subject: 'Tarih',
          difficulty: 'intermediate',
          duration: 25,
          thumbnailUrl: undefined,
          contentUrl: '/demo/osmanli',
          tags: ['osmanlı', 'tarih', 'demo'],
          isPublished: true,
          viewCount: 1450,
          likeCount: 112,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'demo-content-4',
          title: 'Cumhuriyet Dönemi',
          description: "Türkiye Cumhuriyeti'nin kuruluşu ve ilk yılları",
          type: 'video',
          subject: 'Tarih',
          difficulty: 'intermediate',
          duration: 30,
          thumbnailUrl: undefined,
          contentUrl: '/demo/cumhuriyet',
          tags: ['cumhuriyet', 'atatürk', 'demo'],
          isPublished: true,
          viewCount: 1680,
          likeCount: 134,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    };

    return demoContent[subjectId] || [];
  }

  // Get demo quizzes for a specific subject
  async getDemoQuizzes(subjectId: string): Promise<Quiz[]> {
    // Mock demo quizzes - in real app this would come from API
    const demoQuizzes: Record<string, Quiz[]> = {
      '1': [
        // Türkçe
        {
          id: 'demo-quiz-1',
          title: 'Türkçe Demo Quiz',
          description: 'Türkçe temel konularından sorular',
          subject: 'Türkçe',
          difficulty: 'easy',
          questionCount: 5, // Limited for demo
          timeLimit: 10,
          thumbnailUrl: undefined,
          tags: ['türkçe', 'demo'],
          isPublished: true,
          averageScore: 75,
          attemptCount: 245,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
      '2': [
        // Tarih
        {
          id: 'demo-quiz-2',
          title: 'Tarih Demo Quiz',
          description: 'Tarih temel konularından sorular',
          subject: 'Tarih',
          difficulty: 'easy',
          questionCount: 5, // Limited for demo
          timeLimit: 10,
          thumbnailUrl: undefined,
          tags: ['tarih', 'demo'],
          isPublished: true,
          averageScore: 68,
          attemptCount: 189,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    };

    return demoQuizzes[subjectId] || [];
  }

  // Check if content is demo content
  isDemoContent(contentId: string): boolean {
    return contentId.startsWith('demo-');
  }

  // Check if quiz is demo quiz
  isDemoQuiz(quizId: string): boolean {
    return quizId.startsWith('demo-');
  }

  // Get guest mode limitations
  getGuestLimitations() {
    return {
      maxContentViews: 5,
      maxQuizAttempts: 2,
      maxSubjects: 2,
      availableFeatures: [
        'view_demo_content',
        'attempt_demo_quiz',
        'view_leaderboard',
      ],
      restrictedFeatures: [
        'full_content_access',
        'unlimited_quiz_attempts',
        'social_features',
        'progress_tracking',
        'badges',
        'achievements',
      ],
    };
  }

  // Get upgrade benefits for guests
  getUpgradeBenefits() {
    return {
      title: 'Premium Üyelik Avantajları',
      benefits: [
        '100 jeton ile başla',
        'Tüm derslere sınırsız erişim',
        'Sınırsız quiz çözme',
        'Sosyal özellikler',
        'İlerleme takibi',
        'Rozet ve başarımlar',
        'Liderlik tablosunda yer alma',
        'Özel içerikler',
      ],
    };
  }
}

export const guestService = new GuestService();
