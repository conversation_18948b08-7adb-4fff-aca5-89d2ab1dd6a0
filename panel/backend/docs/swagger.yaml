openapi: 3.0.3
info:
  title: KPSS Plus Backend API
  description: |
    Comprehensive API for KPSS Plus - A study platform for KPSS exam preparation.

    ## Features
    - User authentication and management
    - Content management and study tracking
    - Quiz system with detailed analytics
    - Progress tracking and goal setting
    - Social features and friendship system
    - Badge and achievement system
    - Timeline and activity feed
    - Notification system
    - Analytics and reporting

    ## Authentication
    Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```

    ## Rate Limiting
    API requests are rate limited to prevent abuse. Current limits:
    - Authenticated users: 1000 requests per hour
    - Unauthenticated users: 100 requests per hour

    ## Error Handling
    The API uses standard HTTP status codes and returns error details in JSON format:
    ```json
    {
      "error": "Error description",
      "status": 400,
      "details": "Additional error details"
    }
    ```
  version: 1.0.0
  contact:
    name: KPSS Plus API Support
    email: <EMAIL>
    url: https://kpssplus.com/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.kpssplus.com/v1
    description: Production server
  - url: https://staging-api.kpssplus.com/v1
    description: Staging server
  - url: http://localhost:8080/api/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  # Authentication endpoints
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Create a new user account with email and password
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: Login user
      description: Authenticate user with email and password
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /users/profile:
    get:
      tags:
        - Users
      summary: Get user profile
      description: Get the authenticated user's profile information
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /content:
    get:
      tags:
        - Content
      summary: Get content list
      description: Retrieve a paginated list of content
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: subject
          in: query
          description: Filter by subject
          schema:
            type: string
      responses:
        '200':
          description: Content list retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContentListResponse'

  /quiz:
    get:
      tags:
        - Quiz
      summary: Get quiz list
      description: Retrieve a paginated list of quizzes
      security: []
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Quiz list retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizListResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message
        status:
          type: integer
          description: HTTP status code
        details:
          type: string
          description: Additional error details
      required:
        - error
        - status

    RegisterRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User's email address
        password:
          type: string
          minLength: 8
          description: User's password
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: User's full name
        username:
          type: string
          minLength: 3
          maxLength: 50
          description: User's username
      required:
        - email
        - password
        - name
        - username

    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User's email address
        password:
          type: string
          description: User's password
      required:
        - email
        - password

    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
          description: JWT access token
        refresh_token:
          type: string
          description: JWT refresh token
        expires_in:
          type: integer
          description: Token expiration time in seconds
        user:
          $ref: '#/components/schemas/UserInfo'
      required:
        - access_token
        - refresh_token
        - expires_in
        - user

    UserInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: User ID
        username:
          type: string
          description: Username
        name:
          type: string
          description: Full name
        email:
          type: string
          format: email
          description: Email address
        is_verified:
          type: boolean
          description: Whether email is verified
        created_at:
          type: string
          format: date-time
          description: Account creation date
      required:
        - id
        - username
        - name
        - email
        - is_verified
        - created_at

    UserProfileResponse:
      allOf:
        - $ref: '#/components/schemas/UserInfo'
        - type: object
          properties:
            bio:
              type: string
              description: User biography
            study_streak:
              type: integer
              description: Current study streak in days
            total_study_time:
              type: integer
              description: Total study time in seconds
            badges_earned:
              type: integer
              description: Number of badges earned

    ContentResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        type:
          type: string
          enum: [lesson, exercise, exam, video, document]
        subject:
          type: string
        difficulty:
          type: string
          enum: [Beginner, Intermediate, Advanced]
        is_free:
          type: boolean
        created_at:
          type: string
          format: date-time
      required:
        - id
        - title
        - type
        - subject
        - difficulty
        - is_free
        - created_at

    ContentListResponse:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ContentResponse'
        total:
          type: integer
        page:
          type: integer
        limit:
          type: integer
        total_pages:
          type: integer
      required:
        - content
        - total
        - page
        - limit
        - total_pages

    QuizResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        subject:
          type: string
        difficulty:
          type: string
          enum: [Easy, Medium, Hard]
        is_public:
          type: boolean
        question_count:
          type: integer
        average_score:
          type: number
          format: float
        created_at:
          type: string
          format: date-time
      required:
        - id
        - title
        - is_public
        - question_count
        - average_score
        - created_at

    QuizListResponse:
      type: object
      properties:
        quizzes:
          type: array
          items:
            $ref: '#/components/schemas/QuizResponse'
        total:
          type: integer
        page:
          type: integer
        limit:
          type: integer
        total_pages:
          type: integer
      required:
        - quizzes
        - total
        - page
        - limit
        - total_pages

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management and profiles
  - name: Content
    description: Educational content management
  - name: Quiz
    description: Quiz and question management
  - name: Progress
    description: User progress tracking
  - name: Social
    description: Social features and friendship
  - name: Notifications
    description: Notification management
  - name: Analytics
    description: Analytics and reporting
  - name: Badges
    description: Badge and achievement system
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
  /preferences:
    get:
      tags: [Preferences]
      summary: Get user preferences (public)
      description: Returns preferences for authenticated user if token provided; otherwise returns defaults.
      security: []
      responses:
        '200':
          description: Preferences
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/PreferencesResponse'
                  status:
                    type: integer
    put:
      tags: [Preferences]
      summary: Update user preferences
      description: Updates preferences for the authenticated user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePreferencesRequest'
      responses:
        '200':
          description: Updated preferences
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/PreferencesResponse'
                  status:
                    type: integer

  /library:
    get:
      tags: [Library]
      summary: List library items
      description: Returns user's library items with optional details
      parameters:
        - in: query
          name: type
          schema: { type: string, enum: [content, topic, quiz] }
        - in: query
          name: folder_id
          schema: { type: string, format: uuid }
        - in: query
          name: page
          schema: { type: integer, minimum: 1, default: 1 }
        - in: query
          name: limit
          schema: { type: integer, minimum: 1, maximum: 100, default: 20 }
        - in: query
          name: details
          schema: { type: boolean, default: false }
      responses:
        '200':
          description: Library list
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    oneOf:
                      - $ref: '#/components/schemas/LibraryListResponse'
                      - $ref: '#/components/schemas/LibraryListWithDetailsResponse'
                  status:
                    type: integer

  /library/{type}/{id}:
    post:
      tags: [Library]
      summary: Add item to library
      parameters:
        - in: path
          name: type
          required: true
          schema: { type: string, enum: [content, topic, quiz] }
        - in: path
          name: id
          required: true
          schema: { type: string, format: uuid }
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                folder_id:
                  type: string
                  format: uuid
      responses:
        '201':
          description: Added
    delete:
      tags: [Library]
      summary: Remove item from library
      parameters:
        - in: path
          name: type
          required: true
          schema: { type: string, enum: [content, topic, quiz] }
        - in: path
          name: id
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200': { description: Removed }

  /library/folders:
    get:
      tags: [Library]
      summary: List folders
      responses:
        '200': { description: OK }
    post:
      tags: [Library]
      summary: Create folder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFolderRequest'
      responses:
        '201': { description: Created }

  /library/folders/{id}:
    put:
      tags: [Library]
      summary: Rename folder
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string, format: uuid }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFolderRequest'
      responses:
        '200': { description: OK }
    delete:
      tags: [Library]
      summary: Delete folder
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200': { description: OK }

  /quiz/{id}/start:
    post:
      tags: [Quiz]
      summary: Start quiz session
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: Session created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizSessionResponse'

  /quiz/sessions/{sessionId}/answer:
    post:
      tags: [Quiz]
      summary: Submit answer for a session question
      parameters:
        - in: path
          name: sessionId
          required: true
          schema: { type: string, format: uuid }
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitAnswerRequest'
      responses:
        '200':
          description: Answer stored
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnswerResponse'

  /quiz/sessions/{sessionId}/finish:
    post:
      tags: [Quiz]
      summary: Finish quiz session and calculate result
      parameters:
        - in: path
          name: sessionId
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: Result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuizResultResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    PreferencesResponse:
      type: object
      properties:
        interests:
          type: array
          items: { type: string }
        subject_weights:
          type: object
          additionalProperties:
            type: number
            format: float
        difficulty_pref:
          type: string
          enum: [Easy, Medium, Hard, Any]
    UpdatePreferencesRequest:
      allOf:
        - $ref: '#/components/schemas/PreferencesResponse'
    CreateFolderRequest:
      type: object
      required: [name]
      properties:
        name: { type: string }
    UpdateFolderRequest:
      type: object
      required: [name]
      properties:
        name: { type: string }
    LibraryItemResponse:
      type: object
      properties:
        id: { type: string, format: uuid }
        item_type: { type: string, enum: [content, topic, quiz] }
        item_id: { type: string, format: uuid }
        folder_id: { type: string, format: uuid, nullable: true }
    LibraryListResponse:
      type: object
      properties:
        items:
          type: array
          items: { $ref: '#/components/schemas/LibraryItemResponse' }
        total: { type: integer }
        page: { type: integer }
        limit: { type: integer }
        total_pages: { type: integer }
    LibraryListWithDetailsResponse:
      allOf:
        - $ref: '#/components/schemas/LibraryListResponse'
      properties:
        details:
          type: object
          additionalProperties:
            type: object
    QuizSessionResponse:
      type: object
      properties:
        id: { type: string, format: uuid }
        user_id: { type: string, format: uuid }
        quiz_id: { type: string, format: uuid }
        start_time: { type: string, format: date-time }
        end_time: { type: string, format: date-time, nullable: true }
        time_limit: { type: integer, nullable: true }
        is_completed: { type: boolean }
        current_question: { type: integer }
        total_questions: { type: integer }
    SubmitAnswerRequest:
      type: object
      required: [question_id]
      properties:
        question_id: { type: string, format: uuid }
        answer: { type: string, enum: [A,B,C,D,E], nullable: true }
        time_spent: { type: integer, minimum: 0, nullable: true }
    AnswerResponse:
      type: object
      properties:
        id: { type: string, format: uuid }
        question_id: { type: string, format: uuid }
        user_answer: { type: string, nullable: true }
        is_correct: { type: boolean }
        time_spent: { type: integer, nullable: true }
    QuizResultResponse:
      type: object
      properties:
        id: { type: string, format: uuid }
        user_id: { type: string, format: uuid }
        quiz_id: { type: string, format: uuid }
        score: { type: integer }
        total_questions: { type: integer }
        percentage: { type: number, format: float }
        time_spent: { type: integer, nullable: true }
        completed_at: { type: string, format: date-time }
