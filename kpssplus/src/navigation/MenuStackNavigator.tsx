import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {MenuStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {MenuHomeScreen} from '../screens/menu/MenuHomeScreen';
import {ProfileScreen} from '../screens/menu/ProfileScreen';
import {SettingsScreen} from '../screens/menu/SettingsScreen';
import {BadgesScreen} from '../screens/menu/BadgesScreen';
import {AnalyticsScreen} from '../screens/menu/AnalyticsScreen';
import {ProgressScreen} from '../screens/menu/ProgressScreen';
import {GoalsScreen} from '../screens/menu/GoalsScreen';
import {NotificationsScreen} from '../screens/menu/NotificationsScreen';
import {HelpScreen} from '../screens/menu/HelpScreen';
import {AboutScreen} from '../screens/menu/AboutScreen';

const Stack = createStackNavigator<MenuStackParamList>();

export const MenuStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.white,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackTitle: '',
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="MenuHome"
        component={MenuHomeScreen}
        options={{
          title: 'Menü',
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profilim',
        }}
      />
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Ayarlar',
        }}
      />
      <Stack.Screen
        name="Badges"
        component={BadgesScreen}
        options={{
          title: 'Rozetlerim',
        }}
      />
      <Stack.Screen
        name="Analytics"
        component={AnalyticsScreen}
        options={{
          title: 'İstatistikler',
        }}
      />
      <Stack.Screen
        name="Progress"
        component={ProgressScreen}
        options={{
          title: 'İlerleme',
        }}
      />
      <Stack.Screen
        name="Goals"
        component={GoalsScreen}
        options={{
          title: 'Hedeflerim',
        }}
      />
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Bildirimler',
        }}
      />
      <Stack.Screen
        name="Help"
        component={HelpScreen}
        options={{
          title: 'Yardım',
        }}
      />
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          title: 'Hakkında',
        }}
      />
    </Stack.Navigator>
  );
};
