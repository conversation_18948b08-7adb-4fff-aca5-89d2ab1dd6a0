import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import {
  BookOpen,
  Clock,
  Users,
  Star,
  Search,
  TrendingUp,
  Heart,
  Play
} from 'lucide-react';
import { Quiz, QuizFilters } from '../../types/quiz';
import { quizService } from '../../services/quizService';
import { useToast } from '../common/Toast';

const QuizList: React.FC = () => {
  const { t } = useTranslation();
  const { showError } = useToast();
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters] = useState<QuizFilters>({});
  const [activeTab, setActiveTab] = useState<'all' | 'popular' | 'featured' | 'favorites' | 'my-quizzes'>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const loadQuizzes = async (reset = false) => {
    try {
      setLoading(true);
      setError(null);

      const currentPage = reset ? 1 : page;
      let response: any;

      // Use actual API call instead of mock data
      if (searchQuery.trim()) {
        response = await quizService.searchQuizzes(searchQuery, {
          ...filters,
          page: currentPage,
          limit: 12
        });
      } else {
        // Call different endpoints based on active tab
        switch (activeTab) {
          case 'featured':
            response = await quizService.getFeaturedQuizzes({
              page: currentPage,
              limit: 12
            });
            break;
          case 'popular':
            response = await quizService.getPopularQuizzes({
              page: currentPage,
              limit: 12
            });
            break;
          case 'my-quizzes':
            response = await quizService.getUserQuizzes({
              page: currentPage,
              limit: 12
            });
            break;
          default:
            response = await quizService.getQuizzes({
              ...filters,
              page: currentPage,
              limit: 12
            });
        }
      }

      if (response.success && response.data) {
        if (reset) {
          setQuizzes(response.data.quizzes);
          setPage(1);
        } else {
          setQuizzes(prev => [...prev, ...response.data.quizzes]);
        }
        setHasMore(response.data.has_more);

        if (response.data.quizzes.length === 0) {
          showError('No quizzes found', 'Backend connection issue or no data available');
        }
      } else {
        const errorMsg = 'Failed to load quizzes - Backend not responding';
        setError(errorMsg);
        showError('Connection Error', errorMsg);
      }
    } catch (err) {
      const errorMsg = 'Backend service unavailable';
      setError(errorMsg);
      showError('Service Error', errorMsg);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadQuizzes(true);
  }, [activeTab, searchQuery, filters]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadQuizzes(true);
  };

  const handleTabChange = (tab: typeof activeTab) => {
    setActiveTab(tab);
    setPage(1);
  };

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
    loadQuizzes();
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">{t('quiz.quizzes')}</h1>
          <p className="text-dark-300 mt-1">Discover and take practice tests</p>
        </div>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex gap-2 max-w-md w-full lg:w-auto">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400" size={20} />
            <input
              type="text"
              placeholder="Search quizzes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:border-primary-500"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <Search size={20} />
          </button>
        </form>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
        {[
          { key: 'all', label: 'All Quizzes', icon: BookOpen },
          { key: 'popular', label: 'Popular', icon: TrendingUp },
          { key: 'featured', label: 'Featured', icon: Star },
          { key: 'favorites', label: 'Favorites', icon: Heart }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => handleTabChange(key as typeof activeTab)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === key
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:text-white hover:bg-dark-700'
            }`}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* Quiz Grid */}
      {error ? (
        <div className="text-center py-12">
          <div className="text-red-400 mb-2">Error loading quizzes</div>
          <p className="text-dark-400">{error}</p>
          <button
            onClick={() => loadQuizzes(true)}
            className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quizzes.map((quiz) => (
              <QuizCard key={quiz.id} quiz={quiz} />
            ))}
          </div>

          {loading && (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
            </div>
          )}

          {!loading && quizzes.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-dark-400 mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">{t('quiz.noQuizzes')}</h3>
              <p className="text-dark-400">Try adjusting your search or filters</p>
            </div>
          )}

          {!loading && hasMore && quizzes.length > 0 && (
            <div className="text-center">
              <button
                onClick={handleLoadMore}
                className="px-6 py-2 bg-dark-700 hover:bg-dark-600 text-white rounded-lg transition-colors"
              >
                Load More
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// Quiz Card Component
const QuizCard: React.FC<{ quiz: Quiz }> = ({ quiz }) => {
  const { t } = useTranslation();

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 bg-green-400/10';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10';
      case 'hard': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  return (
    <div className="bg-dark-800 rounded-xl p-6 border border-dark-700 hover:border-primary-500/50 transition-all duration-200 group">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white group-hover:text-primary-400 transition-colors line-clamp-2">
            {quiz.title}
          </h3>
          <p className="text-dark-300 text-sm mt-1 line-clamp-2">{quiz.description}</p>
        </div>
        {quiz.is_featured && (
          <Star className="text-yellow-400 ml-2 flex-shrink-0" size={20} />
        )}
      </div>

      {/* Stats */}
      <div className="flex items-center gap-4 text-sm text-dark-400 mb-4">
        <div className="flex items-center gap-1">
          <BookOpen size={16} />
          <span>{quiz.question_count} questions</span>
        </div>
        <div className="flex items-center gap-1">
          <Clock size={16} />
          <span>{formatDuration(quiz.time_limit)}</span>
        </div>
        <div className="flex items-center gap-1">
          <Users size={16} />
          <span>{quiz.statistics.total_attempts}</span>
        </div>
      </div>

      {/* Tags */}
      <div className="flex items-center gap-2 mb-4">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(quiz.difficulty)}`}>
          {quiz.difficulty}
        </span>
        <span className="px-2 py-1 bg-dark-700 text-dark-300 rounded-full text-xs">
          {quiz.subject}
        </span>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {quiz.creator_avatar && (
            <img
              src={quiz.creator_avatar}
              alt={quiz.creator_name}
              className="w-6 h-6 rounded-full"
            />
          )}
          <span className="text-sm text-dark-400">{quiz.creator_name}</span>
        </div>
        
        <Link
          to={`/quizzes/${quiz.id}`}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors text-sm font-medium"
        >
          <Play size={16} />
          {t('quiz.startQuiz')}
        </Link>
      </div>
    </div>
  );
};

export default QuizList;
