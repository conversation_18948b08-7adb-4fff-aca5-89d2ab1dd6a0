package routes

import (
    "net/http"
    "strconv"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/kpss-plus-backend/pkg/domains/analytics"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/middleware"
    "github.com/kpss-plus-backend/pkg/state"
)

func AnalyticsRoutes(r *gin.RouterGroup, analyticsService analytics.Service) {
	analyticsGroup := r.Group("/analytics")

	// Protected analytics routes
	protectedGroup := analyticsGroup.Use(middleware.Authorized())
	{
		// User analytics
		protectedGroup.GET("/users", GetUserAnalytics(analyticsService))
		protectedGroup.GET("/users/activity", GetUserActivity(analyticsService))
		protectedGroup.GET("/users/progress", GetUserProgress(analyticsService))

		// Content analytics
		protectedGroup.GET("/content", GetContentAnalytics(analyticsService))
		protectedGroup.GET("/content/popular", GetPopularContentAnal(analyticsService))
		protectedGroup.GET("/content/engagement", GetContentEngagement(analyticsService))

		// Quiz analytics
		protectedGroup.GET("/quizzes", GetQuizAnalytics(analyticsService))
		protectedGroup.GET("/quizzes/performance", GetQuizPerformance(analyticsService))
		protectedGroup.GET("/quizzes/completion", GetQuizCompletion(analyticsService))

		// System analytics
		protectedGroup.GET("/system", GetSystemAnalytics(analyticsService))
		protectedGroup.GET("/system/usage", GetSystemUsage(analyticsService))
	}
}

func GetUserAnalytics(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.DefaultQuery("user_id", c.GetString(state.CurrentUserID))
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        metrics := c.QueryArray("metrics")
        gran := c.DefaultQuery("granularity", "day")

        payload := &dtos.GetUserAnalyticsRequest{StartDate: start, EndDate: end, Metrics: metrics, Granularity: gran}
        res, err := s.GetUserAnalytics(userID, payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user analytics"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetUserActivity(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.DefaultQuery("user_id", c.GetString(state.CurrentUserID))
        // Use dashboard as activity summary
        res, err := s.GetUserDashboard(userID)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user activity"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetUserProgress(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.DefaultQuery("user_id", c.GetString(state.CurrentUserID))
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetProgressReportRequest{StartDate: start, EndDate: end}
        res, err := s.GetUserProgressReport(userID, payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user progress"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetContentAnalytics(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        contentID := c.Query("content_id")
        if contentID == "" {
            c.JSON(http.StatusBadRequest, gin.H{"error": "content_id is required"})
            return
        }
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetContentAnalyticsRequest{StartDate: start, EndDate: end}
        res, err := s.GetContentAnalytics(contentID, payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get content analytics"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetPopularContentAnal(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetContentPopularityReportRequest{StartDate: start, EndDate: end, Limit: limit}
        res, err := s.GetContentPopularityReport(payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get popular content"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetContentEngagement(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        // If content_id provided, return analytics for that content
        if contentID := c.Query("content_id"); contentID != "" {
            days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
            end := time.Now()
            start := end.Add(-time.Duration(days) * 24 * time.Hour)
            payload := &dtos.GetContentAnalyticsRequest{StartDate: start, EndDate: end}
            res, err := s.GetContentAnalytics(contentID, payload)
            if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get content engagement"})
                return
            }
            c.JSON(http.StatusOK, res)
            return
        }
        // Otherwise return aggregated engagement report
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetContentEngagementReportRequest{StartDate: start, EndDate: end}
        res, err := s.GetContentEngagementReport(payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get content engagement"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetQuizAnalytics(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        quizID := c.Query("quiz_id")
        if quizID == "" {
            c.JSON(http.StatusBadRequest, gin.H{"error": "quiz_id is required"})
            return
        }
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetQuizAnalyticsRequest{StartDate: start, EndDate: end}
        res, err := s.GetQuizAnalytics(quizID, payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz analytics"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetQuizPerformance(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        quizID := c.Query("quiz_id")
        if quizID == "" {
            c.JSON(http.StatusBadRequest, gin.H{"error": "quiz_id is required"})
            return
        }
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetQuizPerformanceReportRequest{StartDate: start, EndDate: end}
        res, err := s.GetQuizPerformanceReport(quizID, payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz performance"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetQuizCompletion(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetQuizStatisticsReportRequest{StartDate: start, EndDate: end}
        res, err := s.GetQuizStatisticsReport(payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz completion"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetSystemAnalytics(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetSystemAnalyticsRequest{StartDate: start, EndDate: end}
        res, err := s.GetSystemAnalytics(payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get system analytics"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}

func GetSystemUsage(s analytics.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
        end := time.Now()
        start := end.Add(-time.Duration(days) * 24 * time.Hour)
        payload := &dtos.GetPlatformUsageReportRequest{StartDate: start, EndDate: end}
        res, err := s.GetPlatformUsageReport(payload)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get system usage"})
            return
        }
        c.JSON(http.StatusOK, res)
    }
}
