import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout';
import { apiGet, apiPost, apiPut, apiDel } from '../lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

interface Topic {
  id: string;
  title: string;
}



interface Question {
  id: string;
  topic_id?: string;
  content_id?: string;
  quiz_id?: string;
  text: string;
  question_type: string;
  option_a?: string;
  option_b?: string;
  option_c?: string;
  option_d?: string;
  option_e?: string;
  correct_answer?: string;
  created_at: string;
  updated_at: string;
  topic?: Topic;
}

const questionTypes = [
  { value: 'multiple_choice', label: 'Çok<PERSON>' },
  { value: 'true_false', label: 'Doğru/Yanlış' },
  { value: 'fill_blank', label: 'Boşluk Doldurma' },
  { value: 'essay', label: '<PERSON><PERSON><PERSON><PERSON>' }
];

const Questions: React.FC = () => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [formData, setFormData] = useState({
    text: '',
    question_type: '',
    topic_id: '',
    content_id: '',
    quiz_id: '',
    option_a: '',
    option_b: '',
    option_c: '',
    option_d: '',
    option_e: '',
    correct_answer: ''
  });

  useEffect(() => {
    loadQuestions();
    loadTopics();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, selectedType]);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchQuery) params.append('q', searchQuery);
      if (selectedType) params.append('question_type', selectedType);
      params.append('limit', '50');

      const response = await apiGet<{ data: Question[]; total: number }>(`/questions?${params}`);
      setQuestions(response.data || []);
    } catch (error) {
      console.error('Error loading questions:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTopics = async () => {
    try {
      const response = await apiGet<{ data: Topic[] }>('/topics?limit=100');
      setTopics(response.data || []);
    } catch (error) {
      console.error('Error loading topics:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Backend'in beklediği format
      const data: any = {
        text: formData.text,
        question_type: formData.question_type
      };

      // Opsiyonel alanları ekle
      if (formData.topic_id) data.topic_id = formData.topic_id;
      if (formData.content_id) data.content_id = formData.content_id;
      if (formData.quiz_id) data.quiz_id = formData.quiz_id;

      // Çoktan seçmeli sorular için seçenekleri ekle
      if (formData.question_type === 'multiple_choice') {
        if (formData.option_a) data.option_a = formData.option_a;
        if (formData.option_b) data.option_b = formData.option_b;
        if (formData.option_c) data.option_c = formData.option_c;
        if (formData.option_d) data.option_d = formData.option_d;
        if (formData.option_e) data.option_e = formData.option_e;
      }

      // Tüm soru türleri için doğru cevabı ekle
      if (formData.correct_answer) {
        data.correct_answer = formData.correct_answer;
      }

      if (editingQuestion) {
        await apiPut(`/questions/${editingQuestion.id}`, data);
      } else {
        await apiPost('/questions', data);
      }

      setShowCreateModal(false);
      setEditingQuestion(null);
      resetForm();
      loadQuestions();
    } catch (error) {
      console.error('Error saving question:', error);
      alert('Soru kaydedilirken hata oluştu. Lütfen tüm gerekli alanları doldurun.');
    }
  };

  const handleEdit = (question: Question) => {
    setEditingQuestion(question);
    setFormData({
      text: question.text,
      question_type: question.question_type,
      topic_id: question.topic_id || '',
      content_id: question.content_id || '',
      quiz_id: question.quiz_id || '',
      option_a: question.option_a || '',
      option_b: question.option_b || '',
      option_c: question.option_c || '',
      option_d: question.option_d || '',
      option_e: question.option_e || '',
      correct_answer: question.correct_answer || ''
    });
    setShowCreateModal(true);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Bu soruyu silmek istediğinizden emin misiniz?')) return;

    try {
      await apiDel(`/questions/${id}`);
      loadQuestions();
    } catch (error) {
      console.error('Error deleting question:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      text: '',
      question_type: '',
      topic_id: '',
      content_id: '',
      quiz_id: '',
      option_a: '',
      option_b: '',
      option_c: '',
      option_d: '',
      option_e: '',
      correct_answer: ''
    });
  };

  const handleCreateNew = () => {
    setEditingQuestion(null);
    resetForm();
    setShowCreateModal(true);
  };

  const getQuestionTypeLabel = (type: string) => {
    const questionType = questionTypes.find(qt => qt.value === type);
    return questionType?.label || type;
  };

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Sorular
            </h1>
            <p className="mt-2 text-slate-300">
              KPSS sorularını yönetin (Çoktan seçmeli, doğru/yanlış, boşluk doldurma...)
            </p>
          </div>
          <button
            onClick={handleCreateNew}
            className="mt-4 sm:mt-0 btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Soru Ekle
          </button>
        </div>

        {/* Filters */}
        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Arama
              </label>
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                <input
                  type="text"
                  placeholder="Soru ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="input-field pl-10"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Soru Türü
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="input-field"
              >
                <option value="">Tüm Türler</option>
                {questionTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Questions List */}
        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-slate-400">Yükleniyor...</p>
            </div>
          ) : questions.length === 0 ? (
            <div className="p-8 text-center">
              <QuestionMarkCircleIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-400">Henüz soru bulunmuyor.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-slate-900/50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Soru
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Tür
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                      Konu
                    </th>
                    <th className="px-6 py-4 text-right text-xs font-medium text-slate-300 uppercase tracking-wider">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-700/50">
                  {questions.map((question) => (
                    <tr key={question.id} className="hover:bg-slate-700/30 transition-colors duration-200">
                      <td className="px-6 py-4">
                        <div className="flex items-start">
                          <QuestionMarkCircleIcon className="h-5 w-5 text-blue-400 mr-3 mt-0.5 flex-shrink-0" />
                          <div>
                            <div className="text-sm font-medium text-slate-200">
                              {question.text.length > 100
                                ? `${question.text.substring(0, 100)}...`
                                : question.text
                              }
                            </div>
                            {question.correct_answer && (
                              <div className="text-sm text-slate-400 mt-1">
                                Doğru cevap: {question.correct_answer}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-slate-300">
                        {getQuestionTypeLabel(question.question_type)}
                      </td>

                      <td className="px-6 py-4 text-sm text-slate-300">
                        {question.topic?.title || '-'}
                      </td>
                      <td className="px-6 py-4 text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleEdit(question)}
                            className="text-blue-400 hover:text-blue-300 transition-colors duration-200"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(question.id)}
                            className="text-red-400 hover:text-red-300 transition-colors duration-200"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-slate-800 rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-white mb-6">
              {editingQuestion ? 'Soru Düzenle' : 'Yeni Soru Ekle'}
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Soru Metni *
                </label>
                <textarea
                  required
                  value={formData.text}
                  onChange={(e) => setFormData({ ...formData, text: e.target.value })}
                  className="input-field"
                  rows={4}
                  placeholder="Soru metnini girin..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Soru Türü *
                  </label>
                  <select
                    required
                    value={formData.question_type}
                    onChange={(e) => setFormData({ ...formData, question_type: e.target.value })}
                    className="input-field"
                  >
                    <option value="">Tür Seçin</option>
                    {questionTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Konu
                  </label>
                  <select
                    value={formData.topic_id}
                    onChange={(e) => setFormData({ ...formData, topic_id: e.target.value })}
                    className="input-field"
                  >
                    <option value="">Konu Seçin</option>
                    {topics.map((topic) => (
                      <option key={topic.id} value={topic.id}>
                        {topic.title}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Multiple Choice Options */}
              {formData.question_type === 'multiple_choice' && (
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Seçenekler
                  </label>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Seçenek A</label>
                      <input
                        type="text"
                        value={formData.option_a}
                        onChange={(e) => setFormData({ ...formData, option_a: e.target.value })}
                        className="input-field"
                        placeholder="Seçenek A"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Seçenek B</label>
                      <input
                        type="text"
                        value={formData.option_b}
                        onChange={(e) => setFormData({ ...formData, option_b: e.target.value })}
                        className="input-field"
                        placeholder="Seçenek B"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Seçenek C</label>
                      <input
                        type="text"
                        value={formData.option_c}
                        onChange={(e) => setFormData({ ...formData, option_c: e.target.value })}
                        className="input-field"
                        placeholder="Seçenek C"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Seçenek D</label>
                      <input
                        type="text"
                        value={formData.option_d}
                        onChange={(e) => setFormData({ ...formData, option_d: e.target.value })}
                        className="input-field"
                        placeholder="Seçenek D"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-slate-400 mb-1">Seçenek E (İsteğe bağlı)</label>
                      <input
                        type="text"
                        value={formData.option_e}
                        onChange={(e) => setFormData({ ...formData, option_e: e.target.value })}
                        className="input-field"
                        placeholder="Seçenek E"
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Doğru Cevap
                    </label>
                    <select
                      value={formData.correct_answer}
                      onChange={(e) => setFormData({ ...formData, correct_answer: e.target.value })}
                      className="input-field"
                    >
                      <option value="">Doğru cevabı seçin</option>
                      <option value="A">A</option>
                      <option value="B">B</option>
                      <option value="C">C</option>
                      <option value="D">D</option>
                      <option value="E">E</option>
                    </select>
                  </div>
                </div>
              )}

              {/* True/False Answer */}
              {formData.question_type === 'true_false' && (
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Doğru Cevap
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="correct_answer"
                        value="true"
                        checked={formData.correct_answer === 'true'}
                        onChange={(e) => setFormData({ ...formData, correct_answer: e.target.value })}
                        className="mr-2"
                      />
                      <span className="text-slate-300">Doğru</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="correct_answer"
                        value="false"
                        checked={formData.correct_answer === 'false'}
                        onChange={(e) => setFormData({ ...formData, correct_answer: e.target.value })}
                        className="mr-2"
                      />
                      <span className="text-slate-300">Yanlış</span>
                    </label>
                  </div>
                </div>
              )}

              {/* Other question types */}
              {(formData.question_type === 'fill_blank' || formData.question_type === 'essay') && (
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Doğru Cevap / Örnek Cevap
                  </label>
                  <textarea
                    value={formData.correct_answer}
                    onChange={(e) => setFormData({ ...formData, correct_answer: e.target.value })}
                    className="input-field"
                    rows={3}
                    placeholder="Doğru cevabı veya örnek cevabı girin..."
                  />
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="btn-secondary"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  {editingQuestion ? 'Güncelle' : 'Oluştur'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default Questions;
