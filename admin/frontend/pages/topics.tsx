import { useEffect, useState } from 'react'
import Layout from '../src/components/Layout'
import { apiDel, apiGet, apiPost, apiPut } from '../src/lib/api'
import { 
  MagnifyingGlassIcon, 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  BookOpenIcon,
  FolderIcon
} from '@heroicons/react/24/outline'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'

type Area = {
  id: string
  name: string
  code: string
}

type Subject = {
  id: string
  area_id: string
  name: string
  code: string
  area?: Area
}

type Topic = {
  id: string
  subject_id?: string
  parent_id?: string
  title: string
  description?: string
  body?: string
  order?: number
  is_published?: boolean
  achievement_code?: string
  subject?: Subject
  parent?: Topic
  children?: Topic[]
}

export default function TopicsPage() {
  const [items, setItems] = useState<Topic[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [loading, setLoading] = useState(false)
  const [q, setQ] = useState('')
  const [offset, setOffset] = useState(0)
  const [limit] = useState(20)
  const [total, setTotal] = useState(0)

  // Form states
  const [subjectId, setSubjectId] = useState('')
  const [parentId, setParentId] = useState('')
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [body, setBody] = useState('')
  const [order, setOrder] = useState<number | ''>('')
  const [achievementCode, setAchievementCode] = useState('')
  const [isPublished, setIsPublished] = useState(true)

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null)

  const loadSubjects = async () => {
    try {
      const data = await apiGet<{ data: Subject[] }>('/subjects')
      setSubjects(data.data || [])
    } catch (error) {
      console.error('Error loading subjects:', error)
    }
  }

  const load = async (reset = true) => {
    setLoading(true)
    try {
      const data = await apiGet<{ data: Topic[]; total: number }>(`/topics?limit=${limit}&offset=${reset ? 0 : offset}&q=${encodeURIComponent(q)}`)
      const rows = data.data || []
      setTotal(data.total || 0)
      setItems(reset ? rows : [...items, ...rows])
      if (reset) setOffset(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadSubjects()
    load()
  }, [])

  const create = async () => {
    if (!title) return
    
    await apiPost('/topics', {
      subject_id: subjectId || undefined,
      parent_id: parentId || undefined,
      title,
      description: description || undefined,
      body: body || undefined,
      order: order === '' ? 0 : Number(order),
      achievement_code: achievementCode || undefined,
      is_published: isPublished
    })
    
    // Reset form
    setSubjectId('')
    setParentId('')
    setTitle('')
    setDescription('')
    setBody('')
    setOrder('')
    setAchievementCode('')
    setIsPublished(true)
    setShowCreateModal(false)
    
    load(true)
  }

  const openEditModal = (topic: Topic) => {
    setEditingTopic(topic)
    setShowEditModal(true)
  }

  const saveEdit = async () => {
    if (!editingTopic) return
    
    await apiPut(`/topics/${editingTopic.id}`, editingTopic)
    setShowEditModal(false)
    setEditingTopic(null)
    load(true)
  }

  const remove = async (id: string) => {
    if (confirm('Bu konuyu silmek istediğinizden emin misiniz?')) {
      await apiDel(`/topics/${id}`)
      load(true)
    }
  }

  const togglePublish = async (id: string, isPublished: boolean) => {
    const endpoint = isPublished ? 'unpublish' : 'publish'
    await apiPut(`/topics/${id}/${endpoint}`, {})
    load(true)
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Konular</h1>
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              KPSS konularını yönetin (ünite/alt-konu ağaç yapısı)
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              onClick={() => setShowCreateModal(true)}
              className="btn-primary flex items-center"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Konu Ekle
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="card">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="input-field pl-10"
              placeholder="Konu ara..."
              value={q}
              onChange={(e) => setQ(e.target.value)}
            />
          </div>
        </div>

        {/* Topics Table */}
        <div className="card p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <span className="ml-2 text-gray-600 dark:text-gray-300">Konular yükleniyor...</span>
            </div>
          ) : (
            <div className="table-container">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" className="table-header">Konu</th>
                    <th scope="col" className="table-header">Ders</th>
                    <th scope="col" className="table-header">Kazanım Kodu</th>
                    <th scope="col" className="table-header">Sıra</th>
                    <th scope="col" className="table-header">Durum</th>
                    <th scope="col" className="table-header">İşlemler</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                  {items.map((topic) => (
                    <tr key={topic.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="table-cell">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0">
                            <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                              {topic.parent_id ? (
                                <FolderIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                              ) : (
                                <BookOpenIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                              )}
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{topic.title}</div>
                            {topic.description && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">{topic.description}</div>
                            )}
                            {topic.parent && (
                              <div className="text-xs text-gray-400 dark:text-gray-500">Alt konu: {topic.parent.title}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        {topic.subject ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                            {topic.subject.name}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-500 dark:text-gray-400">-</span>
                        )}
                      </td>
                      <td className="table-cell">
                        {topic.achievement_code ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
                            {topic.achievement_code}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-500 dark:text-gray-400">-</span>
                        )}
                      </td>
                      <td className="table-cell">
                        <span className="text-sm text-gray-900 dark:text-gray-100">{topic.order || 0}</span>
                      </td>
                      <td className="table-cell">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          topic.is_published 
                            ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
                            : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                        }`}>
                          {topic.is_published ? 'Yayında' : 'Taslak'}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => togglePublish(topic.id, topic.is_published || false)}
                            className="p-1 rounded-md text-blue-600 hover:text-blue-900"
                            title={topic.is_published ? 'Yayından Kaldır' : 'Yayınla'}
                          >
                            {topic.is_published ? (
                              <EyeSlashIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => openEditModal(topic)}
                            className="p-1 rounded-md text-primary-600 hover:text-primary-900"
                            title="Düzenle"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => remove(topic.id)}
                            className="p-1 rounded-md text-red-600 hover:text-red-900"
                            title="Sil"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Modal */}
      <Transition.Root show={showCreateModal} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={setShowCreateModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                  <div>
                    <div className="mt-3 text-center sm:mt-5">
                      <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900 dark:text-white">
                        Yeni Konu Ekle
                      </Dialog.Title>
                      <div className="mt-4 space-y-4">
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                          <div>
                            <select
                              className="input-field"
                              value={subjectId}
                              onChange={(e) => setSubjectId(e.target.value)}
                            >
                              <option value="">Ders Seçin (opsiyonel)</option>
                              {subjects.map((subject) => (
                                <option key={subject.id} value={subject.id}>
                                  {subject.name} ({subject.area?.name})
                                </option>
                              ))}
                            </select>
                          </div>
                          <div>
                            <input
                              type="text"
                              className="input-field"
                              placeholder="Üst Konu ID (opsiyonel)"
                              value={parentId}
                              onChange={(e) => setParentId(e.target.value)}
                            />
                          </div>
                        </div>
                        <div>
                          <input
                            type="text"
                            className="input-field"
                            placeholder="Konu Başlığı"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                          />
                        </div>
                        <div>
                          <textarea
                            className="input-field"
                            placeholder="Açıklama (opsiyonel)"
                            rows={3}
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                          />
                        </div>
                        <div>
                          <textarea
                            className="input-field"
                            placeholder="İçerik (HTML) (opsiyonel)"
                            rows={4}
                            value={body}
                            onChange={(e) => setBody(e.target.value)}
                          />
                        </div>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                          <div>
                            <input
                              type="text"
                              className="input-field"
                              placeholder="MEB Kazanım Kodu (opsiyonel)"
                              value={achievementCode}
                              onChange={(e) => setAchievementCode(e.target.value)}
                            />
                          </div>
                          <div>
                            <input
                              type="number"
                              className="input-field"
                              placeholder="Sıra"
                              value={order}
                              onChange={(e) => setOrder(e.target.value === '' ? '' : Number(e.target.value))}
                            />
                          </div>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="published"
                            type="checkbox"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={isPublished}
                            onChange={(e) => setIsPublished(e.target.checked)}
                          />
                          <label htmlFor="published" className="ml-2 block text-sm text-gray-900 dark:text-white">
                            Yayınla
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                    <button
                      type="button"
                      className="btn-primary w-full sm:col-start-2"
                      onClick={create}
                      disabled={loading || !title}
                    >
                      Oluştur
                    </button>
                    <button
                      type="button"
                      className="btn-secondary w-full mt-3 sm:col-start-1 sm:mt-0"
                      onClick={() => setShowCreateModal(false)}
                    >
                      İptal
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </Layout>
  )
}
