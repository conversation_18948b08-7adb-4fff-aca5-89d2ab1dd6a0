package entities

import (
	"github.com/google/uuid"
)

// ContentType represents different types of study content
type ContentType string

const (
	ContentTypeBook     ContentType = "book"
	ContentTypeVideo    ContentType = "video"
	ContentTypePlaylist ContentType = "playlist"
	ContentTypeQuestion ContentType = "question"
	ContentTypePDF      ContentType = "pdf"
)

// Content represents study materials (books, videos, questions, etc.)
type Content struct {
	Base
	Title       string      `json:"title" gorm:"not null"`
	Description *string     `json:"description"`
	Type        ContentType `json:"type" gorm:"type:varchar(20);not null"`
	URL         *string     `json:"url"`                              // For videos, playlists, PDFs
	TotalPages  *int        `json:"total_pages"`                      // For books, PDFs
	TotalTime   *int        `json:"total_time"`                       // For videos in seconds
	Subject     *string     `json:"subject"`                          // KPSS subject area
	Difficulty  *string     `json:"difficulty"`                       // Easy, Medium, Hard
	IsOfficial  bool        `json:"is_official" gorm:"default:false"` // Official KPSS content
	CreatorID   *uuid.UUID  `json:"creator_id" gorm:"type:uuid"`      // User who added this content

	// Metadata for different content types
	Author      *string `json:"author"`    // For books
	Publisher   *string `json:"publisher"` // For books
	Year        *int    `json:"year"`
	ISBN        *string `json:"isbn"`         // For books
	ChannelName *string `json:"channel_name"` // For videos
	Duration    *string `json:"duration"`     // Human readable duration
}

// Progress represents user progress on content
type Progress struct {
	Base
	UserID      *uuid.UUID `json:"user_id"`    // Optional user reference
	ContentID   *uuid.UUID `json:"content_id"` // Optional content reference
	CurrentPage *int       `json:"current_page"`
	CurrentTime *int       `json:"current_time"`
	Percentage  float64    `json:"percentage"`
	IsCompleted bool       `json:"is_completed"`
	Notes       *string    `json:"notes"` // User notes
}

func (Content) TableName() string {
	return "contents"
}

func (Progress) TableName() string {
	return "progress"
}
