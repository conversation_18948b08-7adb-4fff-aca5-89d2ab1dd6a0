export interface QuizCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export interface QuizStatistics {
  total_attempts: number;
  average_score: number;
  completion_rate: number;
  average_time: number; // minutes
  difficulty_rating: number; // 1-5
  popularity_score: number;
}

export interface QuestionOption {
  id: string;
  text: string;
  is_correct: boolean;
  order_index: number;
}

export interface QuestionMedia {
  type: 'image' | 'video' | 'audio';
  url: string;
  alt_text?: string;
  caption?: string;
}

export interface QuizAnswer {
  question_id: string;
  selected_answer: string | string[];
  is_correct: boolean;
  time_taken: number; // seconds
  answered_at: string;
  confidence_level?: number; // 1-5
}

export interface QuizSessionSettings {
  shuffle_questions: boolean;
  shuffle_options: boolean;
  show_correct_answers: boolean;
  allow_review: boolean;
  time_limit_enabled: boolean;
}

export interface SubjectPerformance {
  subject: string;
  correct: number;
  total: number;
  percentage: number;
}

export interface DifficultyPerformance {
  difficulty: 'easy' | 'medium' | 'hard';
  correct: number;
  total: number;
  percentage: number;
}

export interface TimeAnalysis {
  average_time_per_question: number;
  fastest_question: number;
  slowest_question: number;
  time_efficiency: number; // percentage
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'quiz' | 'study' | 'social' | 'milestone';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
  unlocked_at: string;
}

export interface LeaderboardEntry {
  rank: number;
  user_id: string;
  username: string;
  avatar?: string;
  score: number;
  percentage: number;
  time_taken: number;
  completed_at: string;
  is_current_user?: boolean;
}

export interface CreateQuestionOptionRequest {
  text: string;
  is_correct: boolean;
}

export interface PerformanceAnalysis {
  strengths: string[];
  weaknesses: string[];
  subject_breakdown: SubjectPerformance[];
  difficulty_breakdown: DifficultyPerformance[];
  time_analysis: TimeAnalysis;
  improvement_suggestions: string[];
}

export interface Quiz {
  id: string;
  title: string;
  description: string;
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  question_count: number;
  time_limit: number; // minutes
  is_public: boolean;
  is_featured: boolean;
  created_by: string;
  creator_name: string;
  creator_avatar?: string;
  created_at: string;
  updated_at: string;
  tags: string[];
  category: QuizCategory;
  statistics: QuizStatistics;
  is_favorite?: boolean;
  user_attempts?: number;
  best_score?: number;
}

export interface Question {
  id: string;
  quiz_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'fill_blank' | 'essay';
  options?: QuestionOption[];
  correct_answer: string | string[];
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  time_limit?: number; // seconds
  order_index: number;
  tags: string[];
  media?: QuestionMedia;
}

export interface QuizSession {
  id: string;
  quiz_id: string;
  user_id: string;
  started_at: string;
  status: 'active' | 'paused' | 'completed' | 'abandoned';
  current_question_index: number;
  time_remaining: number; // seconds
  answers: QuizAnswer[];
  settings: QuizSessionSettings;
}

export interface QuizResult {
  id: string;
  quiz_id: string;
  user_id: string;
  session_id: string;
  score: number;
  percentage: number;
  total_questions: number;
  correct_answers: number;
  time_taken: number; // seconds
  completed_at: string;
  answers: QuizAnswer[];
  performance_analysis: PerformanceAnalysis;
  achievements?: Achievement[];
}

export interface QuizLeaderboard {
  quiz_id: string;
  entries: LeaderboardEntry[];
  user_rank?: number;
  total_participants: number;
}

export interface CreateQuestionRequest {
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'fill_blank' | 'essay';
  options?: CreateQuestionOptionRequest[];
  correct_answer: string | string[];
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  time_limit?: number;
  tags: string[];
}

export interface CreateQuizRequest {
  title: string;
  description: string;
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  time_limit: number;
  is_public: boolean;
  tags: string[];
  category_id: string;
  questions: CreateQuestionRequest[];
}

export interface StartQuizRequest {
  quiz_id: string;
  settings?: QuizSessionSettings;
}

export interface SubmitAnswerRequest {
  session_id: string;
  question_id: string;
  selected_answer: string | string[];
  time_taken: number;
  confidence_level?: number;
}

export interface QuizFilters {
  subject?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  category_id?: string;
  tags?: string[];
  created_by?: string;
  is_featured?: boolean;
  min_questions?: number;
  max_questions?: number;
  min_time?: number;
  max_time?: number;
  search?: string;
}

export interface QuizListResponse {
  quizzes: Quiz[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}