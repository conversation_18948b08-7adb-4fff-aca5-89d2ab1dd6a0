package timeline

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	// Timeline management
	CreateTimelineEntry(userID string, payload *dtos.CreateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error)
	GetUserTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error)
	GetFriendTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error)
	GetPublicTimeline(payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error)
	UpdateTimelineEntry(entryID string, payload *dtos.UpdateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error)
	DeleteTimelineEntry(entryID string) error
	GetTimelineEntry(entryID string) (*dtos.TimelineEntryResponse, error)

	// Timeline interactions
	LikeTimelineEntry(userID string, entryID string) (*dtos.TimelineLikeResponse, error)
	UnlikeTimelineEntry(userID string, entryID string) error
	IsTimelineEntryLiked(userID string, entryID string) (bool, error)
	CommentOnTimelineEntry(userID string, entryID string, payload *dtos.CreateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error)
	UpdateTimelineComment(commentID string, payload *dtos.UpdateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error)
	DeleteTimelineComment(commentID string) error
	GetTimelineComment(commentID string) (*dtos.TimelineCommentResponse, error)
	GetTimelineEntryComments(entryID string, payload *dtos.GetTimelineCommentsRequest) (*dtos.TimelineCommentsResponse, error)

	// Activity tracking
	TrackStudyActivity(userID string, payload *dtos.StudyActivityRequest) error
	TrackQuizActivity(userID string, payload *dtos.QuizActivityRequest) error
	TrackProgressActivity(userID string, payload *dtos.ProgressActivityRequest) error
	TrackSocialActivity(userID string, payload *dtos.SocialActivityRequest) error
	TrackAchievementActivity(userID string, payload *dtos.AchievementActivityRequest) error

	// Timeline analytics
	GetTimelineStats(userID string) (*dtos.TimelineStatsResponse, error)
	GetActivitySummary(userID string, payload *dtos.GetActivitySummaryRequest) (*dtos.ActivitySummaryResponse, error)
	GetEngagementStats(userID string, entryID string) (*dtos.EngagementStatsResponse, error)

	// Timeline privacy and filtering
	UpdateTimelinePrivacy(userID string, payload *dtos.UpdateTimelinePrivacyRequest) (*dtos.TimelinePrivacyResponse, error)
	GetTimelinePrivacy(userID string) (*dtos.TimelinePrivacyResponse, error)
	HideTimelineEntry(userID string, entryID string) error
	ReportTimelineEntry(userID string, entryID string, payload *dtos.ReportTimelineEntryRequest) error

	// Notification helpers
	CreateTimelineLikeNotification(ownerID string, likerID string, entryID string) error
	CreateTimelineCommentNotification(ownerID string, commenterID string, entryID string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Timeline management
func (r *repository) CreateTimelineEntry(userID string, payload *dtos.CreateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error) {
	entry := entities.TimelineEntry{
		UserID:      uuid.MustParse(userID),
		Type:        entities.TimelineEntryType(payload.Type),
		Title:       payload.Title,
		Description: payload.Description,
		Visibility:  entities.TimelineVisibility(payload.Visibility),
		Metadata:    payload.Metadata,
		ContentID:   payload.ContentID,
		QuizID:      payload.QuizID,
	}

	err := r.db.Create(&entry).Error
	if err != nil {
		return nil, errors.New("Failed to create timeline entry")
	}

	return r.getTimelineEntryResponse(&entry, &userID)
}

func (r *repository) GetUserTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error) {
	// TODO: Implement comprehensive user timeline with pagination and filtering
	var entries []entities.TimelineEntry
	query := r.db.Preload("User").Where("user_id = ?", userID)

	if payload.Type != "" {
		query = query.Where("type = ?", payload.Type)
	}

	err := query.Order("created_at DESC").Find(&entries).Error
	if err != nil {
		return nil, errors.New("Failed to get user timeline")
	}

	entryResponses := make([]dtos.TimelineEntryResponse, len(entries))
	for i, entry := range entries {
		response, err := r.getTimelineEntryResponse(&entry, &userID)
		if err != nil {
			continue
		}
		entryResponses[i] = *response
	}

	return &dtos.TimelineResponse{
		Entries:    entryResponses,
		Total:      len(entryResponses),
		Page:       1,
		Limit:      len(entryResponses),
		TotalPages: 1,
	}, nil
}

func (r *repository) GetFriendTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error) {
    // Collect friend IDs (accepted friendships, both directions)
    var friendIDs []uuid.UUID
    type pair struct{ ID uuid.UUID }

    var rows []pair
    // Friendships where current user is requester and accepted
    r.db.Raw("SELECT addressee_id as id FROM friendships WHERE requester_id = ? AND status = 'accepted'", userID).Scan(&rows)
    for _, p := range rows { friendIDs = append(friendIDs, p.ID) }
    rows = []pair{}
    // Friendships where current user is addressee and accepted
    r.db.Raw("SELECT requester_id as id FROM friendships WHERE addressee_id = ? AND status = 'accepted'", userID).Scan(&rows)
    for _, p := range rows { friendIDs = append(friendIDs, p.ID) }

    // Following IDs (users current user follows)
    followingIDs := []uuid.UUID{}
    rows = []pair{}
    r.db.Raw("SELECT following_id as id FROM follows WHERE follower_id = ?", userID).Scan(&rows)
    for _, p := range rows { followingIDs = append(followingIDs, p.ID) }

    // Build query: friends -> public or friends; following -> public only
    var entries []entities.TimelineEntry
    q := r.db.Model(&entities.TimelineEntry{})
    if len(friendIDs) > 0 {
        q = q.Or("user_id IN ? AND visibility IN ?", friendIDs, []entities.TimelineVisibility{entities.TimelineVisibilityPublic, entities.TimelineVisibilityFriends})
    }
    if len(followingIDs) > 0 {
        q = q.Or("user_id IN ? AND visibility = ?", followingIDs, entities.TimelineVisibilityPublic)
    }

    if payload != nil && payload.Type != "" {
        q = q.Where("type = ?", payload.Type)
    }

    // Defaults
    page := 1
    limit := 20
    if payload != nil {
        if payload.Page > 0 { page = payload.Page }
        if payload.Limit > 0 && payload.Limit <= 100 { limit = payload.Limit }
    }
    offset := (page - 1) * limit

    var total int64
    q.Count(&total)

    err := q.Order("created_at DESC").Offset(offset).Limit(limit).Find(&entries).Error
    if err != nil {
        return nil, errors.New("Failed to get friend timeline")
    }

    entryResponses := make([]dtos.TimelineEntryResponse, len(entries))
    for i, entry := range entries {
        uid := userID
        resp, err := r.getTimelineEntryResponse(&entry, &uid)
        if err != nil { continue }
        entryResponses[i] = *resp
    }
    totalPages := int((total + int64(limit) - 1) / int64(limit))
    return &dtos.TimelineResponse{
        Entries:    entryResponses,
        Total:      int(total),
        Page:       page,
        Limit:      limit,
        TotalPages: totalPages,
    }, nil
}

func (r *repository) GetPublicTimeline(payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error) {
	// TODO: Implement public timeline
	var entries []entities.TimelineEntry
	query := r.db.Preload("User").Where("visibility = ?", entities.TimelineVisibilityPublic)

	if payload.Type != "" {
		query = query.Where("type = ?", payload.Type)
	}

	err := query.Order("created_at DESC").Limit(20).Find(&entries).Error
	if err != nil {
		return nil, errors.New("Failed to get public timeline")
	}

	entryResponses := make([]dtos.TimelineEntryResponse, len(entries))
	for i, entry := range entries {
		response, err := r.getTimelineEntryResponse(&entry, nil)
		if err != nil {
			continue
		}
		entryResponses[i] = *response
	}

	return &dtos.TimelineResponse{
		Entries:    entryResponses,
		Total:      len(entryResponses),
		Page:       1,
		Limit:      20,
		TotalPages: 1,
	}, nil
}

func (r *repository) UpdateTimelineEntry(entryID string, payload *dtos.UpdateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error) {
	var entry entities.TimelineEntry
	err := r.db.Where("id = ?", entryID).First(&entry).Error
	if err != nil {
		return nil, errors.New("Timeline entry not found")
	}

	updates := make(map[string]interface{})
	if payload.Title != nil {
		updates["title"] = *payload.Title
	}
	if payload.Description != nil {
		updates["description"] = *payload.Description
	}
	if payload.Visibility != nil {
		updates["visibility"] = *payload.Visibility
	}
	if payload.Metadata != nil {
		updates["metadata"] = payload.Metadata
	}

	if len(updates) > 0 {
		err = r.db.Model(&entry).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update timeline entry")
		}
	}

	// Reload entry
	r.db.Preload("User").Where("id = ?", entryID).First(&entry)

	userID := entry.UserID.String()
	return r.getTimelineEntryResponse(&entry, &userID)
}

func (r *repository) DeleteTimelineEntry(entryID string) error {
	err := r.db.Where("id = ?", entryID).Delete(&entities.TimelineEntry{}).Error
	if err != nil {
		return errors.New("Failed to delete timeline entry")
	}
	return nil
}

func (r *repository) GetTimelineEntry(entryID string) (*dtos.TimelineEntryResponse, error) {
	var entry entities.TimelineEntry
	err := r.db.Preload("User").Where("id = ?", entryID).First(&entry).Error
	if err != nil {
		return nil, errors.New("Timeline entry not found")
	}

	userID := entry.UserID.String()
	return r.getTimelineEntryResponse(&entry, &userID)
}

// Helper method to convert entity to response
func (r *repository) getTimelineEntryResponse(entry *entities.TimelineEntry, currentUserID *string) (*dtos.TimelineEntryResponse, error) {
	// Get likes count
	var likesCount int64
	r.db.Model(&entities.TimelineLike{}).Where("entry_id = ?", entry.ID).Count(&likesCount)

	// Get comments count
	var commentsCount int64
	r.db.Model(&entities.TimelineComment{}).Where("entry_id = ?", entry.ID).Count(&commentsCount)

	// Check if current user liked this entry
	isLiked := false
	isOwner := false
	if currentUserID != nil {
		var likeCount int64
		r.db.Model(&entities.TimelineLike{}).Where("entry_id = ? AND user_id = ?", entry.ID, *currentUserID).Count(&likeCount)
		isLiked = likeCount > 0
		isOwner = entry.UserID.String() == *currentUserID
	}

	// Get user info separately
	var user entities.User
	r.db.Where("id = ?", entry.UserID).First(&user)

	userInfo := dtos.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Name:       user.Name,
		Email:      user.Email,
		IsVerified: user.IsVerified,
		CreatedAt:  user.CreatedAt,
	}

	response := &dtos.TimelineEntryResponse{
		ID:            entry.ID,
		UserID:        entry.UserID,
		Type:          string(entry.Type),
		Title:         entry.Title,
		Description:   entry.Description,
		Visibility:    string(entry.Visibility),
		Metadata:      entry.Metadata,
		ContentID:     entry.ContentID,
		QuizID:        entry.QuizID,
		User:          userInfo,
		LikesCount:    int(likesCount),
		CommentsCount: int(commentsCount),
		IsLiked:       isLiked,
		IsOwner:       isOwner,
		CreatedAt:     entry.CreatedAt,
		UpdatedAt:     entry.UpdatedAt,
	}

	return response, nil
}

// Timeline interactions (placeholder implementations)
func (r *repository) LikeTimelineEntry(userID string, entryID string) (*dtos.TimelineLikeResponse, error) {
	like := entities.TimelineLike{
		UserID:          uuid.MustParse(userID),
		TimelineEntryID: uuid.MustParse(entryID),
	}

	err := r.db.Create(&like).Error
	if err != nil {
		return nil, errors.New("Failed to like timeline entry")
	}

	// Load user for response
	var user entities.User
	r.db.Where("id = ?", userID).First(&user)

	return &dtos.TimelineLikeResponse{
		ID:      like.ID,
		UserID:  like.UserID,
		EntryID: like.TimelineEntryID,
		User: dtos.UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
		},
		CreatedAt: like.CreatedAt,
	}, nil
}

func (r *repository) UnlikeTimelineEntry(userID string, entryID string) error {
	err := r.db.Where("user_id = ? AND entry_id = ?", userID, entryID).Delete(&entities.TimelineLike{}).Error
	if err != nil {
		return errors.New("Failed to unlike timeline entry")
	}
	return nil
}

func (r *repository) IsTimelineEntryLiked(userID string, entryID string) (bool, error) {
	var count int64
	err := r.db.Model(&entities.TimelineLike{}).Where("user_id = ? AND entry_id = ?", userID, entryID).Count(&count).Error
	return count > 0, err
}

func (r *repository) CommentOnTimelineEntry(userID string, entryID string, payload *dtos.CreateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error) {
	comment := entities.TimelineComment{
		UserID:          uuid.MustParse(userID),
		TimelineEntryID: uuid.MustParse(entryID),
		Content:         payload.Content,
	}

	err := r.db.Create(&comment).Error
	if err != nil {
		return nil, errors.New("Failed to create comment")
	}

	// Load user for response
	var user entities.User
	r.db.Where("id = ?", userID).First(&user)

	return &dtos.TimelineCommentResponse{
		ID:      comment.ID,
		UserID:  comment.UserID,
		EntryID: comment.TimelineEntryID,
		Content: comment.Content,
		User: dtos.UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
		},
		LikesCount: 0,
		IsLiked:    false,
		IsOwner:    true,
		CreatedAt:  comment.CreatedAt,
		UpdatedAt:  comment.UpdatedAt,
	}, nil
}

func (r *repository) UpdateTimelineComment(commentID string, payload *dtos.UpdateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error) {
	var comment entities.TimelineComment
	err := r.db.Where("id = ?", commentID).First(&comment).Error
	if err != nil {
		return nil, errors.New("Comment not found")
	}

	comment.Content = payload.Content
	err = r.db.Save(&comment).Error
	if err != nil {
		return nil, errors.New("Failed to update comment")
	}

	// Load user for response
	var user entities.User
	r.db.Where("id = ?", comment.UserID).First(&user)

	return &dtos.TimelineCommentResponse{
		ID:      comment.ID,
		UserID:  comment.UserID,
		EntryID: comment.TimelineEntryID,
		Content: comment.Content,
		User: dtos.UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Name:     user.Name,
		},
		LikesCount: 0,
		IsLiked:    false,
		IsOwner:    true,
		CreatedAt:  comment.CreatedAt,
		UpdatedAt:  comment.UpdatedAt,
	}, nil
}

func (r *repository) DeleteTimelineComment(commentID string) error {
	err := r.db.Where("id = ?", commentID).Delete(&entities.TimelineComment{}).Error
	if err != nil {
		return errors.New("Failed to delete comment")
	}
	return nil
}

func (r *repository) GetTimelineComment(commentID string) (*dtos.TimelineCommentResponse, error) {
	var comment entities.TimelineComment
	err := r.db.Preload("User").Where("id = ?", commentID).First(&comment).Error
	if err != nil {
		return nil, errors.New("Comment not found")
	}

	// Get user info separately
	var user entities.User
	r.db.Where("id = ?", comment.UserID).First(&user)

	return &dtos.TimelineCommentResponse{
		ID:      comment.ID,
		UserID:  comment.UserID,
		EntryID: comment.TimelineEntryID,
		Content: comment.Content,
		User: dtos.UserInfo{
			ID:         user.ID,
			Username:   user.Username,
			Name:       user.Name,
			Email:      user.Email,
			IsVerified: user.IsVerified,
			CreatedAt:  user.CreatedAt,
		},
		LikesCount: 0,
		IsLiked:    false,
		IsOwner:    false,
		CreatedAt:  comment.CreatedAt,
		UpdatedAt:  comment.UpdatedAt,
	}, nil
}

func (r *repository) GetTimelineEntryComments(entryID string, payload *dtos.GetTimelineCommentsRequest) (*dtos.TimelineCommentsResponse, error) {
	// TODO: Implement comprehensive comment retrieval with pagination
	var comments []entities.TimelineComment
	err := r.db.Preload("User").Where("timeline_entry_id = ?", entryID).Order("created_at ASC").Find(&comments).Error
	if err != nil {
		return nil, errors.New("Failed to get comments")
	}

	commentResponses := make([]dtos.TimelineCommentResponse, len(comments))
	for i, comment := range comments {
		// Get user info separately
		var user entities.User
		r.db.Where("id = ?", comment.UserID).First(&user)

		commentResponses[i] = dtos.TimelineCommentResponse{
			ID:      comment.ID,
			UserID:  comment.UserID,
			EntryID: comment.TimelineEntryID,
			Content: comment.Content,
			User: dtos.UserInfo{
				ID:         user.ID,
				Username:   user.Username,
				Name:       user.Name,
				Email:      user.Email,
				IsVerified: user.IsVerified,
				CreatedAt:  user.CreatedAt,
			},
			LikesCount: 0,
			IsLiked:    false,
			IsOwner:    false,
			CreatedAt:  comment.CreatedAt,
			UpdatedAt:  comment.UpdatedAt,
		}
	}

	return &dtos.TimelineCommentsResponse{
		Comments:   commentResponses,
		Total:      len(commentResponses),
		Page:       1,
		Limit:      len(commentResponses),
		TotalPages: 1,
	}, nil
}

// Activity tracking (placeholder implementations)
func (r *repository) TrackStudyActivity(userID string, payload *dtos.StudyActivityRequest) error {
	// TODO: Create timeline entry for study activity
	return nil
}

func (r *repository) TrackQuizActivity(userID string, payload *dtos.QuizActivityRequest) error {
	// TODO: Create timeline entry for quiz activity
	return nil
}

func (r *repository) TrackProgressActivity(userID string, payload *dtos.ProgressActivityRequest) error {
	// TODO: Create timeline entry for progress activity
	return nil
}

func (r *repository) TrackSocialActivity(userID string, payload *dtos.SocialActivityRequest) error {
	// TODO: Create timeline entry for social activity
	return nil
}

func (r *repository) TrackAchievementActivity(userID string, payload *dtos.AchievementActivityRequest) error {
	// TODO: Create timeline entry for achievement activity
	return nil
}

// Timeline analytics (placeholder implementations)
func (r *repository) GetTimelineStats(userID string) (*dtos.TimelineStatsResponse, error) {
	// TODO: Implement comprehensive timeline statistics
	return &dtos.TimelineStatsResponse{
		UserID:        uuid.MustParse(userID),
		TotalEntries:  0,
		TotalLikes:    0,
		TotalComments: 0,
	}, nil
}

func (r *repository) GetActivitySummary(userID string, payload *dtos.GetActivitySummaryRequest) (*dtos.ActivitySummaryResponse, error) {
	// TODO: Implement activity summary
	return &dtos.ActivitySummaryResponse{
		Period:            "custom",
		StartDate:         payload.StartDate,
		EndDate:           payload.EndDate,
		TotalActivities:   0,
		DailyActivities:   []dtos.DailyActivitySummary{},
		ActivityBreakdown: make(map[string]int),
	}, nil
}

func (r *repository) GetEngagementStats(userID string, entryID string) (*dtos.EngagementStatsResponse, error) {
	// TODO: Implement engagement statistics
	return &dtos.EngagementStatsResponse{
		EntryID:          uuid.MustParse(entryID),
		TotalLikes:       0,
		TotalComments:    0,
		RecentLikes:      []dtos.TimelineLikeResponse{},
		RecentComments:   []dtos.TimelineCommentResponse{},
		DailyEngagement:  []dtos.DailyEngagementStat{},
		HourlyEngagement: []dtos.HourlyEngagementStat{},
	}, nil
}

// Timeline privacy and filtering (placeholder implementations)
func (r *repository) UpdateTimelinePrivacy(userID string, payload *dtos.UpdateTimelinePrivacyRequest) (*dtos.TimelinePrivacyResponse, error) {
	// TODO: Implement timeline privacy settings
	return &dtos.TimelinePrivacyResponse{
		UserID:               uuid.MustParse(userID),
		DefaultVisibility:    "friends",
		AllowComments:        true,
		AllowLikes:           true,
		ShowStudyActivity:    true,
		ShowQuizActivity:     true,
		ShowProgressActivity: true,
		ShowSocialActivity:   true,
		UpdatedAt:            time.Now(),
	}, nil
}

func (r *repository) GetTimelinePrivacy(userID string) (*dtos.TimelinePrivacyResponse, error) {
	// TODO: Implement timeline privacy retrieval
	return &dtos.TimelinePrivacyResponse{
		UserID:               uuid.MustParse(userID),
		DefaultVisibility:    "friends",
		AllowComments:        true,
		AllowLikes:           true,
		ShowStudyActivity:    true,
		ShowQuizActivity:     true,
		ShowProgressActivity: true,
		ShowSocialActivity:   true,
		UpdatedAt:            time.Now(),
	}, nil
}

func (r *repository) HideTimelineEntry(userID string, entryID string) error {
	// TODO: Implement timeline entry hiding
	return nil
}

func (r *repository) ReportTimelineEntry(userID string, entryID string, payload *dtos.ReportTimelineEntryRequest) error {
	// TODO: Implement timeline entry reporting
	return nil
}

// Notification helpers (placeholder implementations)
func (r *repository) CreateTimelineLikeNotification(ownerID string, likerID string, entryID string) error {
	// TODO: Create notification for timeline like
	return nil
}

func (r *repository) CreateTimelineCommentNotification(ownerID string, commenterID string, entryID string) error {
	// TODO: Create notification for timeline comment
	return nil
}
