import http from './httpClient';
import { endpoints } from './endpoints';
import { ApiResponse } from '../types/api';

export interface User {
  id: string;
  username: string;
  email: string;
  full_name: string;
  avatar?: string;
  bio?: string;
  level: number;
  total_points: number;
  is_online: boolean;
  last_seen: string;
  created_at: string;
}

export interface Friend {
  id: string;
  user: User;
  status: 'pending' | 'accepted' | 'blocked';
  created_at: string;
  accepted_at?: string;
}

export interface FriendRequest {
  id: string;
  from_user: User;
  to_user: User;
  status: 'pending' | 'accepted' | 'rejected';
  message?: string;
  created_at: string;
}

export interface SocialStats {
  friends_count: number;
  followers_count: number;
  following_count: number;
  mutual_friends_count?: number;
}

export interface Activity {
  id: string;
  user: User;
  type: 'quiz_completed' | 'achievement_unlocked' | 'level_up' | 'friend_added' | 'content_completed';
  title: string;
  description: string;
  metadata?: any;
  created_at: string;
}

export interface SearchResult {
  users: User[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

export const socialService = {
  // Friends
  async getFriends(params?: { page?: number; limit?: number }): Promise<ApiResponse<Friend[]>> {
    return http.get(endpoints.social.friends.list, { params });
  },

  async sendFriendRequest(data: { user_id: string; message?: string }): Promise<ApiResponse<FriendRequest>> {
    return http.post(endpoints.social.friends.request, data);
  },

  async respondToFriendRequest(data: { request_id: string; action: 'accept' | 'reject' }): Promise<ApiResponse<void>> {
    return http.post(endpoints.social.friends.respond, data);
  },

  async getFriendRequests(type: 'sent' | 'received'): Promise<ApiResponse<FriendRequest[]>> {
    return http.get(endpoints.social.friends.requests(type));
  },

  async removeFriend(friendId: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.social.friends.remove(friendId));
  },

  // Follow system
  async followUser(userId: string): Promise<ApiResponse<void>> {
    return http.post(endpoints.social.follow.follow, { user_id: userId });
  },

  async unfollowUser(userId: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.social.follow.unfollow(userId));
  },

  async getFollowers(params?: { page?: number; limit?: number }): Promise<ApiResponse<User[]>> {
    return http.get(endpoints.social.follow.followers, { params });
  },

  async getFollowing(params?: { page?: number; limit?: number }): Promise<ApiResponse<User[]>> {
    return http.get(endpoints.social.follow.following, { params });
  },

  // Search and suggestions
  async searchUsers(query: string, params?: { page?: number; limit?: number }): Promise<ApiResponse<SearchResult>> {
    return http.get(endpoints.social.search.users, { 
      params: { q: query, ...params } 
    });
  },

  async getFriendSuggestions(params?: { page?: number; limit?: number }): Promise<ApiResponse<User[]>> {
    return http.get(endpoints.social.search.suggestions, { params });
  },

  async getMutualFriends(userId: string): Promise<ApiResponse<User[]>> {
    return http.get(endpoints.social.search.mutualFriends(userId));
  },

  // Block system
  async blockUser(userId: string): Promise<ApiResponse<void>> {
    return http.post(endpoints.social.block.block, { user_id: userId });
  },

  async unblockUser(userId: string): Promise<ApiResponse<void>> {
    return http.delete(endpoints.social.block.unblock(userId));
  },

  async getBlockedUsers(): Promise<ApiResponse<User[]>> {
    return http.get(endpoints.social.block.list);
  },

  // Activity and profile
  async getActivity(params?: { page?: number; limit?: number; user_id?: string }): Promise<ApiResponse<Activity[]>> {
    return http.get(endpoints.social.activity, { params });
  },

  async getUserProfile(userId: string): Promise<ApiResponse<User & SocialStats>> {
    return http.get(endpoints.social.profile(userId));
  },

  async getSocialStats(): Promise<ApiResponse<SocialStats>> {
    return http.get(endpoints.social.stats);
  },

  // Privacy settings
  async getPrivacySettings(): Promise<ApiResponse<any>> {
    return http.get(endpoints.social.privacy);
  },

  async updatePrivacySettings(settings: any): Promise<ApiResponse<any>> {
    return http.put(endpoints.social.privacy, settings);
  }
};

export default socialService;
