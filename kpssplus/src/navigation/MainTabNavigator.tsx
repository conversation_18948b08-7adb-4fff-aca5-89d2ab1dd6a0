import React, {useCallback} from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/Ionicons';
import {MainTabParamList} from './types';
import {ContentStackNavigator} from './ContentStackNavigator';
import {QuizStackNavigator} from './QuizStackNavigator';
import {BattleStackNavigator} from './BattleStackNavigator';
import {SocialStackNavigator} from './SocialStackNavigator';
import {MenuStackNavigator} from './MenuStackNavigator';
import {colors} from '../theme/colors';

const Tab = createBottomTabNavigator<MainTabParamList>();

// Tab bar icon component moved outside to avoid re-creation on each render
const TabBarIcon: React.FC<{
  focused: boolean;
  color: string;
  size: number;
  routeName: keyof MainTabParamList;
}> = ({focused, color, size, routeName}) => {
  let iconName: string;

  switch (routeName) {
    case 'Content':
      iconName = focused ? 'book' : 'book-outline';
      break;
    case 'Quiz':
      iconName = focused ? 'clipboard' : 'clipboard-outline';
      break;
    case 'Battle':
      iconName = focused ? 'trophy' : 'trophy-outline';
      break;
    case 'Social':
      iconName = focused ? 'people' : 'people-outline';
      break;
    case 'Menu':
      iconName = focused ? 'menu' : 'menu-outline';
      break;
    default:
      iconName = 'circle';
  }

  return <Icon name={iconName} size={size} color={color} />;
};

export const MainTabNavigator: React.FC = () => {
  const renderTabBarIcon = useCallback(
    ({
      focused,
      color,
      size,
      route,
    }: {
      focused: boolean;
      color: string;
      size: number;
      route: {name: keyof MainTabParamList};
    }) => (
      <TabBarIcon
        focused={focused}
        color={color}
        size={size}
        routeName={route.name}
      />
    ),
    [],
  );

  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: props => renderTabBarIcon({...props, route}),
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.gray[500],
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopColor: colors.gray[200],
          borderTopWidth: 1,
          paddingBottom: 8,
          paddingTop: 8,
          height: 80, // Fixed height for visibility
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: 4,
        },
        headerShown: false,
      })}>
      <Tab.Screen
        name="Content"
        component={ContentStackNavigator}
        options={{
          tabBarLabel: 'Dersler',
        }}
      />
      <Tab.Screen
        name="Quiz"
        component={QuizStackNavigator}
        options={{
          tabBarLabel: 'Quiz',
        }}
      />
      <Tab.Screen
        name="Battle"
        component={BattleStackNavigator}
        options={{
          tabBarLabel: 'Kapışma',
        }}
      />
      <Tab.Screen
        name="Social"
        component={SocialStackNavigator}
        options={{
          tabBarLabel: 'Sosyal',
        }}
      />
      <Tab.Screen
        name="Menu"
        component={MenuStackNavigator}
        options={{
          tabBarLabel: 'Menü',
        }}
      />
    </Tab.Navigator>
  );
};
