package database

import (
	"fmt"
	"log"
	"sync"

	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db          *gorm.DB
	err         error
	client_once sync.Once
)

func InitDB(dbc config.Database) {
	client_once.Do(func() {
		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul", dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name)
		db, err = gorm.Open(
			postgres.New(
				postgres.Config{
					DSN:                  dsn,
					PreferSimpleProtocol: true, // daha az kaynak
				},
			),
		)
		if err != nil {
			panic(err)
		}
		db.AutoMigrate(
			&entities.Log{},
			&entities.Version{},
			&entities.User{},
			&entities.Friendship{},
			&entities.Follow{},
			&entities.Content{},
			&entities.Progress{},
			&entities.Topic{},
			&entities.UserTopicProgress{},
			&entities.Quiz{},
			&entities.Question{},
			&entities.QuizResult{},
			&entities.QuizAnswer{},
			&entities.QuizSession{},
			&entities.QuizSessionAnswer{},
			&entities.TimelineEntry{},
			&entities.TimelineLike{},
			&entities.TimelineComment{},
			&entities.Notification{},
			&entities.NotificationSettings{},
			&entities.Badge{},
			&entities.UserBadge{},
			&entities.LibraryFolder{},
			&entities.LibraryItem{},
			&entities.UserPreferences{},
			&entities.Leaderboard{},
			&entities.LeaderboardEntry{},
			&entities.StudySession{},
			&entities.UserStats{},
			&entities.DailyStats{},
			&entities.WeeklyReport{},
		)

		db.Exec("SET TIME ZONE 'Europe/Istanbul';")
	})
}

func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("Postgres is not initialized. Call InitDB first.")
	}
	return db
}
