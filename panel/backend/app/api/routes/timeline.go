package routes

import (
    "github.com/gin-gonic/gin"
    "github.com/kpss-plus-backend/pkg/domains/timeline"
    "github.com/kpss-plus-backend/pkg/dtos"
    "github.com/kpss-plus-backend/pkg/middleware"
    "github.com/kpss-plus-backend/pkg/state"
)

func TimelineRoutes(r *gin.RouterGroup, s timeline.Service) {
    grp := r.Group("/timeline")

    // Public timeline
    grp.GET("/public", GetPublicTimeline(s))

    // Protected routes
    grp.Use(middleware.Authorized())

    // Feed and personal timeline
    grp.GET("", GetUserTimeline(s))
    grp.GET("/feed", GetFriendTimeline(s))

    // Create/update/delete entries
    grp.POST("", CreateTimelineEntry(s))
    grp.PUT("/:id", UpdateTimelineEntry(s))
    grp.DELETE("/:id", DeleteTimelineEntry(s))

    // Likes
    grp.POST("/:id/like", LikeTimelineEntry(s))
    grp.DELETE("/:id/like", UnlikeTimelineEntry(s))

    // Comments
    grp.GET("/:id/comments", GetTimelineComments(s))
    grp.POST("/:id/comments", CreateTimelineComment(s))
    grp.PUT("/comments/:commentId", UpdateTimelineComment(s))
    grp.DELETE("/comments/:commentId", DeleteTimelineComment(s))

    // Analytics
    grp.GET("/stats", GetTimelineStats(s))
    grp.GET("/summary", GetActivitySummary(s))
    grp.GET("/:id/engagement", GetEngagementStats(s))

    // Privacy and moderation
    grp.GET("/privacy", GetTimelinePrivacy(s))
    grp.PUT("/privacy", UpdateTimelinePrivacy(s))
    grp.POST("/:id/hide", HideTimelineEntry(s))
    grp.POST("/:id/report", ReportTimelineEntry(s))
}

func CreateTimelineEntry(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.CreateTimelineEntryRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.CreateTimelineEntry(userID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(201, gin.H{"data": resp, "status": 201})
    }
}

func GetUserTimeline(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.GetTimelineRequest
        if err := c.ShouldBindQuery(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.GetUserTimeline(userID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func GetFriendTimeline(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.GetTimelineRequest
        if err := c.ShouldBindQuery(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.GetFriendTimeline(userID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func GetPublicTimeline(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        var req dtos.GetTimelineRequest
        if err := c.ShouldBindQuery(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.GetPublicTimeline(&req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func UpdateTimelineEntry(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        id := c.Param("id")
        var req dtos.UpdateTimelineEntryRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.UpdateTimelineEntry(userID, id, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func DeleteTimelineEntry(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        id := c.Param("id")
        if err := s.DeleteTimelineEntry(userID, id); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "Timeline entry deleted"}, "status": 200})
    }
}

func LikeTimelineEntry(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        id := c.Param("id")
        resp, err := s.LikeTimelineEntry(userID, id)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(201, gin.H{"data": resp, "status": 201})
    }
}

func UnlikeTimelineEntry(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        id := c.Param("id")
        if err := s.UnlikeTimelineEntry(userID, id); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "Unliked"}, "status": 200})
    }
}

func CreateTimelineComment(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        id := c.Param("id")
        var req dtos.CreateTimelineCommentRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.CommentOnTimelineEntry(userID, id, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(201, gin.H{"data": resp, "status": 201})
    }
}

func UpdateTimelineComment(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        commentID := c.Param("commentId")
        var req dtos.UpdateTimelineCommentRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.UpdateTimelineComment(userID, commentID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func DeleteTimelineComment(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        commentID := c.Param("commentId")
        if err := s.DeleteTimelineComment(userID, commentID); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "Comment deleted"}, "status": 200})
    }
}

func GetTimelineComments(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        entryID := c.Param("id")
        var req dtos.GetTimelineCommentsRequest
        if err := c.ShouldBindQuery(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.GetTimelineEntryComments(entryID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func GetTimelineStats(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        resp, err := s.GetTimelineStats(userID)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func GetActivitySummary(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.GetActivitySummaryRequest
        if err := c.ShouldBindQuery(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.GetActivitySummary(userID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func GetEngagementStats(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        _ = userID // reserved for permissions later
        entryID := c.Param("id")
        resp, err := s.GetEngagementStats(userID, entryID)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func GetTimelinePrivacy(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        resp, err := s.GetTimelinePrivacy(userID)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func UpdateTimelinePrivacy(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        var req dtos.UpdateTimelinePrivacyRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        resp, err := s.UpdateTimelinePrivacy(userID, &req)
        if err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": resp, "status": 200})
    }
}

func HideTimelineEntry(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        id := c.Param("id")
        if err := s.HideTimelineEntry(userID, id); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "Entry hidden"}, "status": 200})
    }
}

func ReportTimelineEntry(s timeline.Service) func(c *gin.Context) {
    return func(c *gin.Context) {
        userID := c.GetString(state.CurrentUserID)
        id := c.Param("id")
        var req dtos.ReportTimelineEntryRequest
        if err := c.ShouldBindJSON(&req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        if err := s.ReportTimelineEntry(userID, id, &req); err != nil {
            c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
            return
        }
        c.JSON(200, gin.H{"data": gin.H{"message": "Reported"}, "status": 200})
    }
}

