package question

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Repository interface {
	GetAll(limit, offset int, search string) ([]entities.Question, int64, error)
	GetByContentID(contentID uuid.UUID) ([]entities.Question, error)
	GetByTopicID(topicID uuid.UUID) ([]entities.Question, error)
	GetByQuizID(quizID uuid.UUID) ([]entities.Question, error)
	GetByID(id uuid.UUID) (*entities.Question, error)
	Create(question *entities.Question) error
	Update(question *entities.Question) error
	Delete(id uuid.UUID) error
	Publish(id uuid.UUID) error
	Unpublish(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetAll(limit, offset int, search string) ([]entities.Question, int64, error) {
	var questions []entities.Question
	var total int64

    query := r.db.Model(&entities.Question{})

	if search != "" {
		query = query.Where("text ILIKE ?", "%"+search+"%")
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

    err = query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&questions).Error
	return questions, total, err
}

func (r *repository) GetByContentID(contentID uuid.UUID) ([]entities.Question, error) {
	var questions []entities.Question
    err := r.db.
        Where("content_id = ?", contentID).
        Order("created_at DESC").Find(&questions).Error
	return questions, err
}

func (r *repository) GetByTopicID(topicID uuid.UUID) ([]entities.Question, error) {
	var questions []entities.Question
    err := r.db.
        Where("topic_id = ?", topicID).
        Order("created_at DESC").Find(&questions).Error
	return questions, err
}

func (r *repository) GetByQuizID(quizID uuid.UUID) ([]entities.Question, error) {
	var questions []entities.Question
    err := r.db.
        Where("quiz_id = ?", quizID).
        Order("created_at DESC").Find(&questions).Error
	return questions, err
}

func (r *repository) GetByID(id uuid.UUID) (*entities.Question, error) {
	var question entities.Question
    err := r.db.
        First(&question, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &question, nil
}

func (r *repository) Create(question *entities.Question) error {
	return r.db.Create(question).Error
}

func (r *repository) Update(question *entities.Question) error {
	return r.db.Save(question).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Question{}, "id = ?", id).Error
}

func (r *repository) Publish(id uuid.UUID) error {
	return r.db.Model(&entities.Question{}).Where("id = ?", id).Update("is_published", true).Error
}

func (r *repository) Unpublish(id uuid.UUID) error {
	return r.db.Model(&entities.Question{}).Where("id = ?", id).Update("is_published", false).Error
}
