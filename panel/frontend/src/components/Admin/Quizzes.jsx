import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

const Quizzes = () => {
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [showQuizModal, setShowQuizModal] = useState(false);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [editingQuestions, setEditingQuestions] = useState([]);

  const API_URL = process.env.REACT_APP_ADMIN_API_URL || 'http://localhost:8100/api';

  const fetchQuizzes = useCallback(async () => {
    const token = localStorage.getItem('adminToken');
    
    try {
      const response = await axios.get(`${API_URL}/quizzes?limit=20`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setQuizzes(response.data.data || []);
    } catch (err) {
      console.error('Failed to fetch quizzes:', err);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  useEffect(() => {
    fetchQuizzes();
  }, [fetchQuizzes]);

  const handleDeleteQuiz = async (quizId) => {
    if (!window.confirm('Are you sure you want to delete this quiz? All questions will also be deleted.')) {
      return;
    }

    const token = localStorage.getItem('adminToken');
    
    try {
      await axios.delete(`${API_URL}/quizzes/${quizId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      fetchQuizzes();
    } catch (err) {
      console.error('Failed to delete quiz:', err);
    }
  };

  const handleEditQuiz = (quiz) => {
    setSelectedQuiz(quiz);
    setShowQuizModal(true);
  };

  const handleCreateQuiz = () => {
    setSelectedQuiz({
      title: '',
      description: '',
      type: 'official',
      subject: '',
      difficulty: 'medium',
      time_limit: null,
      is_public: true,
      is_active: true
    });
    setShowQuizModal(true);
  };

  const handleSaveQuiz = async (e) => {
    e.preventDefault();
    const token = localStorage.getItem('adminToken');
    
    try {
      const payload = { ...selectedQuiz };
      
      // Remove empty fields
      Object.keys(payload).forEach(key => {
        if (payload[key] === '' || payload[key] === null) {
          delete payload[key];
        }
      });

      let savedQuiz;
      if (selectedQuiz.id) {
        const response = await axios.put(`${API_URL}/quizzes/${selectedQuiz.id}`, payload, {
          headers: { Authorization: `Bearer ${token}` }
        });
        savedQuiz = response.data;
      } else {
        const response = await axios.post(`${API_URL}/quizzes`, payload, {
          headers: { Authorization: `Bearer ${token}` }
        });
        savedQuiz = response.data;
      }
      
      setShowQuizModal(false);
      
      // If this is a new quiz, ask if they want to add questions
      if (!selectedQuiz.id) {
        if (window.confirm('Quiz created successfully! Would you like to add questions now?')) {
          setSelectedQuiz(savedQuiz);
          setEditingQuestions([]);
          setShowQuestionModal(true);
        }
      }
      
      fetchQuizzes();
    } catch (err) {
      console.error('Failed to save quiz:', err);
    }
  };

  const handleManageQuestions = (quiz) => {
    setSelectedQuiz(quiz);
    // Fetch existing questions for this quiz
    const token = localStorage.getItem('adminToken');
    axios.get(`${API_URL}/questions?quiz_id=${quiz.id}`, {
      headers: { Authorization: `Bearer ${token}` }
    }).then(response => {
      setEditingQuestions(response.data.data || []);
      setShowQuestionModal(true);
    }).catch(err => {
      console.error('Failed to fetch questions:', err);
      setEditingQuestions([]);
      setShowQuestionModal(true);
    });
  };

  const handleAddQuestion = () => {
    const newQuestion = {
      id: 'temp_' + Date.now(),
      text: '',
      option_a: '',
      option_b: '',
      option_c: '',
      option_d: '',
      option_e: '',
      correct_answer: 'A',
      explanation: '',
      subject: '',
      year: null,
      order_index: editingQuestions.length
    };
    setEditingQuestions([...editingQuestions, newQuestion]);
  };

  const handleUpdateQuestion = (index, field, value) => {
    const updatedQuestions = [...editingQuestions];
    updatedQuestions[index] = { ...updatedQuestions[index], [field]: value };
    setEditingQuestions(updatedQuestions);
  };

  const handleRemoveQuestion = (index) => {
    if (window.confirm('Remove this question?')) {
      const updatedQuestions = [...editingQuestions];
      updatedQuestions.splice(index, 1);
      setEditingQuestions(updatedQuestions);
    }
  };

  const handleSaveQuestions = async () => {
    const token = localStorage.getItem('adminToken');
    
    try {
      // Save each question
      for (const question of editingQuestions) {
        const payload = { ...question };
        delete payload.id; // Remove temporary ID
        
        if (question.id && !question.id.toString().startsWith('temp_')) {
          // Update existing question
          await axios.put(`${API_URL}/admin/questions/${question.id}`, payload, {
            headers: { Authorization: `Bearer ${token}` }
          });
        } else {
          // Create new question
          await axios.post(`${API_URL}/admin/quizzes/${selectedQuiz.id}/questions`, payload, {
            headers: { Authorization: `Bearer ${token}` }
          });
        }
      }
      
      setShowQuestionModal(false);
      fetchQuizzes();
    } catch (err) {
      console.error('Failed to save questions:', err);
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'personal': return 'Personal';
      case 'shared': return 'Shared';
      case 'official': return 'Official';
      default: return type;
    }
  };

  if (loading) {
    return <div className="loading">Loading quizzes...</div>;
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h2>Quizzes</h2>
        <button className="btn btn-success" onClick={handleCreateQuiz}>
          Create New Quiz
        </button>
      </div>
      
      <div className="table-container">
        <h3>Manage Quizzes</h3>
        <table>
          <thead>
            <tr>
              <th>Title</th>
              <th>Type</th>
              <th>Subject</th>
              <th>Difficulty</th>
              <th>Time Limit</th>
              <th>Questions</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {quizzes.map((quiz) => (
              <tr key={quiz.id}>
                <td>{quiz.title}</td>
                <td>{getTypeLabel(quiz.type)}</td>
                <td>{quiz.subject || '-'}</td>
                <td>{quiz.difficulty || '-'}</td>
                <td>{quiz.time_limit ? `${quiz.time_limit}s` : '-'}</td>
                <td>{quiz.question_count || 0}</td>
                <td>{new Date(quiz.created_at).toLocaleDateString()}</td>
                <td className="actions">
                  <button className="btn btn-primary" onClick={() => handleEditQuiz(quiz)}>
                    Edit
                  </button>
                  <button className="btn btn-info" onClick={() => handleManageQuestions(quiz)}>
                    Questions
                  </button>
                  <button className="btn btn-danger" onClick={() => handleDeleteQuiz(quiz.id)}>
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Quiz Modal */}
      {showQuizModal && (
        <div className="modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{selectedQuiz.id ? 'Edit Quiz' : 'Create New Quiz'}</h3>
              <button className="close-btn" onClick={() => setShowQuizModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={handleSaveQuiz}>
              <div className="form-group">
                <label>Title *</label>
                <input
                  type="text"
                  value={selectedQuiz.title}
                  onChange={(e) => setSelectedQuiz({...selectedQuiz, title: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={selectedQuiz.description || ''}
                  onChange={(e) => setSelectedQuiz({...selectedQuiz, description: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Type</label>
                  <select
                    value={selectedQuiz.type}
                    onChange={(e) => setSelectedQuiz({...selectedQuiz, type: e.target.value})}
                    required
                  >
                    <option value="personal">Personal</option>
                    <option value="shared">Shared</option>
                    <option value="official">Official</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>Subject</label>
                  <select
                    value={selectedQuiz.subject || ''}
                    onChange={(e) => setSelectedQuiz({...selectedQuiz, subject: e.target.value})}
                  >
                    <option value="">Select subject</option>
                    <option value="Genel Kültür">Genel Kültür</option>
                    <option value="Genel Yetenek">Genel Yetenek</option>
                    <option value="Eğitim Bilimleri">Eğitim Bilimleri</option>
                    <option value="ÖABT">ÖABT</option>
                  </select>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>Difficulty</label>
                  <select
                    value={selectedQuiz.difficulty || ''}
                    onChange={(e) => setSelectedQuiz({...selectedQuiz, difficulty: e.target.value})}
                  >
                    <option value="">Select difficulty</option>
                    <option value="easy">Easy</option>
                    <option value="medium">Medium</option>
                    <option value="hard">Hard</option>
                  </select>
                </div>
                <div className="form-group">
                  <label>Time Limit (seconds)</label>
                  <input
                    type="number"
                    value={selectedQuiz.time_limit || ''}
                    onChange={(e) => setSelectedQuiz({...selectedQuiz, time_limit: parseInt(e.target.value)})}
                    placeholder="Leave empty for no limit"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={selectedQuiz.is_public}
                      onChange={(e) => setSelectedQuiz({...selectedQuiz, is_public: e.target.checked})}
                    />
                    Public
                  </label>
                </div>
                <div className="form-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={selectedQuiz.is_active}
                      onChange={(e) => setSelectedQuiz({...selectedQuiz, is_active: e.target.checked})}
                    />
                    Active
                  </label>
                </div>
              </div>

              <div className="actions">
                <button type="submit" className="btn btn-success">
                  Save Quiz
                </button>
                <button type="button" className="btn btn-danger" onClick={() => setShowQuizModal(false)}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Questions Modal */}
      {showQuestionModal && (
        <div className="modal">
          <div className="modal-content" style={{ maxWidth: '900px' }}>
            <div className="modal-header">
              <h3>Manage Questions - {selectedQuiz?.title}</h3>
              <button className="close-btn" onClick={() => setShowQuestionModal(false)}>
                &times;
              </button>
            </div>
            <div className="questions-manager">
              <div style={{ marginBottom: '20px' }}>
                <button className="btn btn-success" onClick={handleAddQuestion}>
                  Add Question
                </button>
                <button className="btn btn-primary" onClick={handleSaveQuestions} style={{ marginLeft: '10px' }}>
                  Save All Questions
                </button>
              </div>

              <div className="questions-list">
                {editingQuestions.map((question, index) => (
                  <div key={question.id} className="question-item" style={{ 
                    border: '1px solid #ddd', 
                    padding: '15px', 
                    marginBottom: '15px',
                    borderRadius: '5px',
                    backgroundColor: '#f9f9f9'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                      <h4>Question #{index + 1}</h4>
                      <button 
                        className="btn btn-danger" 
                        onClick={() => handleRemoveQuestion(index)}
                        style={{ padding: '5px 10px' }}
                      >
                        Remove
                      </button>
                    </div>

                    <div className="form-group">
                      <label>Question Text *</label>
                      <textarea
                        value={question.text}
                        onChange={(e) => handleUpdateQuestion(index, 'text', e.target.value)}
                        required
                        rows={3}
                        style={{ width: '100%' }}
                      />
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label>Option A *</label>
                        <input
                          type="text"
                          value={question.option_a}
                          onChange={(e) => handleUpdateQuestion(index, 'option_a', e.target.value)}
                          required
                          style={{ width: '100%' }}
                        />
                      </div>
                      <div className="form-group">
                        <label>Option B *</label>
                        <input
                          type="text"
                          value={question.option_b}
                          onChange={(e) => handleUpdateQuestion(index, 'option_b', e.target.value)}
                          required
                          style={{ width: '100%' }}
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label>Option C *</label>
                        <input
                          type="text"
                          value={question.option_c}
                          onChange={(e) => handleUpdateQuestion(index, 'option_c', e.target.value)}
                          required
                          style={{ width: '100%' }}
                        />
                      </div>
                      <div className="form-group">
                        <label>Option D *</label>
                        <input
                          type="text"
                          value={question.option_d}
                          onChange={(e) => handleUpdateQuestion(index, 'option_d', e.target.value)}
                          required
                          style={{ width: '100%' }}
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label>Option E (Optional)</label>
                        <input
                          type="text"
                          value={question.option_e || ''}
                          onChange={(e) => handleUpdateQuestion(index, 'option_e', e.target.value)}
                          style={{ width: '100%' }}
                        />
                      </div>
                      <div className="form-group">
                        <label>Correct Answer *</label>
                        <select
                          value={question.correct_answer}
                          onChange={(e) => handleUpdateQuestion(index, 'correct_answer', e.target.value)}
                          required
                          style={{ width: '100%' }}
                        >
                          <option value="A">A</option>
                          <option value="B">B</option>
                          <option value="C">C</option>
                          <option value="D">D</option>
                          {question.option_e && <option value="E">E</option>}
                        </select>
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label>Explanation</label>
                        <textarea
                          value={question.explanation || ''}
                          onChange={(e) => handleUpdateQuestion(index, 'explanation', e.target.value)}
                          rows={2}
                          style={{ width: '100%' }}
                        />
                      </div>
                      <div className="form-group">
                        <label>Subject</label>
                        <select
                          value={question.subject || ''}
                          onChange={(e) => handleUpdateQuestion(index, 'subject', e.target.value)}
                          style={{ width: '100%' }}
                        >
                          <option value="">Select subject</option>
                          <option value="Genel Kültür">Genel Kültür</option>
                          <option value="Genel Yetenek">Genel Yetenek</option>
                          <option value="Eğitim Bilimleri">Eğitim Bilimleri</option>
                          <option value="ÖABT">ÖABT</option>
                        </select>
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label>Year (Optional)</label>
                        <input
                          type="number"
                          value={question.year || ''}
                          onChange={(e) => handleUpdateQuestion(index, 'year', parseInt(e.target.value))}
                          placeholder="2023"
                          style={{ width: '100%' }}
                        />
                      </div>
                      <div className="form-group">
                        <label>Order</label>
                        <input
                          type="number"
                          value={question.order_index}
                          onChange={(e) => handleUpdateQuestion(index, 'order_index', parseInt(e.target.value))}
                          style={{ width: '100%' }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export { Quizzes };