import {useState, useEffect, useCallback} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {User, AuthTokens, LoginRequest, RegisterRequest} from '../types';
import {authService} from '../services';
import {STORAGE_KEYS} from '../services/api';

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  error: string | null;
  isGuest: boolean;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  loginAsGuest: () => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
  switchToGuest: () => void;
  switchToAuth: () => void;
}

export const useAuth = (): AuthState & AuthActions => {
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    tokens: null,
    isLoading: true,
    error: null,
    isGuest: true, // Start in guest mode
  });

  // Initialize auth state on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setState(prev => ({...prev, isLoading: true}));

      // Check if user data exists in storage
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      const tokens = await authService.getAuthTokens();

      if (userData && tokens) {
        // Validate token
        const isValid = await authService.isAuthenticated();

        if (isValid) {
          const user = JSON.parse(userData);
          setState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            tokens,
            isGuest: false,
            isLoading: false,
          }));
          return;
        } else {
          // Token invalid, clear auth data
          await authService.logout();
        }
      }

      // No valid auth, start in guest mode
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Auth initialization error:', error);
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
        error: 'Kimlik doğrulama başlatılamadı',
      }));
    }
  };

  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      const response = await authService.login(credentials);

      // Store user data
      await AsyncStorage.setItem(
        STORAGE_KEYS.USER_DATA,
        JSON.stringify(response.user),
      );

      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user: response.user,
        tokens: response.tokens,
        isGuest: false,
        isLoading: false,
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.error || 'Giriş yapılamadı',
      }));
      throw error;
    }
  }, []);

  const register = useCallback(async (userData: RegisterRequest) => {
    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      const response = await authService.register(userData);

      // Store user data
      await AsyncStorage.setItem(
        STORAGE_KEYS.USER_DATA,
        JSON.stringify(response.user),
      );

      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user: response.user,
        tokens: response.tokens,
        isGuest: false,
        isLoading: false,
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.error || 'Kayıt olunamadı',
      }));
      throw error;
    }
  }, []);

  const loginAsGuest = useCallback(async () => {
    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      const response = await authService.loginAsGuest();

      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user: response.user,
        tokens: null,
        isGuest: true,
        isLoading: false,
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.error || 'Misafir girişi başarısız',
      }));
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setState(prev => ({...prev, isLoading: true}));

      await authService.logout();
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);

      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local state
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
      }));
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({...prev, error: null}));
  }, []);

  const refreshUser = useCallback(async () => {
    try {
      if (!state.isAuthenticated) {
        return;
      }

      const user = await authService.getCurrentUser();
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));

      setState(prev => ({...prev, user}));
    } catch (error) {
      console.error('Refresh user error:', error);
      // If refresh fails, user might need to re-login
      await logout();
    }
  }, [state.isAuthenticated, logout]);

  const switchToGuest = useCallback(() => {
    setState(prev => ({...prev, isGuest: true}));
  }, []);

  const switchToAuth = useCallback(() => {
    setState(prev => ({...prev, isGuest: false}));
  }, []);

  return {
    ...state,
    login,
    register,
    loginAsGuest,
    logout,
    clearError,
    refreshUser,
    switchToGuest,
    switchToAuth,
  };
};
